import type { ArrowPosition, FloatingPosition } from '../types';
export declare function getArrowPositionStyles({ position, arrowSize, arrowOffset, arrowRadius, arrowPosition, arrowX, arrowY, dir, }: {
    position: FloatingPosition;
    arrowSize: number;
    arrowOffset: number;
    arrowRadius: number;
    arrowPosition: ArrowPosition;
    arrowX: number | undefined;
    arrowY: number | undefined;
    dir: 'rtl' | 'ltr';
}): {};
