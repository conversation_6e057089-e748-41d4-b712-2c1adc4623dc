import React, { useEffect, useRef, useState } from 'react';
import { Card, Group, Text, Switch } from '@mantine/core';
import * as d3 from 'd3';

interface DataPoint {
  time: string;
  value: number;
}

interface D3CrosshairTestProps {
  title: string;
  data: DataPoint[];
  height?: number;
}

const D3CrosshairTest: React.FC<D3CrosshairTestProps> = ({
  title,
  data,
  height = 300,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [enableCrosshair, setEnableCrosshair] = useState(true);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    // Clear previous chart
    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 50 };
    const width = (containerRef.current?.clientWidth || 600) - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom - 60;

    // Process data with index for positioning
    const processedData = data.map((d, i) => ({
      ...d,
      index: i,
    }));

    // Set up scales
    const xScale = d3.scaleLinear()
      .domain([0, processedData.length - 1])
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(processedData, d => d.value) || 100])
      .range([chartHeight, 0]);

    // Create main group
    const g = svg.append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Add axes
    g.append('g')
      .attr('transform', `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale)
        .tickFormat((d) => processedData[d as number]?.time || '')
        .ticks(Math.min(8, processedData.length))
      );

    g.append('g')
      .call(d3.axisLeft(yScale));

    // Add grid lines
    g.append('g')
      .attr('class', 'grid')
      .attr('transform', `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale)
        .tickSize(-chartHeight)
        .tickFormat(() => '')
        .ticks(Math.min(8, processedData.length))
      )
      .style('stroke-dasharray', '2,2')
      .style('opacity', 0.3);

    g.append('g')
      .attr('class', 'grid')
      .call(d3.axisLeft(yScale)
        .tickSize(-width)
        .tickFormat(() => '')
      )
      .style('stroke-dasharray', '2,2')
      .style('opacity', 0.3);

    // Create line
    const line = d3.line<any>()
      .x(d => xScale(d.index))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Add the line
    g.append('path')
      .datum(processedData)
      .attr('fill', 'none')
      .attr('stroke', '#3b82f6')
      .attr('stroke-width', 2)
      .attr('d', line);

    // Add dots
    g.selectAll('.dot')
      .data(processedData)
      .enter().append('circle')
      .attr('class', 'dot')
      .attr('cx', d => xScale(d.index))
      .attr('cy', d => yScale(d.value))
      .attr('r', 4)
      .attr('fill', '#3b82f6')
      .attr('stroke', '#ffffff')
      .attr('stroke-width', 2);

    // Create crosshair elements (always create them)
    const crosshair = g.append('g')
      .attr('class', 'crosshair')
      .style('display', 'none');

    const verticalLine = crosshair.append('line')
      .attr('class', 'crosshair-vertical')
      .attr('y1', 0)
      .attr('y2', chartHeight)
      .style('stroke', '#ff0000')
      .style('stroke-width', 1)
      .style('stroke-dasharray', '3,3')
      .style('pointer-events', 'none');

    const horizontalLine = crosshair.append('line')
      .attr('class', 'crosshair-horizontal')
      .attr('x1', 0)
      .attr('x2', width)
      .style('stroke', '#ff0000')
      .style('stroke-width', 1)
      .style('stroke-dasharray', '3,3')
      .style('pointer-events', 'none');

    // Create tooltip
    const tooltip = d3.select('body').append('div')
      .attr('class', 'd3-crosshair-tooltip')
      .style('position', 'absolute')
      .style('visibility', 'hidden')
      .style('background', 'rgba(0, 0, 0, 0.8)')
      .style('color', 'white')
      .style('padding', '8px')
      .style('border-radius', '4px')
      .style('font-size', '12px')
      .style('pointer-events', 'none')
      .style('z-index', 1000);

    // Create invisible overlay for mouse events
    const overlay = g.append('rect')
      .attr('class', 'overlay')
      .attr('width', width)
      .attr('height', chartHeight)
      .style('fill', 'none')
      .style('pointer-events', 'all')
      .style('cursor', 'crosshair');

    // Mouse move handler
    overlay.on('mousemove', function(event) {
      if (!enableCrosshair) {
        crosshair.style('display', 'none');
        tooltip.style('visibility', 'hidden');
        return;
      }

      const [mouseX, mouseY] = d3.pointer(event);
      
      // Find closest data point
      const x0 = xScale.invert(mouseX);
      const i = Math.round(Math.max(0, Math.min(x0, processedData.length - 1)));
      const d = processedData[i];

      if (d) {
        const x = xScale(d.index);
        const y = yScale(d.value);

        // Show crosshair
        crosshair.style('display', null);
        
        // Update crosshair position
        verticalLine
          .attr('x1', x)
          .attr('x2', x);
        
        horizontalLine
          .attr('y1', y)
          .attr('y2', y);

        // Show tooltip
        tooltip
          .style('visibility', 'visible')
          .html(`
            <div><strong>Time:</strong> ${d.time}</div>
            <div><strong>Value:</strong> ${d.value.toFixed(2)}</div>
            <div><strong>Index:</strong> ${i}</div>
          `)
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 10) + 'px');
      }
    });

    // Mouse leave handler
    overlay.on('mouseleave', function() {
      crosshair.style('display', 'none');
      tooltip.style('visibility', 'hidden');
    });

    // Cleanup function
    return () => {
      tooltip.remove();
    };

  }, [data, height, enableCrosshair]); // Include enableCrosshair in dependencies

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <div ref={containerRef} style={{ height: '100%' }}>
        <Group justify="space-between" mb="md">
          <Text size="lg" fw={600}>
            {title}
          </Text>
          <Switch
            label="Enable Crosshair"
            checked={enableCrosshair}
            onChange={(event) => setEnableCrosshair(event.currentTarget.checked)}
            size="sm"
            color="red"
          />
        </Group>

        <svg
          ref={svgRef}
          width="100%"
          height={height - 60}
          style={{ overflow: 'visible' }}
        />
        
        <Text size="xs" c="dimmed" mt="xs">
          <strong>Crosshair Test:</strong> Hover over the chart to see red crosshair lines. Toggle switch to enable/disable.
        </Text>
      </div>
    </Card>
  );
};

export default D3CrosshairTest;
