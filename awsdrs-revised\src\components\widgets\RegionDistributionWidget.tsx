import React, { useRef } from 'react';
import { Card, Text, Group, Badge, Stack } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { fetchRegionDistribution } from '../../services/api';
import { WidgetProps } from '../../types/dashboard';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip as ChartTooltip,
  Legend,
} from 'chart.js';
import { Pie } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  ArcElement,
  ChartTooltip,
  Legend
);

const COLORS = [
  '#3b82f6', // Blue
  '#10b981', // Green
  '#f59e0b', // Yellow
  '#ef4444', // Red
  '#8b5cf6'  // Purple
];

const RegionDistributionWidget: React.FC<WidgetProps> = ({ widget }) => {
  const chartRef = useRef<ChartJS<'pie'>>(null);

  const { data: regionData, isLoading } = useQuery({
    queryKey: ['regionDistribution'],
    queryFn: fetchRegionDistribution,
    refetchInterval: 60000, // Refresh every minute
  });

  if (isLoading) {
    return (
      <Card h="100%" p="md">
        <Text c="dimmed" ta="center">Loading...</Text>
      </Card>
    );
  }

  const rawData = regionData?.map((region, index) => ({
    ...region,
    color: COLORS[index % COLORS.length]
  })) || [];

  const total = rawData.reduce((sum, item) => sum + item.count, 0);

  // Prepare data for Chart.js
  const chartData = {
    labels: rawData.map(item => item.region),
    datasets: [
      {
        data: rawData.map(item => item.count),
        backgroundColor: rawData.map(item => item.color),
        borderColor: '#ffffff',
        borderWidth: 2,
        hoverBorderWidth: 3,
        hoverBackgroundColor: rawData.map(item => item.color),
        hoverBorderColor: '#ffffff',
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'bottom' as const,
        labels: {
          usePointStyle: true,
          padding: 15,
          font: {
            size: 11,
          },
          generateLabels: function(chart: any) {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              return data.labels.map((label: string, i: number) => {
                const value = data.datasets[0].data[i];
                const percentage = ((value / total) * 100).toFixed(1);
                return {
                  text: `${label}: ${value} (${percentage}%)`,
                  fillStyle: data.datasets[0].backgroundColor[i],
                  strokeStyle: data.datasets[0].backgroundColor[i],
                  lineWidth: 0,
                  pointStyle: 'circle',
                  hidden: false,
                  index: i
                };
              });
            }
            return [];
          }
        },
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed;
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${value} servers (${percentage}%)`;
          }
        }
      },
    },
  };

  return (
    <Card h="100%" p="md">
      <Group justify="space-between" mb="md">
        <Text fw={600} size="sm">{widget.title}</Text>
        <Badge color="blue" variant="light" size="xs">Live</Badge>
      </Group>

      <div style={{ height: 'calc(100% - 40px)' }}>
        <Pie ref={chartRef} data={chartData} options={options} />
      </div>
    </Card>
  );
};

export default RegionDistributionWidget;
