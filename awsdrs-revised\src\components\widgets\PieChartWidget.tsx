import React, { useState, useRef } from 'react';
import { Card, Text, Group, ActionIcon, Tooltip } from '@mantine/core';
import { IconRefresh, IconDownload } from '@tabler/icons-react';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip as ChartTooltip,
  Legend,
} from 'chart.js';
import { Pie } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  ArcElement,
  ChartTooltip,
  Legend
);

interface PieChartWidgetProps {
  title: string;
  data: any[];
  colors: string[];
  height?: number;
  onRefresh?: () => void;
  onExport?: () => void;
  withBorder?: boolean;
  withShadow?: boolean;
  minimal?: boolean;
}

const PieChartWidget = ({
  title,
  data,
  colors,
  height = 300,
  onRefresh,
  onExport,
  withBorder = true,
  withShadow = true,
  minimal = false
}: PieChartWidgetProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const chartRef = useRef<ChartJS<'pie'>>(null);

  // Consistent tooltip styling to match header row tooltips
  const tooltipStyles = {
    tooltip: {
      fontSize: '11px',
      padding: '4px 8px',
      fontWeight: 400
    }
  };

  // Prepare data for Chart.js
  const chartData = {
    labels: data.map(d => d.name || d.label),
    datasets: [
      {
        data: data.map(d => d.value),
        backgroundColor: colors,
        borderColor: colors.map(color => color),
        borderWidth: 2,
        hoverBorderWidth: 3,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed;
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      },
    },
  };

  if (minimal) {
    return (
      <div style={{ height: '100%', padding: '16px' }}>
        <Group justify="space-between" mb="md">
          <Text size="lg" fw={600}>
            {title}
          </Text>
          <Group gap="xs">
            {onRefresh && (
              <Tooltip label="Refresh chart data" styles={tooltipStyles}>
                <ActionIcon variant="light" size="sm" onClick={onRefresh}>
                  <IconRefresh size={16} />
                </ActionIcon>
              </Tooltip>
            )}
            {onExport && (
              <Tooltip label="Export chart data" styles={tooltipStyles}>
                <ActionIcon variant="light" size="sm" onClick={onExport}>
                  <IconDownload size={16} />
                </ActionIcon>
              </Tooltip>
            )}
          </Group>
        </Group>

        <div style={{ height: `${height}px`, width: '100%' }}>
          <Pie ref={chartRef} data={chartData} options={options} />
        </div>
      </div>
    );
  }

  return (
    <Card shadow={withShadow ? "sm" : "none"} padding="lg" radius="md" withBorder={withBorder}>
      <Group justify="space-between" mb="md">
        <Text size="lg" fw={600}>
          {title}
        </Text>
        <Group gap="xs">
          {onRefresh && (
            <Tooltip label="Refresh chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onRefresh}>
                <IconRefresh size={16} />
              </ActionIcon>
            </Tooltip>
          )}
          {onExport && (
            <Tooltip label="Export chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onExport}>
                <IconDownload size={16} />
              </ActionIcon>
            </Tooltip>
          )}
        </Group>
      </Group>

      <div style={{ height: `${height}px`, width: '100%' }}>
        <Pie ref={chartRef} data={chartData} options={options} />
      </div>
    </Card>
  );
};

export default PieChartWidget;
