<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- $Id$-->
<limits>
<tables>
    <!-- dnyfactor is no.of task run within their data storage period. e.g:Hr- (2*24*60)/7 = 411  -->
<table name="User_Vs_Bandwidth_Hr" minlimit="400" maxlimit="2000" dynfactor="411" />
<table name="External_User_Vs_Bandwidth_Hr" limit="100" />
<table name="External_User_Vs_Bandwidth_Dy" limit="200" />
<table name="External_User_Vs_Bandwidth_Mo" limit="200" />
<table name="External_User_Vs_Bandwidth_Year" limit="200" />
<table name="Dest_Vs_Bandwidth_Hr" minlimit="400" maxlimit="2000" dynfactor="411" />
<table name="External_Dest_Vs_Bandwidth_Hr" limit="100" />
<table name="External_Dest_Vs_Bandwidth_Dy" limit="200" />
<table name="External_Dest_Vs_Bandwidth_Mo" limit="200" />
<table name="External_Dest_Vs_Bandwidth_Year" limit="200" />
<table name="Req_Vs_Cli_Hr" minlimit="500" maxlimit="2000" dynfactor="411" />
<table name="Req_Vs_Cli_Dy" minlimit="400" maxlimit="2000" dynfactor="42" />
<table name="Req_Vs_Cli_Mo" minlimit="400" maxlimit="2000" dynfactor="180" />
<table name="Req_Vs_Cli_Year" minlimit="400" maxlimit="2000" dynfactor="365" />
<table name="User_Vs_Protocol_Hr" minlimit="500" maxlimit="2000" dynfactor="411" />
<table name="User_Vs_Protocol_Dy" minlimit="400" maxlimit="2000" dynfactor="42" />
<table name="User_Vs_Protocol_Mo" minlimit="400" maxlimit="2000" dynfactor="180" />
<table name="User_Vs_Protocol_Year" minlimit="400" maxlimit="2000" dynfactor="365" />
<table name="Rules_Vs_Pro_Hr" minlimit="500" maxlimit="2000" dynfactor="411" />
<table name="Rules_Vs_Pro_Dy" minlimit="400" maxlimit="2000" dynfactor="42" />
<table name="Rules_Vs_Pro_Mo" minlimit="400" maxlimit="2000" dynfactor="180" />
<table name="Rules_Vs_Pro_Year" minlimit="400" maxlimit="2000" dynfactor="365" />
<table name="Evt_Vs_Cli_Tablet" limit="400" />
<table name="VPN_Evt_Vs_Pro_Tablet" limit="400" />
<table name="Rules_Vs_Pro_Tablet" limit="400" />
<table name="User_Vs_Protocol_Tablet" limit="400" />
</tables>
</limits>
