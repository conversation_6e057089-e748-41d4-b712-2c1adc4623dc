import { useState, useRef, useEffect } from 'react'

interface Position {
  x: number
  y: number
}

interface DragRef {
  startX: number
  startY: number
  isDragging: boolean
}

export function useDraggableMenu() {
  const [showMenu, setShowMenu] = useState(false)
  const [menuPosition, setMenuPosition] = useState<Position>({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const dragRef = useRef<DragRef>({ startX: 0, startY: 0, isDragging: false })

  const handleMouseDown = (e: React.MouseEvent) => {
    if (menuRef.current && e.target instanceof Element && e.target.closest('.menu-handle')) {
      dragRef.current = {
        startX: e.clientX - menuPosition.x,
        startY: e.clientY - menuPosition.y,
        isDragging: true
      }
      setIsDragging(true)
    }
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (dragRef.current.isDragging) {
      setMenuPosition({
        x: e.clientX - dragRef.current.startX,
        y: e.clientY - dragRef.current.startY
      })
    }
  }

  const handleMouseUp = () => {
    dragRef.current.isDragging = false
    setIsDragging(false)
  }

  useEffect(() => {
    if (showMenu) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [showMenu])

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(e.target as Node) && !isDragging) {
        setShowMenu(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isDragging])

  return {
    showMenu,
    setShowMenu,
    menuPosition,
    setMenuPosition,
    isDragging,
    menuRef,
    handleMouseDown
  }
}