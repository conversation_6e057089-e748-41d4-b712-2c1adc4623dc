import { useState, useEffect, useCallback } from 'react';
import type { Layout } from 'react-grid-layout';

interface LayoutPreset {
  id: string;
  name: string;
  layouts: { [key: string]: Layout[] };
}

interface UseLayoutManagerProps {
  pageKey: string; // Unique key for the page (e.g., 'dashboard', 'metrics', 'analytics')
  defaultLayouts: { [key: string]: Layout[] };
  defaultLayoutName?: string;
}

interface UseLayoutManagerReturn {
  // Current state
  layouts: { [key: string]: Layout[] };
  layoutPresets: LayoutPreset[];
  currentLayoutId: string;
  
  // Layout management
  setLayouts: (layouts: { [key: string]: Layout[] }) => void;
  switchToLayout: (layoutId: string) => void;
  saveCurrentLayout: (name: string) => void;
  deleteLayout: (layoutId: string) => void;
  resetToDefault: () => void;
  
  // Event handlers
  handleLayoutChange: (layout: Layout[], layouts: { [key: string]: Layout[] }) => void;
}

export const useLayoutManager = ({
  pageKey,
  defaultLayouts,
  defaultLayoutName = 'Default'
}: UseLayoutManagerProps): UseLayoutManagerReturn => {
  const [layouts, setLayoutsState] = useState(defaultLayouts);
  const [layoutPresets, setLayoutPresets] = useState<LayoutPreset[]>([]);
  const [currentLayoutId, setCurrentLayoutId] = useState('default');

  const storageKey = `${pageKey}-layout-presets`;
  const currentLayoutKey = `${pageKey}-current-layout`;

  // Load saved layouts on mount
  useEffect(() => {
    try {
      const savedPresets = localStorage.getItem(storageKey);
      const savedCurrentLayout = localStorage.getItem(currentLayoutKey);
      
      if (savedPresets) {
        const presets: LayoutPreset[] = JSON.parse(savedPresets);
        
        // Always ensure default layout exists
        const defaultPreset: LayoutPreset = {
          id: 'default',
          name: defaultLayoutName,
          layouts: defaultLayouts
        };
        
        const hasDefault = presets.some(preset => preset.id === 'default');
        const finalPresets = hasDefault 
          ? presets.map(preset => preset.id === 'default' ? defaultPreset : preset)
          : [defaultPreset, ...presets];
        
        setLayoutPresets(finalPresets);
        
        // Set current layout
        const currentId = savedCurrentLayout || 'default';
        const currentPreset = finalPresets.find(preset => preset.id === currentId);
        
        if (currentPreset) {
          setCurrentLayoutId(currentId);
          setLayoutsState(currentPreset.layouts);
        } else {
          setCurrentLayoutId('default');
          setLayoutsState(defaultLayouts);
        }
      } else {
        // First time - create default preset
        const defaultPreset: LayoutPreset = {
          id: 'default',
          name: defaultLayoutName,
          layouts: defaultLayouts
        };
        setLayoutPresets([defaultPreset]);
        setCurrentLayoutId('default');
        setLayoutsState(defaultLayouts);
      }
    } catch (error) {
      console.error('Error loading layout presets:', error);
      // Fallback to default
      const defaultPreset: LayoutPreset = {
        id: 'default',
        name: defaultLayoutName,
        layouts: defaultLayouts
      };
      setLayoutPresets([defaultPreset]);
      setCurrentLayoutId('default');
      setLayoutsState(defaultLayouts);
    }
  }, [pageKey, defaultLayouts, defaultLayoutName, storageKey, currentLayoutKey]);

  // Save presets to localStorage
  const savePresets = useCallback((presets: LayoutPreset[]) => {
    try {
      localStorage.setItem(storageKey, JSON.stringify(presets));
    } catch (error) {
      console.error('Error saving layout presets:', error);
    }
  }, [storageKey]);

  // Save current layout ID
  const saveCurrentLayoutId = useCallback((layoutId: string) => {
    try {
      localStorage.setItem(currentLayoutKey, layoutId);
    } catch (error) {
      console.error('Error saving current layout ID:', error);
    }
  }, [currentLayoutKey]);

  // Set layouts and update current preset
  const setLayouts = useCallback((newLayouts: { [key: string]: Layout[] }) => {
    setLayoutsState(newLayouts);
    
    // Update the current preset with new layouts
    setLayoutPresets(prevPresets => {
      const updatedPresets = prevPresets.map(preset => 
        preset.id === currentLayoutId 
          ? { ...preset, layouts: newLayouts }
          : preset
      );
      savePresets(updatedPresets);
      return updatedPresets;
    });
  }, [currentLayoutId, savePresets]);

  // Switch to a different layout
  const switchToLayout = useCallback((layoutId: string) => {
    const preset = layoutPresets.find(p => p.id === layoutId);
    if (preset) {
      setCurrentLayoutId(layoutId);
      setLayoutsState(preset.layouts);
      saveCurrentLayoutId(layoutId);
    }
  }, [layoutPresets, saveCurrentLayoutId]);

  // Save current layout as new preset
  const saveCurrentLayout = useCallback((name: string) => {
    const newId = `layout-${Date.now()}`;
    const newPreset: LayoutPreset = {
      id: newId,
      name,
      layouts: { ...layouts }
    };

    setLayoutPresets(prevPresets => {
      const updatedPresets = [...prevPresets, newPreset];
      savePresets(updatedPresets);
      return updatedPresets;
    });

    // Switch to the new layout
    setCurrentLayoutId(newId);
    saveCurrentLayoutId(newId);
  }, [layouts, savePresets, saveCurrentLayoutId]);

  // Delete a layout preset
  const deleteLayout = useCallback((layoutId: string) => {
    if (layoutId === 'default') return; // Can't delete default

    setLayoutPresets(prevPresets => {
      const updatedPresets = prevPresets.filter(preset => preset.id !== layoutId);
      savePresets(updatedPresets);
      return updatedPresets;
    });

    // If deleting current layout, switch to default
    if (currentLayoutId === layoutId) {
      setCurrentLayoutId('default');
      setLayoutsState(defaultLayouts);
      saveCurrentLayoutId('default');
    }
  }, [currentLayoutId, defaultLayouts, savePresets, saveCurrentLayoutId]);

  // Reset to default layout
  const resetToDefault = useCallback(() => {
    setCurrentLayoutId('default');
    setLayoutsState(defaultLayouts);
    saveCurrentLayoutId('default');
    
    // Update default preset
    setLayoutPresets(prevPresets => {
      const updatedPresets = prevPresets.map(preset => 
        preset.id === 'default' 
          ? { ...preset, layouts: defaultLayouts }
          : preset
      );
      savePresets(updatedPresets);
      return updatedPresets;
    });
  }, [defaultLayouts, savePresets, saveCurrentLayoutId]);

  // Handle layout changes from React Grid Layout
  const handleLayoutChange = useCallback((layout: Layout[], newLayouts: { [key: string]: Layout[] }) => {
    setLayouts(newLayouts);
  }, [setLayouts]);

  return {
    layouts,
    layoutPresets,
    currentLayoutId,
    setLayouts,
    switchToLayout,
    saveCurrentLayout,
    deleteLayout,
    resetToDefault,
    handleLayoutChange
  };
};
