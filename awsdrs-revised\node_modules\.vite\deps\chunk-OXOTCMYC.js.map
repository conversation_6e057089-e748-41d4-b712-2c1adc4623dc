{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconLibraryFilled.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('filled', 'library-filled', 'IconLibraryFilled', [[\"path\",{\"d\":\"M18.333 2a3.667 3.667 0 0 1 3.667 3.667v8.666a3.667 3.667 0 0 1 -3.667 3.667h-8.666a3.667 3.667 0 0 1 -3.667 -3.667v-8.666a3.667 3.667 0 0 1 3.667 -3.667zm-4.333 10h-3a1 1 0 0 0 0 2h3a1 1 0 0 0 0 -2m3 -3h-6a1 1 0 0 0 0 2h6a1 1 0 0 0 0 -2m-1 -3h-5a1 1 0 0 0 0 2h5a1 1 0 0 0 0 -2\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M3.517 6.391a1 1 0 0 1 .99 1.738c-.313 .178 -.506 .51 -.507 .868v10c0 .548 .452 1 1 1h10c.284 0 .405 -.088 .626 -.486a1 1 0 0 1 1.748 .972c-.546 .98 -1.28 1.514 -2.374 1.514h-10c-1.652 0 -3 -1.348 -3 -3v-10.002a3 3 0 0 1 1.517 -2.605\",\"key\":\"svg-1\"}]]);"], "mappings": ";;;;;AACA,IAAA,oBAAe,qBAAqB,UAAU,kBAAkB,qBAAqB,CAAC,CAAC,QAAO,EAAC,KAAI,yRAAwR,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,6OAA4O,OAAM,QAAQ,CAAA,CAAC,CAAC;", "names": []}