<?xml version="1.0" encoding="ISO-8859-1"?>
<properties>
<property name="ProductName" value="AdventNet ManageEngine Firewall Analyzer"/>
<property name="SubProductName" value="FIREWALL"/>
<!--Specify this when you release major version-->
<property name="ProductVersion" value="8.0.0"/>
<property name="HelpXmlFilePath" value="help/updatehelp.xml"/>
<property name="HelpHtmlFilePath" value="help/updatehelp.html"/>
<!--Specify the Font here -->
<property name="Font" value="Arial"/>
<!--Specify the Language here -->
<property name="Language" value="en"/>
<!--Specify the Country here -->
<property name="Country" value="US"/>
<!--Specify the properties file here -->
<property name="PropertiesFileName" value="resources/UpdateManagerResources"/>
<!--Specify true to enable deployment tool -->
<property name="EnableDeploymentTool" value="false"/>
<property name="EnableUninstalltion" value="true"/>
<Log>
							<!-- Implementaion class of UpdateManagerLogInterface -->
							<Class>com.adventnet.tools.update.installer.log.UpdateManagerLogImpl</Class>
							<property name="FileName" value="updatemgrlog%g.txt"/>
							<property name="MaxLines" value="1000"/>
							<property name="FileCount" value="30"/>
							<property name="MaxLinesCached" value="0"/>
							<!-- directory in which the log files should be placed -->
							<property name="LogsDirectory" value="server/default/log"/>
							<property name="UseTimeStamp" value="false"/>
							<property name="Level" value="INFO"/>
							<property name="FormtterClass" value="com.adventnet.tools.update.installer.log.LogFormatter"/>
							<property name="MaxLimit" value="10000000"/>
						</Log>
</properties>
<!-- $Id$ -->
