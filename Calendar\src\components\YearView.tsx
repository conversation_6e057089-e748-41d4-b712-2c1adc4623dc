import { format, addMonths, startOfYear, getDaysInMonth, startOfMonth, addDays } from 'date-fns'
import { Paper, SimpleGrid, Text, Group, Stack } from '@mantine/core'

interface YearViewProps {
  currentDate: Date
  selectedDate: Date | null
  onDayClick: (day: Date) => void
  onViewChange: (view: 'day' | 'week' | 'month' | 'year') => void
}

export function YearView({ currentDate, selectedDate, onDayClick, onViewChange }: YearViewProps) {
  const yearStart = startOfYear(currentDate)
  const months = Array.from({ length: 12 }, (_, i) => addMonths(yearStart, i))
  const weekDays = [
    { key: 'sun', label: 'S' },
    { key: 'mon', label: 'M' },
    { key: 'tue', label: 'T' },
    { key: 'wed', label: 'W' },
    { key: 'thu', label: 'T' },
    { key: 'fri', label: 'F' },
    { key: 'sat', label: 'S' },
  ]

  const getMonthDays = (month: Date) => {
    const daysInMonth = getDaysInMonth(month)
    const firstDay = startOfMonth(month)
    return Array.from({ length: daysInMonth }, (_, i) => addDays(firstDay, i))
  }

  const handleDayClick = (day: Date) => {
    onDayClick(day)
    onViewChange('week')
  }

  return (
    <Paper className="flex-1 overflow-auto" p="md">
      <SimpleGrid cols={3} spacing="md">
        {months.map((month) => {
          const monthDays = getMonthDays(month)
          
          return (
            <Paper
              key={month.toString()}
              p="md"
              withBorder
              shadow="xs"
              className="hover:shadow-lg transition-all duration-200 cursor-pointer"
            >
              <Text size="lg" fw={600} mb="md">
                {format(month, 'MMMM')}
              </Text>
              <SimpleGrid cols={7} spacing="xs">
                {weekDays.map((day) => (
                  <Text key={day.key} size="xs" fw={500} c="dimmed" ta="center">
                    {day.label}
                  </Text>
                ))}
                {Array.from({ length: monthDays[0].getDay() }, (_, i) => (
                  <div key={`empty-${i}`} />
                ))}
                {monthDays.map((day) => (
                  <Paper
                    key={day.toString()}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDayClick(day)
                    }}
                    p="xs"
                    style={{
                      cursor: 'pointer',
                      borderRadius: '50%',
                      backgroundColor: selectedDate?.toDateString() === day.toDateString() 
                        ? 'var(--primary-color)' 
                        : 'transparent',
                      color: selectedDate?.toDateString() === day.toDateString() 
                        ? 'white' 
                        : undefined,
                      minHeight: '32px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                    className="hover:bg-blue-50 hover:scale-110 transition-all duration-200"
                  >
                    <Text size="xs">
                      {format(day, 'd')}
                    </Text>
                  </Paper>
                ))}
              </SimpleGrid>
            </Paper>
          )
        })}
      </SimpleGrid>
    </Paper>
  )
}