{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.0.0", "@heroicons/react": "^2.1.1", "@mantine/core": "^8.1.3", "@mantine/dates": "^8.1.3", "@mantine/hooks": "^8.1.3", "@mantine/notifications": "^8.1.3", "@mantine/spotlight": "^8.1.3", "date-fns": "^3.0.6", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-dom": "^19.0.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.16", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "~5.7.2", "typescript-eslint": "^8.25.0", "vite": "^6.2.0"}}