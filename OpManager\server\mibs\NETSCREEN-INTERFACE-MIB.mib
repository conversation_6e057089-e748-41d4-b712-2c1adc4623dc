-- This module defines enterprise MIBs for interface configuration
-- 
-- Copyright (c) 1999-2004, Juniper Networks, Inc.
-- All rights reserved.

NETSCREEN-INTERFACE-MIB DEFINITIONS ::= BEGIN

IMPORTS
    netscreenInterface
        FROM NETSCREEN-<PERSON>I
    Counter32, <PERSON><PERSON><PERSON>32, <PERSON><PERSON><PERSON><PERSON><PERSON>, MODULE-IDENTITY, OB<PERSON>ECT-TYPE
        FROM SNMPv2-SM<PERSON>
    DisplayString,PhysAddress
        FROM SNMPv2-TC
    ;

netscreenInterfaceMibModule MODULE-IDENTITY
    LAST-UPDATED  "200405032022Z" -- May 03, 2004
    ORGANIZATION
        "Juniper Networks, Inc."
    CONTACT-INFO
        "Customer Support

         1194 North Mathilda Avenue 
         Sunnyvale, California 94089-1206
         USA

         Tel: **************
         E-mail: <EMAIL>
         HTTP://www.juniper.net"
    DESCRIPTION
        "This module defines the object that are used to monitor NS's
         interface configuration"
    REVISION      "200405030000Z" -- May 03, 2004
    DESCRIPTION
        "Modified copyright and contact information"
    REVISION      "200403030000Z" -- March 03, 2004
    DESCRIPTION
        "Converted to SMIv2 by Longview Software"
    REVISION      "200109280000Z" -- September 28, 2001
    DESCRIPTION
        "No Comment"
    REVISION      "200105110000Z" -- May 11, 2001
    DESCRIPTION
        "Creation Date"
    ::= { netscreenInterface 0 }

NsIfEntry ::= SEQUENCE
{
    nsIfIndex         Integer32,
    nsIfName          DisplayString,
    nsIfVsys          Integer32,
    nsIfZone          Integer32,
    nsIfStatus        INTEGER,
    nsIfIp            IpAddress,
    nsIfNetmask       IpAddress,
    nsIfGateway       IpAddress,
    nsIfMngIp         IpAddress,
    nsIfMode          INTEGER,
    nsIfMAC           PhysAddress,
    nsIfMngTelnet     INTEGER,
    nsIfMngSCS        INTEGER,
    nsIfMngWEB        INTEGER,
    nsIfMngSSL        INTEGER,
    nsIfMngSNMP       INTEGER,
    nsIfMngGlobal     INTEGER,
    nsIfMngGlobalPro  INTEGER,
    nsIfMngPing       INTEGER,
    nsIfMngIdentReset INTEGER,
    nsIfInfo          Integer32,
    nsIfDescr         DisplayString
}

NsIfFlowEntry ::= SEQUENCE
{
    nsIfFlowIfIdx     Integer32,
    nsIfFlowVsys      Integer32,
    nsIfFlowInByte    Counter32,
    nsIfFlowInPacket  Counter32,
    nsIfFlowOutByte   Counter32,
    nsIfFlowOutPacket Counter32,
    nsIfFlowInVpn     Counter32,
    nsIfInVlan        Counter32,
    nsIfOutVlan       Counter32,
    nsIfFlowIfInfo    Integer32
}

NsIfMonEntry ::= SEQUENCE
{
    nsIfMonIfIdx        Integer32,
    nsIfMonVsys         Integer32,
    nsIfMonPlyDeny      Counter32,
    nsIfMonAuthFail     Counter32,
    nsIfMonUrlBlock     Counter32,
    nsIfMonTrMngQueue   Counter32,
    nsIfMonTrMngDrop    Counter32,
    nsIfMonEncFail      Counter32,
    nsIfMonNoSa         Counter32,
    nsIfMonNoSaPly      Counter32,
    nsIfMonSaInactive   Counter32,
    nsIfMonSaPolicyDeny Counter32,
    nsIfMonIfInfo       Integer32
}

NsIfSecondaryIpEntry ::= SEQUENCE
{
    nsIfSecondaryIpIndex   Integer32,
    nsIfSecondaryIpIfIdx   Integer32,
    nsIfSecondaryIpVsys    Integer32,
    nsIfSecondaryIpZone    Integer32,
    nsIfSecondaryIpAddress IpAddress,
    nsIfSecondaryIpNetmask IpAddress,
    nsIfSecondaryIpIfInfo  Integer32
}

nsIfTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF NsIfEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "All NetScreen devices have a trusted interface and an
         untrusted interface. The NetScreen-10 and -100 also have a DMZ
         interface. Additionally, on each of the  virtual systems
         supported by the NetScreen-1000 there can be on or more tursted
         subinterfaces linking a particular virtual system to one or
         more virtual LANs. Other interfaces-some physical, some
         logical, and some virtual-provide exclusive  channels for
         administrative traffic, or for communication among member in a
         redundant group. In this table, it will collect following
         interface: tursted interface, untrusted  interface, DMZ
         interface, Management Interface and Subinterface."
    ::= { netscreenInterface 1 }

nsIfEntry OBJECT-TYPE
    SYNTAX        NsIfEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "nsIfEntry collects some attributes about NetScreen interface
         settings."
    INDEX
        { nsIfIndex }
    ::= { nsIfTable 1 }

nsIfIndex OBJECT-TYPE
    SYNTAX        Integer32 (0..2147483647)
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Interface number which uniquely identifies an interface."
    ::= { nsIfEntry 1 }

nsIfName OBJECT-TYPE
    SYNTAX        DisplayString (SIZE(0..32))
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Each interface has a readable name such as 'trust', 'trust/1',
         etc."
    ::= { nsIfEntry 2 }

nsIfVsys OBJECT-TYPE
    SYNTAX        Integer32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "According NetScreen's concepts, each interface belongs to one
         virtual system. This attribute displays the virtual system name
         an interface belongs to. If the device is device which doesn't
         support vsys such as  NS-100, we can logically think all the
         setting belongs to 'root' vsys."
    ::= { nsIfEntry 3 }

nsIfZone OBJECT-TYPE
    SYNTAX        Integer32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Each interface belongs to one security zone. This attribute
         displays the security zone name an interface belongs to."
    ::= { nsIfEntry 4 }

nsIfStatus OBJECT-TYPE
    SYNTAX        INTEGER {
        down(0),
        up(1),
        ready(2),
        inactive(3)
    }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The interface status which indicates interface's operational
         status."
    ::= { nsIfEntry 5 }

nsIfIp OBJECT-TYPE
    SYNTAX        IpAddress
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Each interface must be assigned an IP address."
    ::= { nsIfEntry 6 }

nsIfNetmask OBJECT-TYPE
    SYNTAX        IpAddress
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Each interface must belong to a subnet represented by
         netmask."
    ::= { nsIfEntry 7 }

nsIfGateway OBJECT-TYPE
    SYNTAX        IpAddress
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Default gateway ip address."
    ::= { nsIfEntry 8 }

nsIfMngIp OBJECT-TYPE
    SYNTAX        IpAddress
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Trusted, untrusted and DMZ interfaces can have two IP address:
         an interface IP address that corresponds to the physical port
         through which that  interface connects to a network, and a
         Manage IP address that can be used to  receive administrative
         traffic"
    ::= { nsIfEntry 9 }

nsIfMode OBJECT-TYPE
    SYNTAX        INTEGER {
        transparent(0),
        nat(1),
        route(2),
        not-applicable(3)
    }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "NetScreen interface can has three operation modes. They are
         transparent, nat and route."
    ::= { nsIfEntry 10 }

nsIfMAC OBJECT-TYPE
    SYNTAX        PhysAddress
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "MAC address the interface has."
    ::= { nsIfEntry 11 }

nsIfMngTelnet OBJECT-TYPE
    SYNTAX        INTEGER {
        disable(0),
        enable(1)
    }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This attribute is used to indicate whether the interface
         permits telnet  management."
    ::= { nsIfEntry 12 }

nsIfMngSCS OBJECT-TYPE
    SYNTAX        INTEGER {
        disable(0),
        enable(1)
    }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This attribute is used to indicate whether the interface
         permits Secure Command Shell management."
    ::= { nsIfEntry 13 }

nsIfMngWEB OBJECT-TYPE
    SYNTAX        INTEGER {
        disable(0),
        enable(1)
    }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This attribute is used to indicate whether the interface
         permits WEB UI  management."
    ::= { nsIfEntry 14 }

nsIfMngSSL OBJECT-TYPE
    SYNTAX        INTEGER {
        disable(0),
        enable(1)
    }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This attribute is used to indicate whether the interface
         permits SSL  management."
    ::= { nsIfEntry 15 }

nsIfMngSNMP OBJECT-TYPE
    SYNTAX        INTEGER {
        disable(0),
        enable(1)
    }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This attribute is used to indicate whether the interface
         permits SNMP  management."
    ::= { nsIfEntry 16 }

nsIfMngGlobal OBJECT-TYPE
    SYNTAX        INTEGER {
        disable(0),
        enable(1)
    }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This attribute is used to indicate whether the interface
         permits NS Global  management."
    ::= { nsIfEntry 17 }

nsIfMngGlobalPro OBJECT-TYPE
    SYNTAX        INTEGER {
        disable(0),
        enable(1)
    }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This attribute is used to indicate whether the interface
         permits NS Global Pro management."
    ::= { nsIfEntry 18 }

nsIfMngPing OBJECT-TYPE
    SYNTAX        INTEGER {
        disable(0),
        enable(1)
    }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This attribute is used to indicate whether the interface
         permits Ping go-through."
    ::= { nsIfEntry 19 }

nsIfMngIdentReset OBJECT-TYPE
    SYNTAX        INTEGER {
        disable(0),
        enable(1)
    }
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "This attribute is used to indicate whether the interface
         permits ident reset."
    ::= { nsIfEntry 20 }

nsIfInfo OBJECT-TYPE
    SYNTAX        Integer32 (0..2147483647)
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Internal id assigned to this interface. Stays persistent across resets."
    ::= { nsIfEntry 21 }

nsIfDescr OBJECT-TYPE
    SYNTAX        DisplayString (SIZE(0..32))
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Each interface has a a configurable descriptor string."
    ::= { nsIfEntry 22 }

nsIfSecondaryIpTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF NsIfSecondaryIpEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "In some ScreenOS, it supports multiple IP addresses and
         related subnets based on the same interface. This table
         collects the multiple IP addresses configuration on an
         interface."
    ::= { netscreenInterface 2 }

nsIfSecondaryIpEntry OBJECT-TYPE
    SYNTAX        NsIfSecondaryIpEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "nsIfSecondaryIpEntry collects some attributes about multiple
         IP configurations  on an interface."
    INDEX
        { nsIfSecondaryIpIndex }
    ::= { nsIfSecondaryIpTable 1 }

nsIfSecondaryIpIndex OBJECT-TYPE
    SYNTAX        Integer32 (0..2147483647)
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The table index used as primary key when retrieving the
         table."
    ::= { nsIfSecondaryIpEntry 1 }

nsIfSecondaryIpIfIdx OBJECT-TYPE
    SYNTAX        Integer32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "The index value which uniquely identifies an interface the
         secodary ip belongs to. The interface identified by a
         particular value  of this index is the same interface as
         identified by the same value of ifIndex."
    ::= { nsIfSecondaryIpEntry 2 }

nsIfSecondaryIpVsys OBJECT-TYPE
    SYNTAX        Integer32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "According NetScreen's concepts, each interface belongs to one
         virtual system. This attribute displays the virtual system ID
         an interface belongs to. If the device is device which doesn't
         support vsys such as  NS-100, we can logically think all the
         setting belongs to 'root' vsys."
    ::= { nsIfSecondaryIpEntry 3 }

nsIfSecondaryIpZone OBJECT-TYPE
    SYNTAX        Integer32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "each interface belongs to one security zone. this attribute
         displays the security zone name an interface belongs to."
    ::= { nsIfSecondaryIpEntry 4 }

nsIfSecondaryIpAddress OBJECT-TYPE
    SYNTAX        IpAddress
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Ip Address value."
    ::= { nsIfSecondaryIpEntry 5 }

nsIfSecondaryIpNetmask OBJECT-TYPE
    SYNTAX        IpAddress
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Subnet of a ip belongs to."
    ::= { nsIfSecondaryIpEntry 6 }

nsIfSecondaryIpIfInfo OBJECT-TYPE
    SYNTAX        Integer32 (0..2147483647)
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Internal id assigned to this interface. Stays persistent across resets."
    ::= { nsIfSecondaryIpEntry 7 }

nsIfFlowTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF NsIfFlowEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "nsIfFlowTable is used to get flow statistical information of
         the interface."
    ::= { netscreenInterface 3 }

nsIfFlowEntry OBJECT-TYPE
    SYNTAX        NsIfFlowEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "Table entry collect some attributes about interface flow
         counters."
    INDEX
        { nsIfFlowIfIdx }
    ::= { nsIfFlowTable 1 }

nsIfFlowIfIdx OBJECT-TYPE
    SYNTAX        Integer32 (0..2147483647)
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Unique interface id, also used as table index."
    ::= { nsIfFlowEntry 1 }

nsIfFlowVsys OBJECT-TYPE
    SYNTAX        Integer32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "According NetScreen's concepts. each interface belongs to one
         virtual system. This attribute displays the virtual system name
         an interface belongs to."
    ::= { nsIfFlowEntry 2 }

nsIfFlowInByte OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Incoming byte number arriving at the this interface"
    ::= { nsIfFlowEntry 3 }

nsIfFlowInPacket OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Incoming packet number arriving at the this interface"
    ::= { nsIfFlowEntry 4 }

nsIfFlowOutByte OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Outgoing byte number sending through this interface"
    ::= { nsIfFlowEntry 5 }

nsIfFlowOutPacket OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Outgoing packet number sending through this interface"
    ::= { nsIfFlowEntry 6 }

nsIfFlowInVpn OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "VPN packet go through this interface"
    ::= { nsIfFlowEntry 7 }

nsIfInVlan OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Incoming vlan packet"
    ::= { nsIfFlowEntry 8 }

nsIfOutVlan OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Outgoing vlan packet"
    ::= { nsIfFlowEntry 9 }

nsIfFlowIfInfo OBJECT-TYPE
    SYNTAX        Integer32 (0..2147483647)
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Internal id assigned to this interface. Stays persistent across resets."
    ::= { nsIfFlowEntry 10 }

nsIfMonTable OBJECT-TYPE
    SYNTAX        SEQUENCE OF NsIfMonEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "This table collects some of dropped packet counters of
         interface"
    ::= { netscreenInterface 4 }

nsIfMonEntry OBJECT-TYPE
    SYNTAX        NsIfMonEntry
    MAX-ACCESS    not-accessible
    STATUS        current
    DESCRIPTION
        "An entry containing tunnel information"
    INDEX
        { nsIfMonIfIdx }
    ::= { nsIfMonTable 1 }

nsIfMonIfIdx OBJECT-TYPE
    SYNTAX        Integer32 (0..2147483647)
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Unique interface id, also used as table index."
    ::= { nsIfMonEntry 1 }

nsIfMonVsys OBJECT-TYPE
    SYNTAX        Integer32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "According NetScreen's concepts, each interface belongs to one
         virtual system. This attribute displays the virtual system name
         an interface belongs to."
    ::= { nsIfMonEntry 2 }

nsIfMonPlyDeny OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Packet drop since denied by policy."
    ::= { nsIfMonEntry 3 }

nsIfMonAuthFail OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Packet drop due to authentication failed."
    ::= { nsIfMonEntry 4 }

nsIfMonUrlBlock OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Packet drop due to URL blocking."
    ::= { nsIfMonEntry 5 }

nsIfMonTrMngQueue OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "packet queue up due to traffic management"
    ::= { nsIfMonEntry 6 }

nsIfMonTrMngDrop OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Packet drop due to traffic management"
    ::= { nsIfMonEntry 7 }

nsIfMonEncFail OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "IPSec enc failed due to sa not available, no ipak, etc"
    ::= { nsIfMonEntry 8 }

nsIfMonNoSa OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "packet drop due to no sa found for incomig spi"
    ::= { nsIfMonEntry 9 }

nsIfMonNoSaPly OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "packet drop due to no policy associated with found sa"
    ::= { nsIfMonEntry 10 }

nsIfMonSaInactive OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "packet drop due to sa not active"
    ::= { nsIfMonEntry 11 }

nsIfMonSaPolicyDeny OBJECT-TYPE
    SYNTAX        Counter32
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "packet drop due to denial by sa policy"
    ::= { nsIfMonEntry 12 }

nsIfMonIfInfo OBJECT-TYPE
    SYNTAX        Integer32 (0..2147483647)
    MAX-ACCESS    read-only
    STATUS        current
    DESCRIPTION
        "Internal id assigned to this interface. Stays persistent across resets."
    ::= { nsIfMonEntry 13 }

END


