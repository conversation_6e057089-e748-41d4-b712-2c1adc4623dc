{"version": 3, "sources": ["../../@kurkle/color/dist/color.esm.js", "../../chart.js/src/helpers/helpers.core.ts", "../../chart.js/src/helpers/helpers.math.ts", "../../chart.js/src/helpers/helpers.collection.ts", "../../chart.js/src/helpers/helpers.extras.ts", "../../chart.js/src/helpers/helpers.easing.ts", "../../chart.js/src/helpers/helpers.color.ts", "../../chart.js/src/core/core.animations.defaults.js", "../../chart.js/src/core/core.layouts.defaults.js", "../../chart.js/src/helpers/helpers.intl.ts", "../../chart.js/src/core/core.ticks.js", "../../chart.js/src/core/core.scale.defaults.js", "../../chart.js/src/core/core.defaults.js", "../../chart.js/src/helpers/helpers.canvas.ts", "../../chart.js/src/helpers/helpers.options.ts", "../../chart.js/src/helpers/helpers.config.ts", "../../chart.js/src/helpers/helpers.curve.ts", "../../chart.js/src/helpers/helpers.dom.ts", "../../chart.js/src/helpers/helpers.interpolation.ts", "../../chart.js/src/helpers/helpers.rtl.ts", "../../chart.js/src/helpers/helpers.segment.js", "../../chart.js/src/helpers/helpers.dataset.ts"], "sourcesContent": ["/*!\n * @kurkle/color v0.3.4\n * https://github.com/kurkle/color#readme\n * (c) 2024 <PERSON><PERSON>\n * Released under the MIT License\n */\nfunction round(v) {\n  return v + 0.5 | 0;\n}\nconst lim = (v, l, h) => Math.max(Math.min(v, h), l);\nfunction p2b(v) {\n  return lim(round(v * 2.55), 0, 255);\n}\nfunction b2p(v) {\n  return lim(round(v / 2.55), 0, 100);\n}\nfunction n2b(v) {\n  return lim(round(v * 255), 0, 255);\n}\nfunction b2n(v) {\n  return lim(round(v / 2.55) / 100, 0, 1);\n}\nfunction n2p(v) {\n  return lim(round(v * 100), 0, 100);\n}\n\nconst map$1 = {0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, A: 10, B: 11, C: 12, D: 13, E: 14, F: 15, a: 10, b: 11, c: 12, d: 13, e: 14, f: 15};\nconst hex = [...'0123456789ABCDEF'];\nconst h1 = b => hex[b & 0xF];\nconst h2 = b => hex[(b & 0xF0) >> 4] + hex[b & 0xF];\nconst eq = b => ((b & 0xF0) >> 4) === (b & 0xF);\nconst isShort = v => eq(v.r) && eq(v.g) && eq(v.b) && eq(v.a);\nfunction hexParse(str) {\n  var len = str.length;\n  var ret;\n  if (str[0] === '#') {\n    if (len === 4 || len === 5) {\n      ret = {\n        r: 255 & map$1[str[1]] * 17,\n        g: 255 & map$1[str[2]] * 17,\n        b: 255 & map$1[str[3]] * 17,\n        a: len === 5 ? map$1[str[4]] * 17 : 255\n      };\n    } else if (len === 7 || len === 9) {\n      ret = {\n        r: map$1[str[1]] << 4 | map$1[str[2]],\n        g: map$1[str[3]] << 4 | map$1[str[4]],\n        b: map$1[str[5]] << 4 | map$1[str[6]],\n        a: len === 9 ? (map$1[str[7]] << 4 | map$1[str[8]]) : 255\n      };\n    }\n  }\n  return ret;\n}\nconst alpha = (a, f) => a < 255 ? f(a) : '';\nfunction hexString(v) {\n  var f = isShort(v) ? h1 : h2;\n  return v\n    ? '#' + f(v.r) + f(v.g) + f(v.b) + alpha(v.a, f)\n    : undefined;\n}\n\nconst HUE_RE = /^(hsla?|hwb|hsv)\\(\\s*([-+.e\\d]+)(?:deg)?[\\s,]+([-+.e\\d]+)%[\\s,]+([-+.e\\d]+)%(?:[\\s,]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction hsl2rgbn(h, s, l) {\n  const a = s * Math.min(l, 1 - l);\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  return [f(0), f(8), f(4)];\n}\nfunction hsv2rgbn(h, s, v) {\n  const f = (n, k = (n + h / 60) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n  return [f(5), f(3), f(1)];\n}\nfunction hwb2rgbn(h, w, b) {\n  const rgb = hsl2rgbn(h, 1, 0.5);\n  let i;\n  if (w + b > 1) {\n    i = 1 / (w + b);\n    w *= i;\n    b *= i;\n  }\n  for (i = 0; i < 3; i++) {\n    rgb[i] *= 1 - w - b;\n    rgb[i] += w;\n  }\n  return rgb;\n}\nfunction hueValue(r, g, b, d, max) {\n  if (r === max) {\n    return ((g - b) / d) + (g < b ? 6 : 0);\n  }\n  if (g === max) {\n    return (b - r) / d + 2;\n  }\n  return (r - g) / d + 4;\n}\nfunction rgb2hsl(v) {\n  const range = 255;\n  const r = v.r / range;\n  const g = v.g / range;\n  const b = v.b / range;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const l = (max + min) / 2;\n  let h, s, d;\n  if (max !== min) {\n    d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    h = hueValue(r, g, b, d, max);\n    h = h * 60 + 0.5;\n  }\n  return [h | 0, s || 0, l];\n}\nfunction calln(f, a, b, c) {\n  return (\n    Array.isArray(a)\n      ? f(a[0], a[1], a[2])\n      : f(a, b, c)\n  ).map(n2b);\n}\nfunction hsl2rgb(h, s, l) {\n  return calln(hsl2rgbn, h, s, l);\n}\nfunction hwb2rgb(h, w, b) {\n  return calln(hwb2rgbn, h, w, b);\n}\nfunction hsv2rgb(h, s, v) {\n  return calln(hsv2rgbn, h, s, v);\n}\nfunction hue(h) {\n  return (h % 360 + 360) % 360;\n}\nfunction hueParse(str) {\n  const m = HUE_RE.exec(str);\n  let a = 255;\n  let v;\n  if (!m) {\n    return;\n  }\n  if (m[5] !== v) {\n    a = m[6] ? p2b(+m[5]) : n2b(+m[5]);\n  }\n  const h = hue(+m[2]);\n  const p1 = +m[3] / 100;\n  const p2 = +m[4] / 100;\n  if (m[1] === 'hwb') {\n    v = hwb2rgb(h, p1, p2);\n  } else if (m[1] === 'hsv') {\n    v = hsv2rgb(h, p1, p2);\n  } else {\n    v = hsl2rgb(h, p1, p2);\n  }\n  return {\n    r: v[0],\n    g: v[1],\n    b: v[2],\n    a: a\n  };\n}\nfunction rotate(v, deg) {\n  var h = rgb2hsl(v);\n  h[0] = hue(h[0] + deg);\n  h = hsl2rgb(h);\n  v.r = h[0];\n  v.g = h[1];\n  v.b = h[2];\n}\nfunction hslString(v) {\n  if (!v) {\n    return;\n  }\n  const a = rgb2hsl(v);\n  const h = a[0];\n  const s = n2p(a[1]);\n  const l = n2p(a[2]);\n  return v.a < 255\n    ? `hsla(${h}, ${s}%, ${l}%, ${b2n(v.a)})`\n    : `hsl(${h}, ${s}%, ${l}%)`;\n}\n\nconst map = {\n\tx: 'dark',\n\tZ: 'light',\n\tY: 're',\n\tX: 'blu',\n\tW: 'gr',\n\tV: 'medium',\n\tU: 'slate',\n\tA: 'ee',\n\tT: 'ol',\n\tS: 'or',\n\tB: 'ra',\n\tC: 'lateg',\n\tD: 'ights',\n\tR: 'in',\n\tQ: 'turquois',\n\tE: 'hi',\n\tP: 'ro',\n\tO: 'al',\n\tN: 'le',\n\tM: 'de',\n\tL: 'yello',\n\tF: 'en',\n\tK: 'ch',\n\tG: 'arks',\n\tH: 'ea',\n\tI: 'ightg',\n\tJ: 'wh'\n};\nconst names$1 = {\n\tOiceXe: 'f0f8ff',\n\tantiquewEte: 'faebd7',\n\taqua: 'ffff',\n\taquamarRe: '7fffd4',\n\tazuY: 'f0ffff',\n\tbeige: 'f5f5dc',\n\tbisque: 'ffe4c4',\n\tblack: '0',\n\tblanKedOmond: 'ffebcd',\n\tXe: 'ff',\n\tXeviTet: '8a2be2',\n\tbPwn: 'a52a2a',\n\tburlywood: 'deb887',\n\tcaMtXe: '5f9ea0',\n\tKartYuse: '7fff00',\n\tKocTate: 'd2691e',\n\tcSO: 'ff7f50',\n\tcSnflowerXe: '6495ed',\n\tcSnsilk: 'fff8dc',\n\tcrimson: 'dc143c',\n\tcyan: 'ffff',\n\txXe: '8b',\n\txcyan: '8b8b',\n\txgTMnPd: 'b8860b',\n\txWay: 'a9a9a9',\n\txgYF: '6400',\n\txgYy: 'a9a9a9',\n\txkhaki: 'bdb76b',\n\txmagFta: '8b008b',\n\txTivegYF: '556b2f',\n\txSange: 'ff8c00',\n\txScEd: '9932cc',\n\txYd: '8b0000',\n\txsOmon: 'e9967a',\n\txsHgYF: '8fbc8f',\n\txUXe: '483d8b',\n\txUWay: '2f4f4f',\n\txUgYy: '2f4f4f',\n\txQe: 'ced1',\n\txviTet: '9400d3',\n\tdAppRk: 'ff1493',\n\tdApskyXe: 'bfff',\n\tdimWay: '696969',\n\tdimgYy: '696969',\n\tdodgerXe: '1e90ff',\n\tfiYbrick: 'b22222',\n\tflSOwEte: 'fffaf0',\n\tfoYstWAn: '228b22',\n\tfuKsia: 'ff00ff',\n\tgaRsbSo: 'dcdcdc',\n\tghostwEte: 'f8f8ff',\n\tgTd: 'ffd700',\n\tgTMnPd: 'daa520',\n\tWay: '808080',\n\tgYF: '8000',\n\tgYFLw: 'adff2f',\n\tgYy: '808080',\n\thoneyMw: 'f0fff0',\n\thotpRk: 'ff69b4',\n\tRdianYd: 'cd5c5c',\n\tRdigo: '4b0082',\n\tivSy: 'fffff0',\n\tkhaki: 'f0e68c',\n\tlavFMr: 'e6e6fa',\n\tlavFMrXsh: 'fff0f5',\n\tlawngYF: '7cfc00',\n\tNmoncEffon: 'fffacd',\n\tZXe: 'add8e6',\n\tZcSO: 'f08080',\n\tZcyan: 'e0ffff',\n\tZgTMnPdLw: 'fafad2',\n\tZWay: 'd3d3d3',\n\tZgYF: '90ee90',\n\tZgYy: 'd3d3d3',\n\tZpRk: 'ffb6c1',\n\tZsOmon: 'ffa07a',\n\tZsHgYF: '20b2aa',\n\tZskyXe: '87cefa',\n\tZUWay: '778899',\n\tZUgYy: '778899',\n\tZstAlXe: 'b0c4de',\n\tZLw: 'ffffe0',\n\tlime: 'ff00',\n\tlimegYF: '32cd32',\n\tlRF: 'faf0e6',\n\tmagFta: 'ff00ff',\n\tmaPon: '800000',\n\tVaquamarRe: '66cdaa',\n\tVXe: 'cd',\n\tVScEd: 'ba55d3',\n\tVpurpN: '9370db',\n\tVsHgYF: '3cb371',\n\tVUXe: '7b68ee',\n\tVsprRggYF: 'fa9a',\n\tVQe: '48d1cc',\n\tVviTetYd: 'c71585',\n\tmidnightXe: '191970',\n\tmRtcYam: 'f5fffa',\n\tmistyPse: 'ffe4e1',\n\tmoccasR: 'ffe4b5',\n\tnavajowEte: 'ffdead',\n\tnavy: '80',\n\tTdlace: 'fdf5e6',\n\tTive: '808000',\n\tTivedBb: '6b8e23',\n\tSange: 'ffa500',\n\tSangeYd: 'ff4500',\n\tScEd: 'da70d6',\n\tpOegTMnPd: 'eee8aa',\n\tpOegYF: '98fb98',\n\tpOeQe: 'afeeee',\n\tpOeviTetYd: 'db7093',\n\tpapayawEp: 'ffefd5',\n\tpHKpuff: 'ffdab9',\n\tperu: 'cd853f',\n\tpRk: 'ffc0cb',\n\tplum: 'dda0dd',\n\tpowMrXe: 'b0e0e6',\n\tpurpN: '800080',\n\tYbeccapurpN: '663399',\n\tYd: 'ff0000',\n\tPsybrown: 'bc8f8f',\n\tPyOXe: '4169e1',\n\tsaddNbPwn: '8b4513',\n\tsOmon: 'fa8072',\n\tsandybPwn: 'f4a460',\n\tsHgYF: '2e8b57',\n\tsHshell: 'fff5ee',\n\tsiFna: 'a0522d',\n\tsilver: 'c0c0c0',\n\tskyXe: '87ceeb',\n\tUXe: '6a5acd',\n\tUWay: '708090',\n\tUgYy: '708090',\n\tsnow: 'fffafa',\n\tsprRggYF: 'ff7f',\n\tstAlXe: '4682b4',\n\ttan: 'd2b48c',\n\tteO: '8080',\n\ttEstN: 'd8bfd8',\n\ttomato: 'ff6347',\n\tQe: '40e0d0',\n\tviTet: 'ee82ee',\n\tJHt: 'f5deb3',\n\twEte: 'ffffff',\n\twEtesmoke: 'f5f5f5',\n\tLw: 'ffff00',\n\tLwgYF: '9acd32'\n};\nfunction unpack() {\n  const unpacked = {};\n  const keys = Object.keys(names$1);\n  const tkeys = Object.keys(map);\n  let i, j, k, ok, nk;\n  for (i = 0; i < keys.length; i++) {\n    ok = nk = keys[i];\n    for (j = 0; j < tkeys.length; j++) {\n      k = tkeys[j];\n      nk = nk.replace(k, map[k]);\n    }\n    k = parseInt(names$1[ok], 16);\n    unpacked[nk] = [k >> 16 & 0xFF, k >> 8 & 0xFF, k & 0xFF];\n  }\n  return unpacked;\n}\n\nlet names;\nfunction nameParse(str) {\n  if (!names) {\n    names = unpack();\n    names.transparent = [0, 0, 0, 0];\n  }\n  const a = names[str.toLowerCase()];\n  return a && {\n    r: a[0],\n    g: a[1],\n    b: a[2],\n    a: a.length === 4 ? a[3] : 255\n  };\n}\n\nconst RGB_RE = /^rgba?\\(\\s*([-+.\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?(?:[\\s,/]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction rgbParse(str) {\n  const m = RGB_RE.exec(str);\n  let a = 255;\n  let r, g, b;\n  if (!m) {\n    return;\n  }\n  if (m[7] !== r) {\n    const v = +m[7];\n    a = m[8] ? p2b(v) : lim(v * 255, 0, 255);\n  }\n  r = +m[1];\n  g = +m[3];\n  b = +m[5];\n  r = 255 & (m[2] ? p2b(r) : lim(r, 0, 255));\n  g = 255 & (m[4] ? p2b(g) : lim(g, 0, 255));\n  b = 255 & (m[6] ? p2b(b) : lim(b, 0, 255));\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\nfunction rgbString(v) {\n  return v && (\n    v.a < 255\n      ? `rgba(${v.r}, ${v.g}, ${v.b}, ${b2n(v.a)})`\n      : `rgb(${v.r}, ${v.g}, ${v.b})`\n  );\n}\n\nconst to = v => v <= 0.0031308 ? v * 12.92 : Math.pow(v, 1.0 / 2.4) * 1.055 - 0.055;\nconst from = v => v <= 0.04045 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);\nfunction interpolate(rgb1, rgb2, t) {\n  const r = from(b2n(rgb1.r));\n  const g = from(b2n(rgb1.g));\n  const b = from(b2n(rgb1.b));\n  return {\n    r: n2b(to(r + t * (from(b2n(rgb2.r)) - r))),\n    g: n2b(to(g + t * (from(b2n(rgb2.g)) - g))),\n    b: n2b(to(b + t * (from(b2n(rgb2.b)) - b))),\n    a: rgb1.a + t * (rgb2.a - rgb1.a)\n  };\n}\n\nfunction modHSL(v, i, ratio) {\n  if (v) {\n    let tmp = rgb2hsl(v);\n    tmp[i] = Math.max(0, Math.min(tmp[i] + tmp[i] * ratio, i === 0 ? 360 : 1));\n    tmp = hsl2rgb(tmp);\n    v.r = tmp[0];\n    v.g = tmp[1];\n    v.b = tmp[2];\n  }\n}\nfunction clone(v, proto) {\n  return v ? Object.assign(proto || {}, v) : v;\n}\nfunction fromObject(input) {\n  var v = {r: 0, g: 0, b: 0, a: 255};\n  if (Array.isArray(input)) {\n    if (input.length >= 3) {\n      v = {r: input[0], g: input[1], b: input[2], a: 255};\n      if (input.length > 3) {\n        v.a = n2b(input[3]);\n      }\n    }\n  } else {\n    v = clone(input, {r: 0, g: 0, b: 0, a: 1});\n    v.a = n2b(v.a);\n  }\n  return v;\n}\nfunction functionParse(str) {\n  if (str.charAt(0) === 'r') {\n    return rgbParse(str);\n  }\n  return hueParse(str);\n}\nclass Color {\n  constructor(input) {\n    if (input instanceof Color) {\n      return input;\n    }\n    const type = typeof input;\n    let v;\n    if (type === 'object') {\n      v = fromObject(input);\n    } else if (type === 'string') {\n      v = hexParse(input) || nameParse(input) || functionParse(input);\n    }\n    this._rgb = v;\n    this._valid = !!v;\n  }\n  get valid() {\n    return this._valid;\n  }\n  get rgb() {\n    var v = clone(this._rgb);\n    if (v) {\n      v.a = b2n(v.a);\n    }\n    return v;\n  }\n  set rgb(obj) {\n    this._rgb = fromObject(obj);\n  }\n  rgbString() {\n    return this._valid ? rgbString(this._rgb) : undefined;\n  }\n  hexString() {\n    return this._valid ? hexString(this._rgb) : undefined;\n  }\n  hslString() {\n    return this._valid ? hslString(this._rgb) : undefined;\n  }\n  mix(color, weight) {\n    if (color) {\n      const c1 = this.rgb;\n      const c2 = color.rgb;\n      let w2;\n      const p = weight === w2 ? 0.5 : weight;\n      const w = 2 * p - 1;\n      const a = c1.a - c2.a;\n      const w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n      w2 = 1 - w1;\n      c1.r = 0xFF & w1 * c1.r + w2 * c2.r + 0.5;\n      c1.g = 0xFF & w1 * c1.g + w2 * c2.g + 0.5;\n      c1.b = 0xFF & w1 * c1.b + w2 * c2.b + 0.5;\n      c1.a = p * c1.a + (1 - p) * c2.a;\n      this.rgb = c1;\n    }\n    return this;\n  }\n  interpolate(color, t) {\n    if (color) {\n      this._rgb = interpolate(this._rgb, color._rgb, t);\n    }\n    return this;\n  }\n  clone() {\n    return new Color(this.rgb);\n  }\n  alpha(a) {\n    this._rgb.a = n2b(a);\n    return this;\n  }\n  clearer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 - ratio;\n    return this;\n  }\n  greyscale() {\n    const rgb = this._rgb;\n    const val = round(rgb.r * 0.3 + rgb.g * 0.59 + rgb.b * 0.11);\n    rgb.r = rgb.g = rgb.b = val;\n    return this;\n  }\n  opaquer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 + ratio;\n    return this;\n  }\n  negate() {\n    const v = this._rgb;\n    v.r = 255 - v.r;\n    v.g = 255 - v.g;\n    v.b = 255 - v.b;\n    return this;\n  }\n  lighten(ratio) {\n    modHSL(this._rgb, 2, ratio);\n    return this;\n  }\n  darken(ratio) {\n    modHSL(this._rgb, 2, -ratio);\n    return this;\n  }\n  saturate(ratio) {\n    modHSL(this._rgb, 1, ratio);\n    return this;\n  }\n  desaturate(ratio) {\n    modHSL(this._rgb, 1, -ratio);\n    return this;\n  }\n  rotate(deg) {\n    rotate(this._rgb, deg);\n    return this;\n  }\n}\n\nfunction index_esm(input) {\n  return new Color(input);\n}\n\nexport { Color, b2n, b2p, index_esm as default, hexParse, hexString, hsl2rgb, hslString, hsv2rgb, hueParse, hwb2rgb, lim, n2b, n2p, nameParse, p2b, rgb2hsl, rgbParse, rgbString, rotate, round };\n", "/**\n * @namespace Chart.helpers\n */\n\nimport type {AnyObject} from '../types/basic.js';\nimport type {ActiveDataPoint, ChartEvent} from '../types/index.js';\n\n/**\n * An empty function that can be used, for example, for optional callback.\n */\nexport function noop() {\n  /* noop */\n}\n\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */\nexport const uid = (() => {\n  let id = 0;\n  return () => id++;\n})();\n\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nexport function isNullOrUndef(value: unknown): value is null | undefined {\n  return value === null || value === undefined;\n}\n\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */\nexport function isArray<T = unknown>(value: unknown): value is T[] {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nexport function isObject(value: unknown): value is AnyObject {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */\nfunction isNumberFinite(value: unknown): value is number {\n  return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\nexport {\n  isNumberFinite as isFinite,\n};\n\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */\nexport function finiteOrDefault(value: unknown, defaultValue: number) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\n\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */\nexport function valueOrDefault<T>(value: T | undefined, defaultValue: T) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\n\nexport const toPercentage = (value: number | string, dimension: number) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100\n    : +value / dimension;\n\nexport const toDimension = (value: number | string, dimension: number) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100 * dimension\n    : +value;\n\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */\nexport function callback<T extends (this: TA, ...restArgs: unknown[]) => R, TA, R>(\n  fn: T | undefined,\n  args: unknown[],\n  thisArg?: TA\n): R | undefined {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\n\n/**\n * Note(SB) for performance sake, this method should only be used when loopable type\n * is unknown or in none intensive code (not called often and small loopable). Else\n * it's preferable to use a regular for() loop and save extra function calls.\n * @param loopable - The object or array to be iterated.\n * @param fn - The function to call for each item.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n * @param [reverse] - If true, iterates backward on the loopable.\n */\nexport function each<T, TA>(\n  loopable: Record<string, T>,\n  fn: (this: TA, v: T, i: string) => void,\n  thisArg?: TA,\n  reverse?: boolean\n): void;\nexport function each<T, TA>(\n  loopable: T[],\n  fn: (this: TA, v: T, i: number) => void,\n  thisArg?: TA,\n  reverse?: boolean\n): void;\nexport function each<T, TA>(\n  loopable: T[] | Record<string, T>,\n  fn: (this: TA, v: T, i: any) => void,\n  thisArg?: TA,\n  reverse?: boolean\n) {\n  let i: number, len: number, keys: string[];\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\n\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */\nexport function _elementsEqual(a0: ActiveDataPoint[], a1: ActiveDataPoint[]) {\n  let i: number, ilen: number, v0: ActiveDataPoint, v1: ActiveDataPoint;\n\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */\nexport function clone<T>(source: T): T {\n  if (isArray(source)) {\n    return source.map(clone) as unknown as T;\n  }\n\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone(source[keys[k]]);\n    }\n\n    return target;\n  }\n\n  return source;\n}\n\nfunction isValidKey(key: string) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\n\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */\nexport function _merger(key: string, target: AnyObject, source: AnyObject, options: AnyObject) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone(sval);\n  }\n}\n\nexport interface MergeOptions {\n  merger?: (key: string, target: AnyObject, source: AnyObject, options?: AnyObject) => void;\n}\n\n/**\n * Recursively deep copies `source` properties into `target` with the given `options`.\n * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\n * @param target - The target object in which all sources are merged into.\n * @param source - Object(s) to merge into `target`.\n * @param [options] - Merging options:\n * @param [options.merger] - The merge method (key, target, source, options)\n * @returns The `target` object.\n */\nexport function merge<T>(target: T, source: [], options?: MergeOptions): T;\nexport function merge<T, S1>(target: T, source: S1, options?: MergeOptions): T & S1;\nexport function merge<T, S1>(target: T, source: [S1], options?: MergeOptions): T & S1;\nexport function merge<T, S1, S2>(target: T, source: [S1, S2], options?: MergeOptions): T & S1 & S2;\nexport function merge<T, S1, S2, S3>(target: T, source: [S1, S2, S3], options?: MergeOptions): T & S1 & S2 & S3;\nexport function merge<T, S1, S2, S3, S4>(\n  target: T,\n  source: [S1, S2, S3, S4],\n  options?: MergeOptions\n): T & S1 & S2 & S3 & S4;\nexport function merge<T>(target: T, source: AnyObject[], options?: MergeOptions): AnyObject;\nexport function merge<T>(target: T, source: AnyObject[], options?: MergeOptions): AnyObject {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n\n  if (!isObject(target)) {\n    return target as AnyObject;\n  }\n\n  options = options || {};\n  const merger = options.merger || _merger;\n  let current: AnyObject;\n\n  for (let i = 0; i < ilen; ++i) {\n    current = sources[i];\n    if (!isObject(current)) {\n      continue;\n    }\n\n    const keys = Object.keys(current);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, current, options as AnyObject);\n    }\n  }\n\n  return target;\n}\n\n/**\n * Recursively deep copies `source` properties into `target` *only* if not defined in target.\n * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\n * @param target - The target object in which all sources are merged into.\n * @param source - Object(s) to merge into `target`.\n * @returns The `target` object.\n */\nexport function mergeIf<T>(target: T, source: []): T;\nexport function mergeIf<T, S1>(target: T, source: S1): T & S1;\nexport function mergeIf<T, S1>(target: T, source: [S1]): T & S1;\nexport function mergeIf<T, S1, S2>(target: T, source: [S1, S2]): T & S1 & S2;\nexport function mergeIf<T, S1, S2, S3>(target: T, source: [S1, S2, S3]): T & S1 & S2 & S3;\nexport function mergeIf<T, S1, S2, S3, S4>(target: T, source: [S1, S2, S3, S4]): T & S1 & S2 & S3 & S4;\nexport function mergeIf<T>(target: T, source: AnyObject[]): AnyObject;\nexport function mergeIf<T>(target: T, source: AnyObject[]): AnyObject {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return merge<T>(target, source, {merger: _mergerIf});\n}\n\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */\nexport function _mergerIf(key: string, target: AnyObject, source: AnyObject) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone(sval);\n  }\n}\n\n/**\n * @private\n */\nexport function _deprecated(scope: string, value: unknown, previous: string, current: string) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous +\n      '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\n\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n  // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n  '': v => v,\n  // default resolvers\n  x: o => o.x,\n  y: o => o.y\n};\n\n/**\n * @private\n */\nexport function _splitKey(key: string) {\n  const parts = key.split('.');\n  const keys: string[] = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\n\nfunction _getKeyResolver(key: string) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        // For backward compatibility:\n        // Chart.helpers.core resolveObjectKey should break at empty key\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\n\nexport function resolveObjectKey(obj: AnyObject, key: string): any {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\n\n/**\n * @private\n */\nexport function _capitalize(str: string) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\n\nexport const defined = (value: unknown) => typeof value !== 'undefined';\n\nexport const isFunction = (value: unknown): value is (...args: any[]) => any => typeof value === 'function';\n\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nexport const setsEqual = <T>(a: Set<T>, b: Set<T>) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n\n/**\n * @param e - The event\n * @private\n */\nexport function _isClickEvent(e: ChartEvent) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n", "import type {Point} from '../types/geometric.js';\nimport {isFinite as isFiniteNumber} from './helpers.core.js';\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */\n\nexport const PI = Math.PI;\nexport const TAU = 2 * PI;\nexport const PITAU = TAU + PI;\nexport const INFINITY = Number.POSITIVE_INFINITY;\nexport const RAD_PER_DEG = PI / 180;\nexport const HALF_PI = PI / 2;\nexport const QUARTER_PI = PI / 4;\nexport const TWO_THIRDS_PI = PI * 2 / 3;\n\nexport const log10 = Math.log10;\nexport const sign = Math.sign;\n\nexport function almostEquals(x: number, y: number, epsilon: number) {\n  return Math.abs(x - y) < epsilon;\n}\n\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */\nexport function niceNum(range: number) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\n\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */\nexport function _factorize(value: number) {\n  const result: number[] = [];\n  const sqrt = Math.sqrt(value);\n  let i: number;\n\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) { // if value is a square number\n    result.push(sqrt);\n  }\n\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\n\n/**\n * Verifies that attempting to coerce n to string or number won't throw a TypeError.\n */\nfunction isNonPrimitive(n: unknown) {\n  return typeof n === 'symbol' || (typeof n === 'object' && n !== null && !(Symbol.toPrimitive in n || 'toString' in n || 'valueOf' in n));\n}\n\nexport function isNumber(n: unknown): n is number {\n  return !isNonPrimitive(n) && !isNaN(parseFloat(n as string)) && isFinite(n as number);\n}\n\nexport function almostWhole(x: number, epsilon: number) {\n  const rounded = Math.round(x);\n  return ((rounded - epsilon) <= x) && ((rounded + epsilon) >= x);\n}\n\n/**\n * @private\n */\nexport function _setMinAndMaxByKey(\n  array: Record<string, number>[],\n  target: { min: number, max: number },\n  property: string\n) {\n  let i: number, ilen: number, value: number;\n\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\n\nexport function toRadians(degrees: number) {\n  return degrees * (PI / 180);\n}\n\nexport function toDegrees(radians: number) {\n  return radians * (180 / PI);\n}\n\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */\nexport function _decimalPlaces(x: number) {\n  if (!isFiniteNumber(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\n\n// Gets the angle from vertical upright to the point about a centre.\nexport function getAngleFromPoint(\n  centrePoint: Point,\n  anglePoint: Point\n) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n\n  if (angle < (-0.5 * PI)) {\n    angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n  }\n\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\n\nexport function distanceBetweenPoints(pt1: Point, pt2: Point) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */\nexport function _angleDiff(a: number, b: number) {\n  return (a - b + PITAU) % TAU - PI;\n}\n\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */\nexport function _normalizeAngle(a: number) {\n  return (a % TAU + TAU) % TAU;\n}\n\n/**\n * @private\n */\nexport function _angleBetween(angle: number, start: number, end: number, sameAngleIsFullCircle?: boolean) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || (sameAngleIsFullCircle && s === e)\n    || (angleToStart > angleToEnd && startToAngle < endToAngle);\n}\n\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */\nexport function _limitValue(value: number, min: number, max: number) {\n  return Math.max(min, Math.min(max, value));\n}\n\n/**\n * @param {number} value\n * @private\n */\nexport function _int16Range(value: number) {\n  return _limitValue(value, -32768, 32767);\n}\n\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */\nexport function _isBetween(value: number, start: number, end: number, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\n", "import {_capitalize} from './helpers.core.js';\n\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param value - value to find\n * @param cmp\n * @private\n */\nexport function _lookup(\n  table: number[],\n  value: number,\n  cmp?: (value: number) => boolean\n): {lo: number, hi: number};\nexport function _lookup<T>(\n  table: T[],\n  value: number,\n  cmp: (value: number) => boolean\n): {lo: number, hi: number};\nexport function _lookup(\n  table: unknown[],\n  value: number,\n  cmp?: (value: number) => boolean\n) {\n  cmp = cmp || ((index) => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid: number;\n\n  while (hi - lo > 1) {\n    mid = (lo + hi) >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n\n  return {lo, hi};\n}\n\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */\nexport const _lookupByKey = (\n  table: Record<string, number>[],\n  key: string,\n  value: number,\n  last?: boolean\n) =>\n  _lookup(table, value, last\n    ? index => {\n      const ti = table[index][key];\n      return ti < value || ti === value && table[index + 1][key] === value;\n    }\n    : index => table[index][key] < value);\n\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */\nexport const _rlookupByKey = (\n  table: Record<string, number>[],\n  key: string,\n  value: number\n) =>\n  _lookup(table, value, index => table[index][key] >= value);\n\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */\nexport function _filterBetween(values: number[], min: number, max: number) {\n  let start = 0;\n  let end = values.length;\n\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n\n  return start > 0 || end < values.length\n    ? values.slice(start, end)\n    : values;\n}\n\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'] as const;\n\nexport interface ArrayListener<T> {\n  _onDataPush?(...item: T[]): void;\n  _onDataPop?(): void;\n  _onDataShift?(): void;\n  _onDataSplice?(index: number, deleteCount: number, ...items: T[]): void;\n  _onDataUnshift?(...item: T[]): void;\n}\n\n/**\n * Hooks the array methods that add or remove values ('push', pop', 'shift', 'splice',\n * 'unshift') and notify the listener AFTER the array has been altered. Listeners are\n * called on the '_onData*' callbacks (e.g. _onDataPush, etc.) with same arguments.\n */\nexport function listenArrayEvents<T>(array: T[], listener: ArrayListener<T>): void;\nexport function listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n\n  arrayEvents.forEach((key) => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n\n        array._chartjs.listeners.forEach((object) => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n\n        return res;\n      }\n    });\n  });\n}\n\n\n/**\n * Removes the given array event listener and cleanup extra attached properties (such as\n * the _chartjs stub and overridden methods) if array doesn't have any more listeners.\n */\nexport function unlistenArrayEvents<T>(array: T[], listener: ArrayListener<T>): void;\nexport function unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n\n  if (listeners.length > 0) {\n    return;\n  }\n\n  arrayEvents.forEach((key) => {\n    delete array[key];\n  });\n\n  delete array._chartjs;\n}\n\n/**\n * @param items\n */\nexport function _arrayUnique<T>(items: T[]) {\n  const set = new Set<T>(items);\n\n  if (set.size === items.length) {\n    return items;\n  }\n\n  return Array.from(set);\n}\n", "import type {ChartMeta, PointElement} from '../types/index.js';\n\nimport {_limitValue} from './helpers.math.js';\nimport {_lookupByKey} from './helpers.collection.js';\nimport {isNullOrUndef} from './helpers.core.js';\n\nexport function fontString(pixelSize: number, fontStyle: string, fontFamily: string) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n\n/**\n* Request animation polyfill\n*/\nexport const requestAnimFrame = (function() {\n  if (typeof window === 'undefined') {\n    return function(callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}());\n\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */\nexport function throttled<TArgs extends Array<any>>(\n  fn: (...args: TArgs) => void,\n  thisArg: any,\n) {\n  let argsToUse = [] as TArgs;\n  let ticking = false;\n\n  return function(...args: TArgs) {\n    // Save the args for use later\n    argsToUse = args;\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, argsToUse);\n      });\n    }\n  };\n}\n\n/**\n * Debounces calling `fn` for `delay` ms\n */\nexport function debounce<TArgs extends Array<any>>(fn: (...args: TArgs) => void, delay: number) {\n  let timeout;\n  return function(...args: TArgs) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\n\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */\nexport const _toLeftRightCenter = (align: 'start' | 'end' | 'center') => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */\nexport const _alignStartEnd = (align: 'start' | 'end' | 'center', start: number, end: number) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */\nexport const _textX = (align: 'left' | 'right' | 'center', left: number, right: number, rtl: boolean) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n\n/**\n * Return start and count of visible points.\n * @private\n */\nexport function _getStartAndCountOfVisiblePoints(meta: ChartMeta<'line' | 'scatter'>, points: PointElement[], animationsDisabled: boolean) {\n  const pointCount = points.length;\n\n  let start = 0;\n  let count = pointCount;\n\n  if (meta._sorted) {\n    const {iScale, vScale, _parsed} = meta;\n    const spanGaps = meta.dataset ? meta.dataset.options ? meta.dataset.options.spanGaps : null : null;\n    const axis = iScale.axis;\n    const {min, max, minDefined, maxDefined} = iScale.getUserBounds();\n\n    if (minDefined) {\n      start = Math.min(\n        // @ts-expect-error Need to type _parsed\n        _lookupByKey(_parsed, axis, min).lo,\n        // @ts-expect-error Need to fix types on _lookupByKey\n        animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo);\n      if (spanGaps) {\n        const distanceToDefinedLo = (_parsed\n          .slice(0, start + 1)\n          .reverse()\n          .findIndex(\n            point => !isNullOrUndef(point[vScale.axis])));\n        start -= Math.max(0, distanceToDefinedLo);\n      }\n      start = _limitValue(start, 0, pointCount - 1);\n    }\n    if (maxDefined) {\n      let end = Math.max(\n        // @ts-expect-error Need to type _parsed\n        _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n        // @ts-expect-error Need to fix types on _lookupByKey\n        animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1);\n      if (spanGaps) {\n        const distanceToDefinedHi = (_parsed\n          .slice(end - 1)\n          .findIndex(\n            point => !isNullOrUndef(point[vScale.axis])));\n        end += Math.max(0, distanceToDefinedHi);\n      }\n      count = _limitValue(end, start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n\n  return {start, count};\n}\n\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */\nexport function _scaleRangesChanged(meta) {\n  const {xScale, yScale, _scaleRanges} = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min\n\t\t|| _scaleRanges.xmax !== xScale.max\n\t\t|| _scaleRanges.ymin !== yScale.min\n\t\t|| _scaleRanges.ymax !== yScale.max;\n\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\n", "import {PI, TAU, HALF_PI} from './helpers.math.js';\n\nconst atEdge = (t: number) => t === 0 || t === 1;\nconst elasticIn = (t: number, s: number, p: number) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t: number, s: number, p: number) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n\n/**\n * Easing functions adapted from <PERSON>'s easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */\nconst effects = {\n  linear: (t: number) => t,\n\n  easeInQuad: (t: number) => t * t,\n\n  easeOutQuad: (t: number) => -t * (t - 2),\n\n  easeInOutQuad: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t\n    : -0.5 * ((--t) * (t - 2) - 1),\n\n  easeInCubic: (t: number) => t * t * t,\n\n  easeOutCubic: (t: number) => (t -= 1) * t * t + 1,\n\n  easeInOutCubic: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t\n    : 0.5 * ((t -= 2) * t * t + 2),\n\n  easeInQuart: (t: number) => t * t * t * t,\n\n  easeOutQuart: (t: number) => -((t -= 1) * t * t * t - 1),\n\n  easeInOutQuart: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t\n    : -0.5 * ((t -= 2) * t * t * t - 2),\n\n  easeInQuint: (t: number) => t * t * t * t * t,\n\n  easeOutQuint: (t: number) => (t -= 1) * t * t * t * t + 1,\n\n  easeInOutQuint: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t * t\n    : 0.5 * ((t -= 2) * t * t * t * t + 2),\n\n  easeInSine: (t: number) => -Math.cos(t * HALF_PI) + 1,\n\n  easeOutSine: (t: number) => Math.sin(t * HALF_PI),\n\n  easeInOutSine: (t: number) => -0.5 * (Math.cos(PI * t) - 1),\n\n  easeInExpo: (t: number) => (t === 0) ? 0 : Math.pow(2, 10 * (t - 1)),\n\n  easeOutExpo: (t: number) => (t === 1) ? 1 : -Math.pow(2, -10 * t) + 1,\n\n  easeInOutExpo: (t: number) => atEdge(t) ? t : t < 0.5\n    ? 0.5 * Math.pow(2, 10 * (t * 2 - 1))\n    : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n\n  easeInCirc: (t: number) => (t >= 1) ? t : -(Math.sqrt(1 - t * t) - 1),\n\n  easeOutCirc: (t: number) => Math.sqrt(1 - (t -= 1) * t),\n\n  easeInOutCirc: (t: number) => ((t /= 0.5) < 1)\n    ? -0.5 * (Math.sqrt(1 - t * t) - 1)\n    : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n\n  easeInElastic: (t: number) => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n\n  easeOutElastic: (t: number) => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n\n  easeInOutElastic(t: number) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t :\n      t < 0.5\n        ? 0.5 * elasticIn(t * 2, s, p)\n        : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n\n  easeInBack(t: number) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n\n  easeOutBack(t: number) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n\n  easeInOutBack(t: number) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= (1.525)) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= (1.525)) + 1) * t + s) + 2);\n  },\n\n  easeInBounce: (t: number) => 1 - effects.easeOutBounce(1 - t),\n\n  easeOutBounce(t: number) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < (1 / d)) {\n      return m * t * t;\n    }\n    if (t < (2 / d)) {\n      return m * (t -= (1.5 / d)) * t + 0.75;\n    }\n    if (t < (2.5 / d)) {\n      return m * (t -= (2.25 / d)) * t + 0.9375;\n    }\n    return m * (t -= (2.625 / d)) * t + 0.984375;\n  },\n\n  easeInOutBounce: (t: number) => (t < 0.5)\n    ? effects.easeInBounce(t * 2) * 0.5\n    : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5,\n} as const;\n\nexport type EasingFunction = keyof typeof effects\n\nexport default effects;\n", "import {Color} from '@kurkle/color';\n\nexport function isPatternOrGradient(value: unknown): value is CanvasPattern | CanvasGradient {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n\n  return false;\n}\n\nexport function color(value: CanvasGradient): CanvasGradient;\nexport function color(value: CanvasPattern): CanvasPattern;\nexport function color(\n  value:\n  | string\n  | { r: number; g: number; b: number; a: number }\n  | [number, number, number]\n  | [number, number, number, number]\n): Color;\nexport function color(value) {\n  return isPatternOrGradient(value) ? value : new Color(value);\n}\n\nexport function getHoverColor(value: CanvasGradient): CanvasGradient;\nexport function getHoverColor(value: CanvasPattern): CanvasPattern;\nexport function getHoverColor(value: string): string;\nexport function getHoverColor(value) {\n  return isPatternOrGradient(value)\n    ? value\n    : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\n", "const numbers = ['x', 'y', 'borderWidth', 'radius', 'tension'];\nconst colors = ['color', 'borderColor', 'backgroundColor'];\n\nexport function applyAnimationsDefaults(defaults) {\n  defaults.set('animation', {\n    delay: undefined,\n    duration: 1000,\n    easing: 'easeOutQuart',\n    fn: undefined,\n    from: undefined,\n    loop: undefined,\n    to: undefined,\n    type: undefined,\n  });\n\n  defaults.describe('animation', {\n    _fallback: false,\n    _indexable: false,\n    _scriptable: (name) => name !== 'onProgress' && name !== 'onComplete' && name !== 'fn',\n  });\n\n  defaults.set('animations', {\n    colors: {\n      type: 'color',\n      properties: colors\n    },\n    numbers: {\n      type: 'number',\n      properties: numbers\n    },\n  });\n\n  defaults.describe('animations', {\n    _fallback: 'animation',\n  });\n\n  defaults.set('transitions', {\n    active: {\n      animation: {\n        duration: 400\n      }\n    },\n    resize: {\n      animation: {\n        duration: 0\n      }\n    },\n    show: {\n      animations: {\n        colors: {\n          from: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          duration: 0 // show immediately\n        },\n      }\n    },\n    hide: {\n      animations: {\n        colors: {\n          to: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          easing: 'linear',\n          fn: v => v | 0 // for keeping the dataset visible all the way through the animation\n        },\n      }\n    }\n  });\n}\n", "export function applyLayoutsDefaults(defaults) {\n  defaults.set('layout', {\n    autoPadding: true,\n    padding: {\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    }\n  });\n}\n", "\nconst intlCache = new Map<string, Intl.NumberFormat>();\n\nfunction getNumberFormat(locale: string, options?: Intl.NumberFormatOptions) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\n\nexport function formatNumber(num: number, locale: string, options?: Intl.NumberFormatOptions) {\n  return getNumberFormat(locale, options).format(num);\n}\n", "import {isArray} from '../helpers/helpers.core.js';\nimport {formatNumber} from '../helpers/helpers.intl.js';\nimport {log10} from '../helpers/helpers.math.js';\n\n/**\n * Namespace to hold formatters for different types of ticks\n * @namespace Chart.Ticks.formatters\n */\nconst formatters = {\n  /**\n   * Formatter for value labels\n   * @method Chart.Ticks.formatters.values\n   * @param value the value to display\n   * @return {string|string[]} the label to display\n   */\n  values(value) {\n    return isArray(value) ? /** @type {string[]} */ (value) : '' + value;\n  },\n\n  /**\n   * Formatter for numeric ticks\n   * @method Chart.Ticks.formatters.numeric\n   * @param tickValue {number} the value to be formatted\n   * @param index {number} the position of the tickValue parameter in the ticks array\n   * @param ticks {object[]} the list of ticks being converted\n   * @return {string} string representation of the tickValue parameter\n   */\n  numeric(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0'; // never show decimal places for 0\n    }\n\n    const locale = this.chart.options.locale;\n    let notation;\n    let delta = tickValue; // This is used when there are less than 2 ticks as the tick interval.\n\n    if (ticks.length > 1) {\n      // all ticks are small or there huge numbers; use scientific notation\n      const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n      if (maxTick < 1e-4 || maxTick > 1e+15) {\n        notation = 'scientific';\n      }\n\n      delta = calculateDelta(tickValue, ticks);\n    }\n\n    const logDelta = log10(Math.abs(delta));\n\n    // When datasets have values approaching Number.MAX_VALUE, the tick calculations might result in\n    // infinity and eventually NaN. Passing NaN for minimumFractionDigits or maximumFractionDigits\n    // will make the number formatter throw. So instead we check for isNaN and use a fallback value.\n    //\n    // toFixed has a max of 20 decimal places\n    const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n\n    const options = {notation, minimumFractionDigits: numDecimal, maximumFractionDigits: numDecimal};\n    Object.assign(options, this.options.ticks.format);\n\n    return formatNumber(tickValue, locale, options);\n  },\n\n\n  /**\n   * Formatter for logarithmic ticks\n   * @method Chart.Ticks.formatters.logarithmic\n   * @param tickValue {number} the value to be formatted\n   * @param index {number} the position of the tickValue parameter in the ticks array\n   * @param ticks {object[]} the list of ticks being converted\n   * @return {string} string representation of the tickValue parameter\n   */\n  logarithmic(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const remain = ticks[index].significand || (tickValue / (Math.pow(10, Math.floor(log10(tickValue)))));\n    if ([1, 2, 3, 5, 10, 15].includes(remain) || index > 0.8 * ticks.length) {\n      return formatters.numeric.call(this, tickValue, index, ticks);\n    }\n    return '';\n  }\n\n};\n\n\nfunction calculateDelta(tickValue, ticks) {\n  // Figure out how many digits to show\n  // The space between the first two ticks might be smaller than normal spacing\n  let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n\n  // If we have a number like 2.5 as the delta, figure out how many decimal places we need\n  if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n    // not an integer\n    delta = tickValue - Math.floor(tickValue);\n  }\n  return delta;\n}\n\n/**\n * Namespace to hold static tick generation functions\n * @namespace Chart.Ticks\n */\nexport default {formatters};\n", "import Ticks from './core.ticks.js';\n\nexport function applyScaleDefaults(defaults) {\n  defaults.set('scale', {\n    display: true,\n    offset: false,\n    reverse: false,\n    beginAtZero: false,\n\n    /**\n     * Scale boundary strategy (bypassed by min/max time options)\n     * - `data`: make sure data are fully visible, ticks outside are removed\n     * - `ticks`: make sure ticks are fully visible, data outside are truncated\n     * @see https://github.com/chartjs/Chart.js/pull/4556\n     * @since 3.0.0\n     */\n    bounds: 'ticks',\n\n    clip: true,\n\n    /**\n     * Addition grace added to max and reduced from min data value.\n     * @since 3.0.0\n     */\n    grace: 0,\n\n    // grid line settings\n    grid: {\n      display: true,\n      lineWidth: 1,\n      drawOnChartArea: true,\n      drawTicks: true,\n      tickLength: 8,\n      tickWidth: (_ctx, options) => options.lineWidth,\n      tickColor: (_ctx, options) => options.color,\n      offset: false,\n    },\n\n    border: {\n      display: true,\n      dash: [],\n      dashOffset: 0.0,\n      width: 1\n    },\n\n    // scale title\n    title: {\n      // display property\n      display: false,\n\n      // actual label\n      text: '',\n\n      // top/bottom padding\n      padding: {\n        top: 4,\n        bottom: 4\n      }\n    },\n\n    // label settings\n    ticks: {\n      minRotation: 0,\n      maxRotation: 50,\n      mirror: false,\n      textStrokeWidth: 0,\n      textStrokeColor: '',\n      padding: 3,\n      display: true,\n      autoSkip: true,\n      autoSkipPadding: 3,\n      labelOffset: 0,\n      // We pass through arrays to be rendered as multiline labels, we convert Others to strings here.\n      callback: Ticks.formatters.values,\n      minor: {},\n      major: {},\n      align: 'center',\n      crossAlign: 'near',\n\n      showLabelBackdrop: false,\n      backdropColor: 'rgba(255, 255, 255, 0.75)',\n      backdropPadding: 2,\n    }\n  });\n\n  defaults.route('scale.ticks', 'color', '', 'color');\n  defaults.route('scale.grid', 'color', '', 'borderColor');\n  defaults.route('scale.border', 'color', '', 'borderColor');\n  defaults.route('scale.title', 'color', '', 'color');\n\n  defaults.describe('scale', {\n    _fallback: false,\n    _scriptable: (name) => !name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n    _indexable: (name) => name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash',\n  });\n\n  defaults.describe('scales', {\n    _fallback: 'scale',\n  });\n\n  defaults.describe('scale.ticks', {\n    _scriptable: (name) => name !== 'backdropPadding' && name !== 'callback',\n    _indexable: (name) => name !== 'backdropPadding',\n  });\n}\n", "import {getHoverColor} from '../helpers/helpers.color.js';\nimport {isObject, merge, valueOrDefault} from '../helpers/helpers.core.js';\nimport {applyAnimationsDefaults} from './core.animations.defaults.js';\nimport {applyLayoutsDefaults} from './core.layouts.defaults.js';\nimport {applyScaleDefaults} from './core.scale.defaults.js';\n\nexport const overrides = Object.create(null);\nexport const descriptors = Object.create(null);\n\n/**\n * @param {object} node\n * @param {string} key\n * @return {object}\n */\nfunction getScope(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\n\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope(root, scope), values);\n  }\n  return merge(getScope(root, ''), scope);\n}\n\n/**\n * Please use the module's default export which provides a singleton instance\n * Note: class is exported for typedoc\n */\nexport class Defaults {\n  constructor(_descriptors, _appliers) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = (context) => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = [\n      'mousemove',\n      'mouseout',\n      'click',\n      'touchstart',\n      'touchmove'\n    ];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n\n    this.describe(_descriptors);\n    this.apply(_appliers);\n  }\n\n  /**\n\t * @param {string|object} scope\n\t * @param {object} [values]\n\t */\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n\n  /**\n\t * @param {string} scope\n\t */\n  get(scope) {\n    return getScope(this, scope);\n  }\n\n  /**\n\t * @param {string|object} scope\n\t * @param {object} [values]\n\t */\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n\n  /**\n\t * Routes the named defaults to fallback to another scope/name.\n\t * This routing is useful when those target values, like defaults.color, are changed runtime.\n\t * If the values would be copied, the runtime change would not take effect. By routing, the\n\t * fallback is evaluated at each access, so its always up to date.\n\t *\n\t * Example:\n\t *\n\t * \tdefaults.route('elements.arc', 'backgroundColor', '', 'color')\n\t *   - reads the backgroundColor from defaults.color when undefined locally\n\t *\n\t * @param {string} scope Scope this route applies to.\n\t * @param {string} name Property name that should be routed to different namespace when not defined here.\n\t * @param {string} targetScope The namespace where those properties should be routed to.\n\t * Empty string ('') is the root of defaults.\n\t * @param {string} targetName The target name in the target scope the property should be routed to.\n\t */\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope(this, scope);\n    const targetScopeObject = getScope(this, targetScope);\n    const privateName = '_' + name;\n\n    Object.defineProperties(scopeObject, {\n      // A private property is defined to hold the actual value, when this property is set in its scope (set in the setter)\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      // The actual property is defined as getter/setter so we can do the routing when value is not locally set.\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n\n  apply(appliers) {\n    appliers.forEach((apply) => apply(this));\n  }\n}\n\n// singleton instance\nexport default /* #__PURE__ */ new Defaults({\n  _scriptable: (name) => !name.startsWith('on'),\n  _indexable: (name) => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false,\n  }\n}, [applyAnimationsDefaults, applyLayoutsDefaults, applyScaleDefaults]);\n", "import type {\n  Chart,\n  Point,\n  FontSpec,\n  CanvasFontSpec,\n  PointStyle,\n  RenderTextOpts,\n  BackdropOptions\n} from '../types/index.js';\nimport type {\n  TRBL,\n  SplinePoint,\n  RoundedRect,\n  TRBLCorners\n} from '../types/geometric.js';\nimport {isArray, isNullOrUndef} from './helpers.core.js';\nimport {PI, TAU, HALF_PI, QUARTER_PI, TWO_THIRDS_PI, RAD_PER_DEG} from './helpers.math.js';\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */\nexport function toFontString(font: FontSpec) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n\n  return (font.style ? font.style + ' ' : '')\n\t\t+ (font.weight ? font.weight + ' ' : '')\n\t\t+ font.size + 'px '\n\t\t+ font.family;\n}\n\n/**\n * @private\n */\nexport function _measureText(\n  ctx: CanvasRenderingContext2D,\n  data: Record<string, number>,\n  gc: string[],\n  longest: number,\n  string: string\n) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\n\ntype Thing = string | undefined | null\ntype Things = (Thing | Thing[])[]\n\n/**\n * @private\n */\n// eslint-disable-next-line complexity\nexport function _longestText(\n  ctx: CanvasRenderingContext2D,\n  font: string,\n  arrayOfThings: Things,\n  cache?: {data?: Record<string, number>, garbageCollect?: string[], font?: string}\n) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n\n  ctx.save();\n\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i: number, j: number, jlen: number, thing: Thing | Thing[], nestedThing: Thing | Thing[];\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n\n    // Undefined strings and arrays should not be measured\n    if (thing !== undefined && thing !== null && !isArray(thing)) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      // if it is an array lets measure each element\n      // to do maybe simplify this function a bit so we can do this more recursively?\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        // Undefined strings and arrays should not be measured\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n\n  ctx.restore();\n\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\n\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */\nexport function _alignPixel(chart: Chart, pixel: number, width: number) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n\n/**\n * Clears the entire canvas.\n */\nexport function clearCanvas(canvas?: HTMLCanvasElement, ctx?: CanvasRenderingContext2D) {\n  if (!ctx && !canvas) {\n    return;\n  }\n\n  ctx = ctx || canvas.getContext('2d');\n\n  ctx.save();\n  // canvas.width and canvas.height do not consider the canvas transform,\n  // while clearRect does\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\n\nexport interface DrawPointOptions {\n  pointStyle: PointStyle;\n  rotation?: number;\n  radius: number;\n  borderWidth: number;\n}\n\nexport function drawPoint(\n  ctx: CanvasRenderingContext2D,\n  options: DrawPointOptions,\n  x: number,\n  y: number\n) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  drawPointLegend(ctx, options, x, y, null);\n}\n\n// eslint-disable-next-line complexity\nexport function drawPointLegend(\n  ctx: CanvasRenderingContext2D,\n  options: DrawPointOptions,\n  x: number,\n  y: number,\n  w: number\n) {\n  let type: string, xOffset: number, yOffset: number, size: number, cornerRadius: number, width: number, xOffsetW: number, yOffsetW: number;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n\n  ctx.beginPath();\n\n  switch (style) {\n  // Default includes circle\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n      ctx.closePath();\n      break;\n    case 'triangle':\n      width = w ? w / 2 : radius;\n      ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n    // NOTE: the rounded rect implementation changed to use `arc` instead of\n    // `quadraticCurveTo` since it generates better results when rect is\n    // almost a circle. 0.516 (instead of 0.5) produces results with visually\n    // closer proportion to the previous impl and it is inscribed in the\n    // circle with `radius`. For more details, see the following PRs:\n    // https://github.com/chartjs/Chart.js/issues/5597\n    // https://github.com/chartjs/Chart.js/issues/5858\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'rectRot':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'cross':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'star':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      rad += QUARTER_PI;\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n      break;\n    case false:\n      ctx.closePath();\n      break;\n  }\n\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\n\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */\nexport function _isPointInArea(\n  point: Point,\n  area: TRBL,\n  margin?: number\n) {\n  margin = margin || 0.5; // margin - default is to match rounded decimals\n\n  return !area || (point && point.x > area.left - margin && point.x < area.right + margin &&\n\t\tpoint.y > area.top - margin && point.y < area.bottom + margin);\n}\n\nexport function clipArea(ctx: CanvasRenderingContext2D, area: TRBL) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\n\nexport function unclipArea(ctx: CanvasRenderingContext2D) {\n  ctx.restore();\n}\n\n/**\n * @private\n */\nexport function _steppedLineTo(\n  ctx: CanvasRenderingContext2D,\n  previous: Point,\n  target: Point,\n  flip?: boolean,\n  mode?: string\n) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\n\n/**\n * @private\n */\nexport function _bezierCurveTo(\n  ctx: CanvasRenderingContext2D,\n  previous: SplinePoint,\n  target: SplinePoint,\n  flip?: boolean\n) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(\n    flip ? previous.cp1x : previous.cp2x,\n    flip ? previous.cp1y : previous.cp2y,\n    flip ? target.cp2x : target.cp1x,\n    flip ? target.cp2y : target.cp1y,\n    target.x,\n    target.y);\n}\n\nfunction setRenderOpts(ctx: CanvasRenderingContext2D, opts: RenderTextOpts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\n\nfunction decorateText(\n  ctx: CanvasRenderingContext2D,\n  x: number,\n  y: number,\n  line: string,\n  opts: RenderTextOpts\n) {\n  if (opts.strikethrough || opts.underline) {\n    /**\n     * Now that IE11 support has been dropped, we can use more\n     * of the TextMetrics object. The actual bounding boxes\n     * are unflagged in Chrome, Firefox, Edge, and Safari so they\n     * can be safely used.\n     * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n     */\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\n\nfunction drawBackdrop(ctx: CanvasRenderingContext2D, opts: BackdropOptions) {\n  const oldColor = ctx.fillStyle;\n\n  ctx.fillStyle = opts.color as string;\n  ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n  ctx.fillStyle = oldColor;\n}\n\n/**\n * Render text onto the canvas\n */\nexport function renderText(\n  ctx: CanvasRenderingContext2D,\n  text: string | string[],\n  x: number,\n  y: number,\n  font: CanvasFontSpec,\n  opts: RenderTextOpts = {}\n) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i: number, line: string;\n\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n\n    if (opts.backdrop) {\n      drawBackdrop(ctx, opts.backdrop);\n    }\n\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n\n    y += Number(font.lineHeight);\n  }\n\n  ctx.restore();\n}\n\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */\nexport function addRoundedRectPath(\n  ctx: CanvasRenderingContext2D,\n  rect: RoundedRect & { radius: TRBLCorners }\n) {\n  const {x, y, w, h, radius} = rect;\n\n  // top left arc\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, 1.5 * PI, PI, true);\n\n  // line from top left to bottom left\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n\n  // bottom left arc\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n\n  // line from bottom left to bottom right\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n\n  // bottom right arc\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n\n  // line from bottom right to top right\n  ctx.lineTo(x + w, y + radius.topRight);\n\n  // top right arc\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n\n  // line from top right to top left\n  ctx.lineTo(x + radius.topLeft, y);\n}\n", "import defaults from '../core/core.defaults.js';\nimport {isArray, isObject, toDimension, valueOrDefault} from './helpers.core.js';\nimport {toFontString} from './helpers.canvas.js';\nimport type {ChartArea, FontSpec, Point} from '../types/index.js';\nimport type {TRBL, TRBLCorners} from '../types/geometric.js';\n\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n\n/**\n * @alias Chart.helpers.options\n * @namespace\n */\n/**\n * Converts the given line height `value` in pixels for a specific font `size`.\n * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n * @param size - The font size (in pixels) used to resolve relative `value`.\n * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n * @since 2.7.0\n */\nexport function toLineHeight(value: number | string, size: number): number {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n\n  value = +matches[2];\n\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n    default:\n      break;\n  }\n\n  return size * value;\n}\n\nconst numberOrZero = (v: unknown) => +v || 0;\n\n/**\n * @param value\n * @param props\n */\nexport function _readValueToProps<K extends string>(value: number | Record<K, number>, props: K[]): Record<K, number>;\nexport function _readValueToProps<K extends string, T extends string>(value: number | Record<K & T, number>, props: Record<T, K>): Record<T, number>;\nexport function _readValueToProps(value: number | Record<string, number>, props: string[] | Record<string, string>) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value)\n    ? objProps\n      ? prop => valueOrDefault(value[prop], value[props[prop]])\n      : prop => value[prop]\n    : () => value;\n\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\n\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */\nexport function toTRBL(value: number | TRBL | Point) {\n  return _readValueToProps(value, {top: 'y', right: 'x', bottom: 'y', left: 'x'});\n}\n\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */\nexport function toTRBLCorners(value: number | TRBLCorners) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\n\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */\nexport function toPadding(value?: number | TRBL): ChartArea {\n  const obj = toTRBL(value) as ChartArea;\n\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n\n  return obj;\n}\n\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */\n\nexport function toFont(options: Partial<FontSpec>, fallback?: Partial<FontSpec>) {\n  options = options || {};\n  fallback = fallback || defaults.font as FontSpec;\n\n  let size = valueOrDefault(options.size, fallback.size);\n\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = undefined;\n  }\n\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n\n  font.string = toFontString(font);\n  return font;\n}\n\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */\nexport function resolve(inputs: Array<unknown>, context?: object, index?: number, info?: { cacheable: boolean }) {\n  let cacheable = true;\n  let i: number, ilen: number, value: unknown;\n\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\n\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */\nexport function _addGrace(minmax: { min: number; max: number; }, grace: number | string, beginAtZero: boolean) {\n  const {min, max} = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value: number, add: number) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\n\n/**\n * Create a context inheriting parentContext\n * @param parentContext\n * @param context\n * @returns\n */\nexport function createContext<T extends object>(parentContext: null, context: T): T;\nexport function createContext<T extends object, P extends T>(parentContext: P, context: T): P & T;\nexport function createContext(parentContext: object, context: object) {\n  return Object.assign(Object.create(parentContext), context);\n}\n", "/* eslint-disable @typescript-eslint/no-use-before-define */\nimport type {AnyObject} from '../types/basic.js';\nimport type {ChartMeta} from '../types/index.js';\nimport type {\n  ResolverObjectKey,\n  ResolverCache,\n  ResolverProxy,\n  DescriptorDefaults,\n  Descriptor,\n  ContextCache,\n  ContextProxy\n} from './helpers.config.types.js';\nimport {isArray, isFunction, isObject, resolveObjectKey, _capitalize} from './helpers.core.js';\n\nexport * from './helpers.config.types.js';\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */\nexport function _createResolver<\n  T extends AnyObject[] = AnyObject[],\n  R extends AnyObject[] = T\n>(\n  scopes: T,\n  prefixes = [''],\n  rootScopes?: R,\n  fallback?: ResolverObjectKey,\n  getTarget = () => scopes[0]\n) {\n  const finalRootScopes = rootScopes || scopes;\n  if (typeof fallback === 'undefined') {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache: ResolverCache<T, R> = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: finalRootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: (scope: AnyObject) => _createResolver([scope, ...scopes], prefixes, finalRootScopes, fallback),\n  };\n  return new Proxy(cache, {\n    /**\n     * A trap for the delete operator.\n     */\n    deleteProperty(target, prop: string) {\n      delete target[prop]; // remove from cache\n      delete target._keys; // remove cached keys\n      delete scopes[0][prop]; // remove from top level scope\n      return true;\n    },\n\n    /**\n     * A trap for getting property values.\n     */\n    get(target, prop: string) {\n      return _cached(target, prop,\n        () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n\n    /**\n     * A trap for Object.getPrototypeOf.\n     */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n\n    /**\n     * A trap for the in operator.\n     */\n    has(target, prop: string) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n\n    /**\n     * A trap for setting property values.\n     */\n    set(target, prop: string, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value; // set to top level scope + cache\n      delete target._keys; // remove cached keys\n      return true;\n    }\n  }) as ResolverProxy<T, R>;\n}\n\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */\nexport function _attachContext<\n  T extends AnyObject[] = AnyObject[],\n  R extends AnyObject[] = T\n>(\n  proxy: ResolverProxy<T, R>,\n  context: AnyObject,\n  subProxy?: ResolverProxy<T, R>,\n  descriptorDefaults?: DescriptorDefaults\n) {\n  const cache: ContextCache<T, R> = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: (ctx: AnyObject) => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: (scope: AnyObject) => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    /**\n     * A trap for the delete operator.\n     */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete proxy[prop]; // remove from proxy\n      return true;\n    },\n\n    /**\n     * A trap for getting property values.\n     */\n    get(target, prop: string, receiver) {\n      return _cached(target, prop,\n        () => _resolveWithContext(target, prop, receiver));\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys\n        ? Reflect.has(proxy, prop) ? {enumerable: true, configurable: true} : undefined\n        : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n\n    /**\n     * A trap for Object.getPrototypeOf.\n     */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n\n    /**\n     * A trap for the in operator.\n     */\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n\n    /**\n     * A trap for setting property values.\n     */\n    set(target, prop, value) {\n      proxy[prop] = value; // set to proxy\n      delete target[prop]; // remove from cache\n      return true;\n    }\n  }) as ContextProxy<T, R>;\n}\n\n/**\n * @private\n */\nexport function _descriptors(\n  proxy: ResolverCache,\n  defaults: DescriptorDefaults = {scriptable: true, indexable: true}\n): Descriptor {\n  const {_scriptable = defaults.scriptable, _indexable = defaults.indexable, _allKeys = defaults.allKeys} = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\n\nconst readKey = (prefix: string, name: string) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop: string, value: unknown) => isObject(value) && prop !== 'adapters' &&\n  (Object.getPrototypeOf(value) === null || value.constructor === Object);\n\nfunction _cached(\n  target: AnyObject,\n  prop: string,\n  resolve: () => unknown\n) {\n  if (Object.prototype.hasOwnProperty.call(target, prop) || prop === 'constructor') {\n    return target[prop];\n  }\n\n  const value = resolve();\n  // cache the resolved value\n  target[prop] = value;\n  return value;\n}\n\nfunction _resolveWithContext(\n  target: ContextCache,\n  prop: string,\n  receiver: AnyObject\n) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n  let value = _proxy[prop]; // resolve from proxy\n\n  // resolve with context\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    // if the resolved value is an object, create a sub resolver for it\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\n\nfunction _resolveScriptable(\n  prop: string,\n  getValue: (ctx: AnyObject, sub: AnyObject) => unknown,\n  target: ContextCache,\n  receiver: AnyObject\n) {\n  const {_proxy, _context, _subProxy, _stack} = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  let value = getValue(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    // When scriptable option returns an object, create a resolver on that.\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\n\nfunction _resolveArray(\n  prop: string,\n  value: unknown[],\n  target: ContextCache,\n  isIndexable: (key: string) => boolean\n) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n\n  if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n    return value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    // Array of objects, return array or resolvers\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\n\nfunction resolveFallback(\n  fallback: ResolverObjectKey | ((prop: ResolverObjectKey, value: unknown) => ResolverObjectKey),\n  prop: ResolverObjectKey,\n  value: unknown\n) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\n\nconst getScope = (key: ResolverObjectKey, parent: AnyObject) => key === true ? parent\n  : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\n\nfunction addScopes(\n  set: Set<AnyObject>,\n  parentScopes: AnyObject[],\n  key: ResolverObjectKey,\n  parentFallback: ResolverObjectKey,\n  value: unknown\n) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n        // When we reach the descriptor that defines a new _fallback, return that.\n        // The fallback will resume to that new scope.\n        return fallback;\n      }\n    } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n      // Fallback to `false` results to `false`, when falling back to different key.\n      // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n      return null;\n    }\n  }\n  return false;\n}\n\nfunction createSubResolver(\n  parentScopes: AnyObject[],\n  resolver: ResolverCache,\n  prop: ResolverObjectKey,\n  value: unknown\n) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set<AnyObject>();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (typeof fallback !== 'undefined' && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback,\n    () => subGetTarget(resolver, prop as string, value));\n}\n\nfunction addScopesFromKey(\n  set: Set<AnyObject>,\n  allScopes: AnyObject[],\n  key: ResolverObjectKey,\n  fallback: ResolverObjectKey,\n  item: unknown\n) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\n\nfunction subGetTarget(\n  resolver: ResolverCache,\n  prop: string,\n  value: unknown\n) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    // For array of objects, the object is used to store updated values\n    return value;\n  }\n  return target || {};\n}\n\nfunction _resolveWithPrefixes(\n  prop: string,\n  prefixes: string[],\n  scopes: AnyObject[],\n  proxy: ResolverProxy\n) {\n  let value: unknown;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (typeof value !== 'undefined') {\n      return needsSubResolver(prop, value)\n        ? createSubResolver(scopes, proxy, prop, value)\n        : value;\n    }\n  }\n}\n\nfunction _resolve(key: string, scopes: AnyObject[]) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n  }\n}\n\nfunction getKeysFromAllScopes(target: ResolverCache) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\n\nfunction resolveKeysFromAllScopes(scopes: AnyObject[]) {\n  const set = new Set<string>();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\n\nexport function _parseObjectDataRadialScale(\n  meta: ChartMeta<'line' | 'scatter'>,\n  data: AnyObject[],\n  start: number,\n  count: number\n) {\n  const {iScale} = meta;\n  const {key = 'r'} = this._parsing;\n  const parsed = new Array<{r: unknown}>(count);\n  let i: number, ilen: number, index: number, item: AnyObject;\n\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\n", "import {almostEquals, distanceBetweenPoints, sign} from './helpers.math.js';\nimport {_isPointInArea} from './helpers.canvas.js';\nimport type {ChartArea} from '../types/index.js';\nimport type {SplinePoint} from '../types/geometric.js';\n\nconst EPSILON = Number.EPSILON || 1e-14;\n\ntype OptionalSplinePoint = SplinePoint | false\nconst getPoint = (points: SplinePoint[], i: number): OptionalSplinePoint => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = (indexAxis: 'x' | 'y') => indexAxis === 'x' ? 'y' : 'x';\n\nexport function splineCurve(\n  firstPoint: SplinePoint,\n  middlePoint: SplinePoint,\n  afterPoint: SplinePoint,\n  t: number\n): {\n    previous: SplinePoint\n    next: SplinePoint\n  } {\n  // Props to <PERSON> at scaled innovation for his post on splining between points\n  // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n\n  // This function must also respect \"skipped\" points\n\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n\n  // If all points are the same, s01 & s02 will be inf\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n\n  const fa = t * s01; // scaling factor for triangle Ta\n  const fb = t * s12;\n\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\n\n/**\n * Adjust tangents to ensure monotonic properties\n */\nfunction monotoneAdjust(points: SplinePoint[], deltaK: number[], mK: number[]) {\n  const pointsLen = points.length;\n\n  let alphaK: number, betaK: number, tauK: number, squaredMagnitude: number, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\n\nfunction monotoneCompute(points: SplinePoint[], mK: number[], indexAxis: 'x' | 'y' = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta: number, pointBefore: OptionalSplinePoint, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\n\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */\nexport function splineCurveMonotone(points: SplinePoint[], indexAxis: 'x' | 'y' = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK: number[] = Array(pointsLen).fill(0);\n  const mK: number[] = Array(pointsLen);\n\n  // Calculate slopes (deltaK) and initialize tangents (mK)\n  let i, pointBefore: OptionalSplinePoint, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n\n      // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i]\n      : !pointAfter ? deltaK[i - 1]\n        : (sign(deltaK[i - 1]) !== sign(deltaK[i])) ? 0\n          : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n\n  monotoneAdjust(points, deltaK, mK);\n\n  monotoneCompute(points, mK, indexAxis);\n}\n\nfunction capControlPoint(pt: number, min: number, max: number) {\n  return Math.max(Math.min(pt, max), min);\n}\n\nfunction capBezierPoints(points: SplinePoint[], area: ChartArea) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\n\n/**\n * @private\n */\nexport function _updateBezierControlPoints(\n  points: SplinePoint[],\n  options,\n  area: ChartArea,\n  loop: boolean,\n  indexAxis: 'x' | 'y'\n) {\n  let i: number, ilen: number, point: SplinePoint, controlPoints: ReturnType<typeof splineCurve>;\n\n  // Only consider points that are drawn in case the spanGaps option is used\n  if (options.spanGaps) {\n    points = points.filter((pt) => !pt.skip);\n  }\n\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(\n        prev,\n        point,\n        points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen],\n        options.tension\n      );\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n", "import type {ChartArea, Scale} from '../types/index.js';\nimport type PrivateChart from '../core/core.controller.js';\nimport type {Chart, ChartEvent} from '../types.js';\nimport {INFINITY} from './helpers.math.js';\n\n/**\n * @private\n */\nexport function _isDomSupported(): boolean {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n\n/**\n * @private\n */\nexport function _getParentNode(domNode: HTMLCanvasElement): HTMLCanvasElement {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = (parent as ShadowRoot).host;\n  }\n  return parent as HTMLCanvasElement;\n}\n\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */\n\nfunction parseMaxStyle(styleValue: string | number, node: HTMLElement, parentProperty: string) {\n  let valueInPixels: number;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n\n    if (styleValue.indexOf('%') !== -1) {\n      // percentage * size in dimension\n      valueInPixels = (valueInPixels / 100) * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n\n  return valueInPixels;\n}\n\nconst getComputedStyle = (element: HTMLElement): CSSStyleDeclaration =>\n  element.ownerDocument.defaultView.getComputedStyle(element, null);\n\nexport function getStyle(el: HTMLElement, property: string): string {\n  return getComputedStyle(el).getPropertyValue(property);\n}\n\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles: CSSStyleDeclaration, style: string, suffix?: string): ChartArea {\n  const result = {} as ChartArea;\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\n\nconst useOffsetPos = (x: number, y: number, target: HTMLElement | EventTarget) =>\n  (x > 0 || y > 0) && (!target || !(target as HTMLElement).shadowRoot);\n\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */\nfunction getCanvasPosition(\n  e: Event | TouchEvent | MouseEvent,\n  canvas: HTMLCanvasElement\n): {\n    x: number;\n    y: number;\n    box: boolean;\n  } {\n  const touches = (e as TouchEvent).touches;\n  const source = (touches && touches.length ? touches[0] : e) as MouseEvent;\n  const {offsetX, offsetY} = source as MouseEvent;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {x, y, box};\n}\n\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */\n\nexport function getRelativePosition(\n  event: Event | ChartEvent | TouchEvent | MouseEvent,\n  chart: Chart | PrivateChart\n): { x: number; y: number } {\n  if ('native' in event) {\n    return event;\n  }\n\n  const {canvas, currentDevicePixelRatio} = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {x, y, box} = getCanvasPosition(event, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n\n  let {width, height} = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\n\nfunction getContainerSize(canvas: HTMLCanvasElement, width: number, height: number): Partial<Scale> {\n  let maxWidth: number, maxHeight: number;\n\n  if (width === undefined || height === undefined) {\n    const container = canvas && _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect(); // this is the border box of the container\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\n\nconst round1 = (v: number) => Math.round(v * 10) / 10;\n\n// eslint-disable-next-line complexity\nexport function getMaximumSize(\n  canvas: HTMLCanvasElement,\n  bbWidth?: number,\n  bbHeight?: number,\n  aspectRatio?: number\n): { width: number; height: number } {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {width, height} = containerSize;\n\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    // https://github.com/chartjs/Chart.js/issues/4659\n    // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n    height = round1(width / 2);\n  }\n\n  const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n\n  if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n    height = containerSize.height;\n    width = round1(Math.floor(height * aspectRatio));\n  }\n\n  return {width, height};\n}\n\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */\nexport function retinaScale(\n  chart: Chart | PrivateChart,\n  forceRatio: number,\n  forceStyle?: boolean\n): boolean | void {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n\n  (chart as PrivateChart).height = Math.floor(chart.height);\n  (chart as PrivateChart).width = Math.floor(chart.width);\n\n  const canvas = chart.canvas;\n\n  // If no style has been set on the canvas, the render size is used as display size,\n  // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n  // See https://github.com/chartjs/Chart.js/issues/3575\n  if (canvas.style && (forceStyle || (!canvas.style.height && !canvas.style.width))) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n\n  if (chart.currentDevicePixelRatio !== pixelRatio\n      || canvas.height !== deviceHeight\n      || canvas.width !== deviceWidth) {\n    (chart as PrivateChart).currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\n\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */\nexport const supportsEventListenerOptions = (function() {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() { // This function will be called when the browser attempts to access the passive property.\n        passiveSupported = true;\n        return false;\n      }\n    } as EventListenerOptions;\n\n    if (_isDomSupported()) {\n      window.addEventListener('test', null, options);\n      window.removeEventListener('test', null, options);\n    }\n  } catch (e) {\n    // continue regardless of error\n  }\n  return passiveSupported;\n}());\n\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */\n\nexport function readUsedSize(\n  element: HTMLElement,\n  property: 'width' | 'height'\n): number | undefined {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n", "import type {Point, SplinePoint} from '../types/geometric.js';\n\n/**\n * @private\n */\nexport function _pointInLine(p1: Point, p2: Point, t: number, mode?) { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\n\n/**\n * @private\n */\nexport function _steppedInterpolation(\n  p1: Point,\n  p2: Point,\n  t: number, mode: 'middle' | 'after' | unknown\n) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y\n      : mode === 'after' ? t < 1 ? p1.y : p2.y\n        : t > 0 ? p2.y : p1.y\n  };\n}\n\n/**\n * @private\n */\nexport function _bezierInterpolation(p1: SplinePoint, p2: SplinePoint, t: number, mode?) { // eslint-disable-line @typescript-eslint/no-unused-vars\n  const cp1 = {x: p1.cp2x, y: p1.cp2y};\n  const cp2 = {x: p2.cp1x, y: p2.cp1y};\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\n", "export interface RTLAdapter {\n  x(x: number): number;\n  setWidth(w: number): void;\n  textAlign(align: 'center' | 'left' | 'right'): 'center' | 'left' | 'right';\n  xPlus(x: number, value: number): number;\n  leftForLtr(x: number, itemWidth: number): number;\n}\n\nconst getRightToLeftAdapter = function(rectX: number, width: number): RTLAdapter {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    },\n  };\n};\n\nconst getLeftToRightAdapter = function(): RTLAdapter {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) { // eslint-disable-line no-unused-vars\n    },\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) { // eslint-disable-line @typescript-eslint/no-unused-vars\n      return x;\n    },\n  };\n};\n\nexport function getRtlAdapter(rtl: boolean, rectX: number, width: number) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\n\nexport function overrideTextDirection(ctx: CanvasRenderingContext2D, direction: 'ltr' | 'rtl') {\n  let style: CSSStyleDeclaration, original: [string, string];\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [\n      style.getPropertyValue('direction'),\n      style.getPropertyPriority('direction'),\n    ];\n\n    style.setProperty('direction', direction, 'important');\n    (ctx as { prevTextDirection?: [string, string] }).prevTextDirection = original;\n  }\n}\n\nexport function restoreTextDirection(ctx: CanvasRenderingContext2D, original?: [string, string]) {\n  if (original !== undefined) {\n    delete (ctx as { prevTextDirection?: [string, string] }).prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\n", "import {_angleBetween, _angleDiff, _isBetween, _normalizeAngle} from './helpers.math.js';\nimport {createContext} from './helpers.options.js';\nimport {isPatternOrGradient} from './helpers.color.js';\n\n/**\n * @typedef { import('../elements/element.line.js').default } LineElement\n * @typedef { import('../elements/element.point.js').default } PointElement\n * @typedef {{start: number, end: number, loop: boolean, style?: any}} Segment\n */\n\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle,\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\n\nfunction normalizeSegment({start, end, count, loop, style}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\n\nfunction getSegment(segment, points, bounds) {\n  const {property, start: startBound, end: endBound} = bounds;\n  const {between, normalize} = propertyFn(property);\n  const count = points.length;\n  // eslint-disable-next-line prefer-const\n  let {start, end, loop} = segment;\n  let i, ilen;\n\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n\n  if (end < start) {\n    end += count;\n  }\n  return {start, end, loop, style: segment.style};\n}\n\n/**\n * Returns the sub-segment(s) of a line segment that fall in the given bounds\n * @param {object} segment\n * @param {number} segment.start - start index of the segment, referring the points array\n * @param {number} segment.end - end index of the segment, referring the points array\n * @param {boolean} segment.loop - indicates that the segment is a loop\n * @param {object} [segment.style] - segment style\n * @param {PointElement[]} points - the points that this segment refers to\n * @param {object} [bounds]\n * @param {string} bounds.property - the property of a `PointElement` we are bounding. `x`, `y` or `angle`.\n * @param {number} bounds.start - start value of the property\n * @param {number} bounds.end - end value of the property\n * @private\n **/\nexport function _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n\n  const {property, start: startBound, end: endBound} = bounds;\n  const count = points.length;\n  const {compare, between, normalize} = propertyFn(property);\n  const {start, end, loop, style} = getSegment(segment, points, bounds);\n\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n\n    if (point.skip) {\n      continue;\n    }\n\n    value = normalize(point[property]);\n\n    if (value === prevValue) {\n      continue;\n    }\n\n    inside = between(value, startBound, endBound);\n\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({start: subStart, end: i, loop, count, style}));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n\n  if (subStart !== null) {\n    result.push(normalizeSegment({start: subStart, end, loop, count, style}));\n  }\n\n  return result;\n}\n\n\n/**\n * Returns the segments of the line that are inside given bounds\n * @param {LineElement} line\n * @param {object} [bounds]\n * @param {string} bounds.property - the property we are bounding with. `x`, `y` or `angle`.\n * @param {number} bounds.start - start value of the `property`\n * @param {number} bounds.end - end value of the `property`\n * @private\n */\nexport function _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\n\n/**\n * Find start and end index of a line.\n */\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n\n  if (loop && !spanGaps) {\n    // loop and not spanning gaps, first find a gap to start from\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n\n  // find first non skipped point (after the first gap possibly)\n  while (start < count && points[start].skip) {\n    start++;\n  }\n\n  // if we looped to count, start needs to be 0\n  start %= count;\n\n  if (loop) {\n    // loop will go past count, if start > 0\n    end += start;\n  }\n\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n\n  // end could be more than count, normalize\n  end %= count;\n\n  return {start, end};\n}\n\n/**\n * Compute solid segments from Points, when spanGaps === false\n * @param {PointElement[]} points - the points\n * @param {number} start - start index\n * @param {number} max - max index (can go past count on a loop)\n * @param {boolean} loop - boolean indicating that this would be a loop if no gaps are found\n */\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({start: start % count, end: (end - 1) % count, loop});\n        // @ts-ignore\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n\n  if (last !== null) {\n    result.push({start: start % count, end: last % count, loop});\n  }\n\n  return result;\n}\n\n/**\n * Compute the continuous segments that define the whole line\n * There can be skipped points within a segment, if spanGaps is true.\n * @param {LineElement} line\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n * @private\n */\nexport function _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n\n  if (!count) {\n    return [];\n  }\n\n  const loop = !!line._loop;\n  const {start, end} = findStartAndEnd(points, count, loop, spanGaps);\n\n  if (spanGaps === true) {\n    return splitByStyles(line, [{start, end, loop}], points, segmentOptions);\n  }\n\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\n\n/**\n * @param {Segment[]} segments\n * @param {PointElement[]} points\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n */\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\n\n/**\n * @param {LineElement} line\n * @param {Segment[]} segments\n * @param {PointElement[]} points\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n */\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {_datasetIndex: datasetIndex, options: {spanGaps}} = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    // Style can not start/end on a skipped point, adjust indices accordingly\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({start: s % count, end: e % count, loop: l, style: st});\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n\n  return result;\n}\n\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\n\nfunction styleChanged(style, prevStyle) {\n  if (!prevStyle) {\n    return false;\n  }\n  const cache = [];\n  const replacer = function(key, value) {\n    if (!isPatternOrGradient(value)) {\n      return value;\n    }\n    if (!cache.includes(value)) {\n      cache.push(value);\n    }\n    return cache.indexOf(value);\n  };\n  return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\n", "import type {Chart, ChartArea, ChartMeta, Scale, TRBL} from '../types/index.js';\n\nfunction getSizeForArea(scale: Scale, chartArea: ChartArea, field: keyof ChartArea) {\n  return scale.options.clip ? scale[field] : chartArea[field];\n}\n\nfunction getDatasetArea(meta: ChartMeta, chartArea: ChartArea): TRBL {\n  const {xScale, yScale} = meta;\n  if (xScale && yScale) {\n    return {\n      left: getSizeForArea(xScale, chartArea, 'left'),\n      right: getSizeForArea(xScale, chartArea, 'right'),\n      top: getSizeForArea(yScale, chartArea, 'top'),\n      bottom: getSizeForArea(yScale, chartArea, 'bottom')\n    };\n  }\n  return chartArea;\n}\n\nexport function getDatasetClipArea(chart: Chart, meta: ChartMeta): TRBL | false {\n  const clip = meta._clip;\n  if (clip.disabled) {\n    return false;\n  }\n  const area = getDatasetArea(meta, chart.chartArea);\n\n  return {\n    left: clip.left === false ? 0 : area.left - (clip.left === true ? 0 : clip.left),\n    right: clip.right === false ? chart.width : area.right + (clip.right === true ? 0 : clip.right),\n    top: clip.top === false ? 0 : area.top - (clip.top === true ? 0 : clip.top),\n    bottom: clip.bottom === false ? chart.height : area.bottom + (clip.bottom === true ? 0 : clip.bottom)\n  };\n}\n"], "mappings": ";AAMA,SAAS,MAAM,GAAG;AAChB,SAAO,IAAI,MAAM;AACnB;AACA,IAAM,MAAM,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AACnD,SAAS,IAAI,GAAG;AACd,SAAO,IAAI,MAAM,IAAI,IAAI,GAAG,GAAG,GAAG;AACpC;AAIA,SAAS,IAAI,GAAG;AACd,SAAO,IAAI,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG;AACnC;AACA,SAAS,IAAI,GAAG;AACd,SAAO,IAAI,MAAM,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC;AACxC;AACA,SAAS,IAAI,GAAG;AACd,SAAO,IAAI,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG;AACnC;AAEA,IAAM,QAAQ,EAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAE;AAC7J,IAAM,MAAM,CAAC,GAAG,kBAAkB;AAClC,IAAM,KAAK,OAAK,IAAI,IAAI,EAAG;AAC3B,IAAM,KAAK,OAAK,KAAK,IAAI,QAAS,CAAC,IAAI,IAAI,IAAI,EAAG;AAClD,IAAM,KAAK,QAAO,IAAI,QAAS,OAAQ,IAAI;AAC3C,IAAM,UAAU,OAAK,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC;AAC5D,SAAS,SAAS,KAAK;AACrB,MAAI,MAAM,IAAI;AACd,MAAI;AACJ,MAAI,IAAI,CAAC,MAAM,KAAK;AAClB,QAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B,YAAM;AAAA,QACJ,GAAG,MAAM,MAAM,IAAI,CAAC,CAAC,IAAI;AAAA,QACzB,GAAG,MAAM,MAAM,IAAI,CAAC,CAAC,IAAI;AAAA,QACzB,GAAG,MAAM,MAAM,IAAI,CAAC,CAAC,IAAI;AAAA,QACzB,GAAG,QAAQ,IAAI,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK;AAAA,MACtC;AAAA,IACF,WAAW,QAAQ,KAAK,QAAQ,GAAG;AACjC,YAAM;AAAA,QACJ,GAAG,MAAM,IAAI,CAAC,CAAC,KAAK,IAAI,MAAM,IAAI,CAAC,CAAC;AAAA,QACpC,GAAG,MAAM,IAAI,CAAC,CAAC,KAAK,IAAI,MAAM,IAAI,CAAC,CAAC;AAAA,QACpC,GAAG,MAAM,IAAI,CAAC,CAAC,KAAK,IAAI,MAAM,IAAI,CAAC,CAAC;AAAA,QACpC,GAAG,QAAQ,IAAK,MAAM,IAAI,CAAC,CAAC,KAAK,IAAI,MAAM,IAAI,CAAC,CAAC,IAAK;AAAA,MACxD;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,QAAQ,CAAC,GAAG,MAAM,IAAI,MAAM,EAAE,CAAC,IAAI;AACzC,SAAS,UAAU,GAAG;AACpB,MAAI,IAAI,QAAQ,CAAC,IAAI,KAAK;AAC1B,SAAO,IACH,MAAM,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,MAAM,EAAE,GAAG,CAAC,IAC7C;AACN;AAEA,IAAM,SAAS;AACf,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,QAAM,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;AAC/B,QAAM,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,MAAM,OAAO,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE;AACtF,SAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1B;AACA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,QAAM,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,MAAM,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC;AACpF,SAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1B;AACA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,QAAM,MAAM,SAAS,GAAG,GAAG,GAAG;AAC9B,MAAI;AACJ,MAAI,IAAI,IAAI,GAAG;AACb,QAAI,KAAK,IAAI;AACb,SAAK;AACL,SAAK;AAAA,EACP;AACA,OAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,QAAI,CAAC,KAAK,IAAI,IAAI;AAClB,QAAI,CAAC,KAAK;AAAA,EACZ;AACA,SAAO;AACT;AACA,SAAS,SAAS,GAAG,GAAG,GAAG,GAAG,KAAK;AACjC,MAAI,MAAM,KAAK;AACb,YAAS,IAAI,KAAK,KAAM,IAAI,IAAI,IAAI;AAAA,EACtC;AACA,MAAI,MAAM,KAAK;AACb,YAAQ,IAAI,KAAK,IAAI;AAAA,EACvB;AACA,UAAQ,IAAI,KAAK,IAAI;AACvB;AACA,SAAS,QAAQ,GAAG;AAClB,QAAM,QAAQ;AACd,QAAM,IAAI,EAAE,IAAI;AAChB,QAAM,IAAI,EAAE,IAAI;AAChB,QAAM,IAAI,EAAE,IAAI;AAChB,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,KAAK,MAAM,OAAO;AACxB,MAAI,GAAG,GAAG;AACV,MAAI,QAAQ,KAAK;AACf,QAAI,MAAM;AACV,QAAI,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM;AAC/C,QAAI,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AAC5B,QAAI,IAAI,KAAK;AAAA,EACf;AACA,SAAO,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC;AAC1B;AACA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG;AACzB,UACE,MAAM,QAAQ,CAAC,IACX,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAClB,EAAE,GAAG,GAAG,CAAC,GACb,IAAI,GAAG;AACX;AACA,SAAS,QAAQ,GAAG,GAAG,GAAG;AACxB,SAAO,MAAM,UAAU,GAAG,GAAG,CAAC;AAChC;AACA,SAAS,QAAQ,GAAG,GAAG,GAAG;AACxB,SAAO,MAAM,UAAU,GAAG,GAAG,CAAC;AAChC;AACA,SAAS,QAAQ,GAAG,GAAG,GAAG;AACxB,SAAO,MAAM,UAAU,GAAG,GAAG,CAAC;AAChC;AACA,SAAS,IAAI,GAAG;AACd,UAAQ,IAAI,MAAM,OAAO;AAC3B;AACA,SAAS,SAAS,KAAK;AACrB,QAAM,IAAI,OAAO,KAAK,GAAG;AACzB,MAAI,IAAI;AACR,MAAI;AACJ,MAAI,CAAC,GAAG;AACN;AAAA,EACF;AACA,MAAI,EAAE,CAAC,MAAM,GAAG;AACd,QAAI,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AAAA,EACnC;AACA,QAAM,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AACnB,QAAM,KAAK,CAAC,EAAE,CAAC,IAAI;AACnB,QAAM,KAAK,CAAC,EAAE,CAAC,IAAI;AACnB,MAAI,EAAE,CAAC,MAAM,OAAO;AAClB,QAAI,QAAQ,GAAG,IAAI,EAAE;AAAA,EACvB,WAAW,EAAE,CAAC,MAAM,OAAO;AACzB,QAAI,QAAQ,GAAG,IAAI,EAAE;AAAA,EACvB,OAAO;AACL,QAAI,QAAQ,GAAG,IAAI,EAAE;AAAA,EACvB;AACA,SAAO;AAAA,IACL,GAAG,EAAE,CAAC;AAAA,IACN,GAAG,EAAE,CAAC;AAAA,IACN,GAAG,EAAE,CAAC;AAAA,IACN;AAAA,EACF;AACF;AACA,SAAS,OAAO,GAAG,KAAK;AACtB,MAAI,IAAI,QAAQ,CAAC;AACjB,IAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,GAAG;AACrB,MAAI,QAAQ,CAAC;AACb,IAAE,IAAI,EAAE,CAAC;AACT,IAAE,IAAI,EAAE,CAAC;AACT,IAAE,IAAI,EAAE,CAAC;AACX;AACA,SAAS,UAAU,GAAG;AACpB,MAAI,CAAC,GAAG;AACN;AAAA,EACF;AACA,QAAM,IAAI,QAAQ,CAAC;AACnB,QAAM,IAAI,EAAE,CAAC;AACb,QAAM,IAAI,IAAI,EAAE,CAAC,CAAC;AAClB,QAAM,IAAI,IAAI,EAAE,CAAC,CAAC;AAClB,SAAO,EAAE,IAAI,MACT,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,MACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;AAC3B;AAEA,IAAM,MAAM;AAAA,EACX,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACJ;AACA,IAAM,UAAU;AAAA,EACf,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,cAAc;AAAA,EACd,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,MAAM;AAAA,EACN,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,SAAS;AAAA,EACT,KAAK;AAAA,EACL,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,KAAK;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,KAAK;AAAA,EACL,MAAM;AAAA,EACN,OAAO;AAAA,EACP,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,SAAS;AAAA,EACT,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,WAAW;AAAA,EACX,KAAK;AAAA,EACL,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AAAA,EACN,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,MAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,aAAa;AAAA,EACb,IAAI;AAAA,EACJ,UAAU;AAAA,EACV,OAAO;AAAA,EACP,WAAW;AAAA,EACX,OAAO;AAAA,EACP,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,WAAW;AAAA,EACX,IAAI;AAAA,EACJ,OAAO;AACR;AACA,SAAS,SAAS;AAChB,QAAM,WAAW,CAAC;AAClB,QAAM,OAAO,OAAO,KAAK,OAAO;AAChC,QAAM,QAAQ,OAAO,KAAK,GAAG;AAC7B,MAAI,GAAG,GAAG,GAAG,IAAI;AACjB,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,SAAK,KAAK,KAAK,CAAC;AAChB,SAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,UAAI,MAAM,CAAC;AACX,WAAK,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAC;AAAA,IAC3B;AACA,QAAI,SAAS,QAAQ,EAAE,GAAG,EAAE;AAC5B,aAAS,EAAE,IAAI,CAAC,KAAK,KAAK,KAAM,KAAK,IAAI,KAAM,IAAI,GAAI;AAAA,EACzD;AACA,SAAO;AACT;AAEA,IAAI;AACJ,SAAS,UAAU,KAAK;AACtB,MAAI,CAAC,OAAO;AACV,YAAQ,OAAO;AACf,UAAM,cAAc,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,EACjC;AACA,QAAM,IAAI,MAAM,IAAI,YAAY,CAAC;AACjC,SAAO,KAAK;AAAA,IACV,GAAG,EAAE,CAAC;AAAA,IACN,GAAG,EAAE,CAAC;AAAA,IACN,GAAG,EAAE,CAAC;AAAA,IACN,GAAG,EAAE,WAAW,IAAI,EAAE,CAAC,IAAI;AAAA,EAC7B;AACF;AAEA,IAAM,SAAS;AACf,SAAS,SAAS,KAAK;AACrB,QAAM,IAAI,OAAO,KAAK,GAAG;AACzB,MAAI,IAAI;AACR,MAAI,GAAG,GAAG;AACV,MAAI,CAAC,GAAG;AACN;AAAA,EACF;AACA,MAAI,EAAE,CAAC,MAAM,GAAG;AACd,UAAM,IAAI,CAAC,EAAE,CAAC;AACd,QAAI,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,GAAG,GAAG;AAAA,EACzC;AACA,MAAI,CAAC,EAAE,CAAC;AACR,MAAI,CAAC,EAAE,CAAC;AACR,MAAI,CAAC,EAAE,CAAC;AACR,MAAI,OAAO,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG,GAAG;AACxC,MAAI,OAAO,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG,GAAG;AACxC,MAAI,OAAO,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG,GAAG;AACxC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,UAAU,GAAG;AACpB,SAAO,MACL,EAAE,IAAI,MACF,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,MACxC,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC;AAElC;AAEA,IAAM,KAAK,OAAK,KAAK,WAAY,IAAI,QAAQ,KAAK,IAAI,GAAG,IAAM,GAAG,IAAI,QAAQ;AAC9E,IAAM,OAAO,OAAK,KAAK,UAAU,IAAI,QAAQ,KAAK,KAAK,IAAI,SAAS,OAAO,GAAG;AAC9E,SAAS,YAAY,MAAM,MAAM,GAAG;AAClC,QAAM,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC;AAC1B,QAAM,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC;AAC1B,QAAM,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC;AAC1B,SAAO;AAAA,IACL,GAAG,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,IAC1C,GAAG,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,IAC1C,GAAG,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,IAC1C,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,EACjC;AACF;AAEA,SAAS,OAAO,GAAG,GAAG,OAAO;AAC3B,MAAI,GAAG;AACL,QAAI,MAAM,QAAQ,CAAC;AACnB,QAAI,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,OAAO,MAAM,IAAI,MAAM,CAAC,CAAC;AACzE,UAAM,QAAQ,GAAG;AACjB,MAAE,IAAI,IAAI,CAAC;AACX,MAAE,IAAI,IAAI,CAAC;AACX,MAAE,IAAI,IAAI,CAAC;AAAA,EACb;AACF;AACA,SAAS,MAAM,GAAG,OAAO;AACvB,SAAO,IAAI,OAAO,OAAO,SAAS,CAAC,GAAG,CAAC,IAAI;AAC7C;AACA,SAAS,WAAW,OAAO;AACzB,MAAI,IAAI,EAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAG;AACjC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,QAAI,MAAM,UAAU,GAAG;AACrB,UAAI,EAAC,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,IAAG;AAClD,UAAI,MAAM,SAAS,GAAG;AACpB,UAAE,IAAI,IAAI,MAAM,CAAC,CAAC;AAAA,MACpB;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,MAAM,OAAO,EAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAC,CAAC;AACzC,MAAE,IAAI,IAAI,EAAE,CAAC;AAAA,EACf;AACA,SAAO;AACT;AACA,SAAS,cAAc,KAAK;AAC1B,MAAI,IAAI,OAAO,CAAC,MAAM,KAAK;AACzB,WAAO,SAAS,GAAG;AAAA,EACrB;AACA,SAAO,SAAS,GAAG;AACrB;AACA,IAAM,QAAN,MAAM,OAAM;AAAA,EACV,YAAY,OAAO;AACjB,QAAI,iBAAiB,QAAO;AAC1B,aAAO;AAAA,IACT;AACA,UAAM,OAAO,OAAO;AACpB,QAAI;AACJ,QAAI,SAAS,UAAU;AACrB,UAAI,WAAW,KAAK;AAAA,IACtB,WAAW,SAAS,UAAU;AAC5B,UAAI,SAAS,KAAK,KAAK,UAAU,KAAK,KAAK,cAAc,KAAK;AAAA,IAChE;AACA,SAAK,OAAO;AACZ,SAAK,SAAS,CAAC,CAAC;AAAA,EAClB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM;AACR,QAAI,IAAI,MAAM,KAAK,IAAI;AACvB,QAAI,GAAG;AACL,QAAE,IAAI,IAAI,EAAE,CAAC;AAAA,IACf;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,IAAI,KAAK;AACX,SAAK,OAAO,WAAW,GAAG;AAAA,EAC5B;AAAA,EACA,YAAY;AACV,WAAO,KAAK,SAAS,UAAU,KAAK,IAAI,IAAI;AAAA,EAC9C;AAAA,EACA,YAAY;AACV,WAAO,KAAK,SAAS,UAAU,KAAK,IAAI,IAAI;AAAA,EAC9C;AAAA,EACA,YAAY;AACV,WAAO,KAAK,SAAS,UAAU,KAAK,IAAI,IAAI;AAAA,EAC9C;AAAA,EACA,IAAIA,QAAO,QAAQ;AACjB,QAAIA,QAAO;AACT,YAAM,KAAK,KAAK;AAChB,YAAM,KAAKA,OAAM;AACjB,UAAI;AACJ,YAAM,IAAI,WAAW,KAAK,MAAM;AAChC,YAAM,IAAI,IAAI,IAAI;AAClB,YAAM,IAAI,GAAG,IAAI,GAAG;AACpB,YAAM,OAAO,IAAI,MAAM,KAAK,KAAK,IAAI,MAAM,IAAI,IAAI,MAAM,KAAK;AAC9D,WAAK,IAAI;AACT,SAAG,IAAI,MAAO,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AACtC,SAAG,IAAI,MAAO,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AACtC,SAAG,IAAI,MAAO,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AACtC,SAAG,IAAI,IAAI,GAAG,KAAK,IAAI,KAAK,GAAG;AAC/B,WAAK,MAAM;AAAA,IACb;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAYA,QAAO,GAAG;AACpB,QAAIA,QAAO;AACT,WAAK,OAAO,YAAY,KAAK,MAAMA,OAAM,MAAM,CAAC;AAAA,IAClD;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,WAAO,IAAI,OAAM,KAAK,GAAG;AAAA,EAC3B;AAAA,EACA,MAAM,GAAG;AACP,SAAK,KAAK,IAAI,IAAI,CAAC;AACnB,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,OAAO;AACb,UAAM,MAAM,KAAK;AACjB,QAAI,KAAK,IAAI;AACb,WAAO;AAAA,EACT;AAAA,EACA,YAAY;AACV,UAAM,MAAM,KAAK;AACjB,UAAM,MAAM,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI;AAC3D,QAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,OAAO;AACb,UAAM,MAAM,KAAK;AACjB,QAAI,KAAK,IAAI;AACb,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,UAAM,IAAI,KAAK;AACf,MAAE,IAAI,MAAM,EAAE;AACd,MAAE,IAAI,MAAM,EAAE;AACd,MAAE,IAAI,MAAM,EAAE;AACd,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,OAAO;AACb,WAAO,KAAK,MAAM,GAAG,KAAK;AAC1B,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO;AACZ,WAAO,KAAK,MAAM,GAAG,CAAC,KAAK;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,SAAS,OAAO;AACd,WAAO,KAAK,MAAM,GAAG,KAAK;AAC1B,WAAO;AAAA,EACT;AAAA,EACA,WAAW,OAAO;AAChB,WAAO,KAAK,MAAM,GAAG,CAAC,KAAK;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,OAAO,KAAK;AACV,WAAO,KAAK,MAAM,GAAG;AACrB,WAAO;AAAA,EACT;AACF;;;AC5jBO,SAASC,OAAO;AACrB;AAMK,IAAMC,MAAO,uBAAM;AACxB,MAAIC,KAAK;AACT,SAAO,MAAMA;AACf,GAAA;AAOO,SAASC,cAAcC,OAA2C;AACvE,SAAOA,UAAU,QAAQA,UAAUC;AACrC;AAOO,SAASC,QAAqBF,OAA8B;AACjE,MAAIG,MAAMD,WAAWC,MAAMD,QAAQF,KAAQ,GAAA;AACzC,WAAO;;AAET,QAAMI,OAAOC,OAAOC,UAAUC,SAASC,KAAKR,KAAAA;AAC5C,MAAII,KAAKK,MAAM,GAAG,CAAA,MAAO,aAAaL,KAAKK,MAAM,EAAC,MAAO,UAAU;AACjE,WAAO;;AAET,SAAO;AACT;AAOO,SAASC,SAASV,OAAoC;AAC3D,SAAOA,UAAU,QAAQK,OAAOC,UAAUC,SAASC,KAAKR,KAAW,MAAA;AACrE;AAMA,SAASW,eAAeX,OAAiC;AACvD,UAAQ,OAAOA,UAAU,YAAYA,iBAAiBY,WAAWC,SAAS,CAACb,KAAAA;AAC7E;AAUO,SAASc,gBAAgBd,OAAgBe,cAAsB;AACpE,SAAOJ,eAAeX,KAASA,IAAAA,QAAQe;AACzC;AAOO,SAASC,eAAkBhB,OAAsBe,cAAiB;AACvE,SAAO,OAAOf,UAAU,cAAce,eAAef;AACvD;IAEaiB,eAAe,CAACjB,OAAwBkB,cACnD,OAAOlB,UAAU,YAAYA,MAAMmB,SAAS,GAAA,IAC1CC,WAAWpB,KAAAA,IAAS,MAClB,CAACA,QAAQkB;IAEFG,cAAc,CAACrB,OAAwBkB,cAClD,OAAOlB,UAAU,YAAYA,MAAMmB,SAAS,GAAA,IAC1CC,WAAWpB,KAAAA,IAAS,MAAMkB,YACxB,CAAClB;AASA,SAASsB,SACdC,IACAC,MACAC,SACe;AACf,MAAIF,MAAM,OAAOA,GAAGf,SAAS,YAAY;AACvC,WAAOe,GAAGG,MAAMD,SAASD,IAAAA;;AAE7B;AAuBO,SAASG,KACdC,UACAL,IACAE,SACAI,SACA;AACA,MAAIC,GAAWC,KAAaC;AAC5B,MAAI9B,QAAQ0B,QAAW,GAAA;AACrBG,UAAMH,SAASK;AACf,QAAIJ,SAAS;AACX,WAAKC,IAAIC,MAAM,GAAGD,KAAK,GAAGA,KAAK;AAC7BP,WAAGf,KAAKiB,SAASG,SAASE,CAAAA,GAAIA,CAAAA;MAChC;WACK;AACL,WAAKA,IAAI,GAAGA,IAAIC,KAAKD,KAAK;AACxBP,WAAGf,KAAKiB,SAASG,SAASE,CAAAA,GAAIA,CAAAA;MAChC;;aAEOpB,SAASkB,QAAW,GAAA;AAC7BI,WAAO3B,OAAO2B,KAAKJ,QAAAA;AACnBG,UAAMC,KAAKC;AACX,SAAKH,IAAI,GAAGA,IAAIC,KAAKD,KAAK;AACxBP,SAAGf,KAAKiB,SAASG,SAASI,KAAKF,CAAAA,CAAE,GAAGE,KAAKF,CAAE,CAAA;IAC7C;;AAEJ;AAQO,SAASI,eAAeC,IAAuBC,IAAuB;AAC3E,MAAIN,GAAWO,MAAcC,IAAqBC;AAElD,MAAI,CAACJ,MAAM,CAACC,MAAMD,GAAGF,WAAWG,GAAGH,QAAQ;AACzC,WAAO;;AAGT,OAAKH,IAAI,GAAGO,OAAOF,GAAGF,QAAQH,IAAIO,MAAM,EAAEP,GAAG;AAC3CQ,SAAKH,GAAGL,CAAE;AACVS,SAAKH,GAAGN,CAAE;AAEV,QAAIQ,GAAGE,iBAAiBD,GAAGC,gBAAgBF,GAAGG,UAAUF,GAAGE,OAAO;AAChE,aAAO;;EAEX;AAEA,SAAO;AACT;AAMO,SAASC,OAASC,QAAc;AACrC,MAAIzC,QAAQyC,MAAS,GAAA;AACnB,WAAOA,OAAOC,IAAIF,MAAAA;;AAGpB,MAAIhC,SAASiC,MAAS,GAAA;AACpB,UAAME,SAASxC,uBAAOyC,OAAO,IAAI;AACjC,UAAMd,OAAO3B,OAAO2B,KAAKW,MAAAA;AACzB,UAAMI,OAAOf,KAAKC;AAClB,QAAIe,IAAI;AAER,WAAOA,IAAID,MAAM,EAAEC,GAAG;AACpBH,aAAOb,KAAKgB,CAAAA,CAAE,IAAIN,OAAMC,OAAOX,KAAKgB,CAAAA,CAAE,CAAC;IACzC;AAEA,WAAOH;;AAGT,SAAOF;AACT;AAEA,SAASM,WAAWC,KAAa;AAC/B,SAAO;IAAC;IAAa;IAAa;IAAeC,QAAQD,GAAAA,MAAS;AACpE;AAOO,SAASE,QAAQF,KAAaL,QAAmBF,QAAmBU,SAAoB;AAC7F,MAAI,CAACJ,WAAWC,GAAM,GAAA;AACpB;;AAGF,QAAMI,OAAOT,OAAOK,GAAI;AACxB,QAAMK,OAAOZ,OAAOO,GAAI;AAExB,MAAIxC,SAAS4C,IAAS5C,KAAAA,SAAS6C,IAAO,GAAA;AAEpCC,UAAMF,MAAMC,MAAMF,OAAAA;SACb;AACLR,WAAOK,GAAI,IAAGR,OAAMa,IAAAA;;AAExB;AA0BO,SAASC,MAASX,QAAWF,QAAqBU,SAAmC;AAC1F,QAAMI,UAAUvD,QAAQyC,MAAAA,IAAUA,SAAS;IAACA;EAAO;AACnD,QAAMN,OAAOoB,QAAQxB;AAErB,MAAI,CAACvB,SAASmC,MAAS,GAAA;AACrB,WAAOA;;AAGTQ,YAAUA,WAAW,CAAA;AACrB,QAAMK,SAASL,QAAQK,UAAUN;AACjC,MAAIO;AAEJ,WAAS7B,IAAI,GAAGA,IAAIO,MAAM,EAAEP,GAAG;AAC7B6B,cAAUF,QAAQ3B,CAAE;AACpB,QAAI,CAACpB,SAASiD,OAAU,GAAA;AACtB;;AAGF,UAAM3B,OAAO3B,OAAO2B,KAAK2B,OAAAA;AACzB,aAASX,IAAI,GAAGD,OAAOf,KAAKC,QAAQe,IAAID,MAAM,EAAEC,GAAG;AACjDU,aAAO1B,KAAKgB,CAAE,GAAEH,QAAQc,SAASN,OAAAA;IACnC;EACF;AAEA,SAAOR;AACT;AAgBO,SAASe,QAAWf,QAAWF,QAAgC;AAEpE,SAAOa,MAASX,QAAQF,QAAQ;IAACe,QAAQG;EAAS,CAAA;AACpD;AAMO,SAASA,UAAUX,KAAaL,QAAmBF,QAAmB;AAC3E,MAAI,CAACM,WAAWC,GAAM,GAAA;AACpB;;AAGF,QAAMI,OAAOT,OAAOK,GAAI;AACxB,QAAMK,OAAOZ,OAAOO,GAAI;AAExB,MAAIxC,SAAS4C,IAAS5C,KAAAA,SAAS6C,IAAO,GAAA;AACpCK,YAAQN,MAAMC,IAAAA;aACL,CAAClD,OAAOC,UAAUwD,eAAetD,KAAKqC,QAAQK,GAAM,GAAA;AAC7DL,WAAOK,GAAI,IAAGR,OAAMa,IAAAA;;AAExB;AAaA,IAAMQ,eAAe;;EAEnB,IAAIC,CAAAA,MAAKA;;EAETC,GAAGC,CAAAA,MAAKA,EAAED;EACVE,GAAGD,CAAAA,MAAKA,EAAEC;AACZ;AAKO,SAASC,UAAUC,KAAa;AACrC,QAAMC,QAAQD,IAAIE,MAAM,GAAA;AACxB,QAAMC,OAAiB,CAAA;AACvB,MAAIC,MAAM;AACV,aAAWC,QAAQJ,OAAO;AACxBG,WAAOC;AACP,QAAID,IAAIE,SAAS,IAAO,GAAA;AACtBF,YAAMA,IAAIG,MAAM,GAAG,EAAM,IAAA;WACpB;AACLJ,WAAKK,KAAKJ,GAAAA;AACVA,YAAM;;EAEV;AACA,SAAOD;AACT;AAEA,SAASM,gBAAgBT,KAAa;AACpC,QAAMG,OAAOJ,UAAUC,GAAAA;AACvB,SAAOU,CAAAA,QAAO;AACZ,eAAWC,KAAKR,MAAM;AACpB,UAAIQ,MAAM,IAAI;AAGZ;;AAEFD,YAAMA,OAAOA,IAAIC,CAAE;IACrB;AACA,WAAOD;EACT;AACF;AAEO,SAASE,iBAAiBF,KAAgBV,KAAkB;AACjE,QAAMa,WAAWnB,aAAaM,GAAI,MAAKN,aAAaM,GAAAA,IAAOS,gBAAgBT,GAAG;AAC9E,SAAOa,SAASH,GAAAA;AAClB;AAKO,SAASI,YAAYC,KAAa;AACvC,SAAOA,IAAIC,OAAO,CAAA,EAAGC,YAAW,IAAKF,IAAIR,MAAM,CAAA;AACjD;IAGaW,UAAU,CAACC,UAAmB,OAAOA,UAAU;IAE/CC,aAAa,CAACD,UAAqD,OAAOA,UAAU;AAGpFE,IAAAA,YAAY,CAAIC,GAAWC,MAAc;AACpD,MAAID,EAAEE,SAASD,EAAEC,MAAM;AACrB,WAAO;;AAGT,aAAWC,QAAQH,GAAG;AACpB,QAAI,CAACC,EAAEG,IAAID,IAAO,GAAA;AAChB,aAAO;;EAEX;AAEA,SAAO;AACT;AAMO,SAASE,cAAcC,GAAe;AAC3C,SAAOA,EAAEC,SAAS,aAAaD,EAAEC,SAAS,WAAWD,EAAEC,SAAS;AAClE;ACvZO,IAAMC,KAAKC,KAAKD;AAChB,IAAME,MAAM,IAAIF;AAChB,IAAMG,QAAQD,MAAMF;AACdI,IAAAA,WAAWC,OAAOC;AACxB,IAAMC,cAAcP,KAAK;AACzB,IAAMQ,UAAUR,KAAK;AACrB,IAAMS,aAAaT,KAAK;AAClBU,IAAAA,gBAAgBV,KAAK,IAAI;AAEzBW,IAAAA,QAAQV,KAAKU;AACbC,IAAAA,OAAOX,KAAKW;AAElB,SAASC,aAAa/C,GAAWE,GAAW8C,SAAiB;AAClE,SAAOb,KAAKc,IAAIjD,IAAIE,CAAK8C,IAAAA;AAC3B;AAKO,SAASE,QAAQC,OAAe;AACrC,QAAMC,eAAejB,KAAKkB,MAAMF,KAAAA;AAChCA,UAAQJ,aAAaI,OAAOC,cAAcD,QAAQ,GAAA,IAAQC,eAAeD;AACzE,QAAMG,YAAYnB,KAAKoB,IAAI,IAAIpB,KAAKqB,MAAMX,MAAMM,KAAAA,CAAAA,CAAAA;AAChD,QAAMM,WAAWN,QAAQG;AACzB,QAAMI,eAAeD,YAAY,IAAI,IAAIA,YAAY,IAAI,IAAIA,YAAY,IAAI,IAAI;AACjF,SAAOC,eAAeJ;AACxB;AAMO,SAASK,WAAWpC,OAAe;AACxC,QAAMqC,SAAmB,CAAA;AACzB,QAAMC,OAAO1B,KAAK0B,KAAKtC,KAAAA;AACvB,MAAIuC;AAEJ,OAAKA,IAAI,GAAGA,IAAID,MAAMC,KAAK;AACzB,QAAIvC,QAAQuC,MAAM,GAAG;AACnBF,aAAOhD,KAAKkD,CAAAA;AACZF,aAAOhD,KAAKW,QAAQuC,CAAAA;;EAExB;AACA,MAAID,UAAUA,OAAO,IAAI;AACvBD,WAAOhD,KAAKiD,IAAAA;;AAGdD,SAAOG,KAAK,CAACrC,GAAGC,MAAMD,IAAIC,CAAAA,EAAGqC,IAAG;AAChC,SAAOJ;AACT;AAKA,SAASK,eAAeC,GAAY;AAClC,SAAO,OAAOA,MAAM,YAAa,OAAOA,MAAM,YAAYA,MAAM,QAAQ,EAAEC,OAAOC,eAAeF,KAAK,cAAcA,KAAK,aAAaA;AACvI;AAEO,SAASG,SAASH,GAAyB;AAChD,SAAO,CAACD,eAAeC,CAAAA,KAAM,CAACI,MAAMC,WAAWL,CAAAA,CAAAA,KAAiBM,SAASN,CAAAA;AAC3E;AAEO,SAASO,YAAYzE,GAAWgD,SAAiB;AACtD,QAAM0B,UAAUvC,KAAKkB,MAAMrD,CAAAA;AAC3B,SAAO,UAAYgD,WAAYhD,KAAQ0E,UAAU1B,WAAYhD;AAC/D;AAKO,SAAS2E,mBACdC,OACAC,QACAC,UACA;AACA,MAAIhB,GAAWiB,MAAcxD;AAE7B,OAAKuC,IAAI,GAAGiB,OAAOH,MAAMI,QAAQlB,IAAIiB,MAAMjB,KAAK;AAC9CvC,YAAQqD,MAAMd,CAAE,EAACgB,QAAS;AAC1B,QAAI,CAACR,MAAM/C,KAAQ,GAAA;AACjBsD,aAAOI,MAAM9C,KAAK8C,IAAIJ,OAAOI,KAAK1D,KAAAA;AAClCsD,aAAOK,MAAM/C,KAAK+C,IAAIL,OAAOK,KAAK3D,KAAAA;;EAEtC;AACF;AAEO,SAAS4D,UAAUC,SAAiB;AACzC,SAAOA,WAAWlD,KAAK;AACzB;AAEO,SAASmD,UAAUC,SAAiB;AACzC,SAAOA,WAAW,MAAMpD;AAC1B;AASO,SAASqD,eAAevF,GAAW;AACxC,MAAI,CAACwF,eAAexF,CAAI,GAAA;AACtB;;AAEF,MAAIgC,IAAI;AACR,MAAIyD,IAAI;AACR,SAAOtD,KAAKkB,MAAMrD,IAAIgC,CAAAA,IAAKA,MAAMhC,GAAG;AAClCgC,SAAK;AACLyD;EACF;AACA,SAAOA;AACT;AAGO,SAASC,kBACdC,aACAC,YACA;AACA,QAAMC,sBAAsBD,WAAW5F,IAAI2F,YAAY3F;AACvD,QAAM8F,sBAAsBF,WAAW1F,IAAIyF,YAAYzF;AACvD,QAAM6F,2BAA2B5D,KAAK0B,KAAKgC,sBAAsBA,sBAAsBC,sBAAsBA,mBAAAA;AAE7G,MAAIE,QAAQ7D,KAAK8D,MAAMH,qBAAqBD,mBAAAA;AAE5C,MAAIG,QAAS,OAAO9D,IAAK;AACvB8D,aAAS5D;;AAGX,SAAO;IACL4D;IACAE,UAAUH;EACZ;AACF;AAEO,SAASI,sBAAsBC,KAAYC,KAAY;AAC5D,SAAOlE,KAAK0B,KAAK1B,KAAKoB,IAAI8C,IAAIrG,IAAIoG,IAAIpG,GAAG,CAAA,IAAKmC,KAAKoB,IAAI8C,IAAInG,IAAIkG,IAAIlG,GAAG,CAAA,CAAA;AACxE;AAMO,SAASoG,WAAW5E,GAAWC,GAAW;AAC/C,UAAQD,IAAIC,IAAIU,SAASD,MAAMF;AACjC;AAMO,SAASqE,gBAAgB7E,GAAW;AACzC,UAAQA,IAAIU,MAAMA,OAAOA;AAC3B;AAKO,SAASoE,cAAcR,OAAeS,OAAeC,KAAaC,uBAAiC;AACxG,QAAMjF,IAAI6E,gBAAgBP,KAAAA;AAC1B,QAAMY,IAAIL,gBAAgBE,KAAAA;AAC1B,QAAMzE,IAAIuE,gBAAgBG,GAAAA;AAC1B,QAAMG,eAAeN,gBAAgBK,IAAIlF,CAAAA;AACzC,QAAMoF,aAAaP,gBAAgBvE,IAAIN,CAAAA;AACvC,QAAMqF,eAAeR,gBAAgB7E,IAAIkF,CAAAA;AACzC,QAAMI,aAAaT,gBAAgB7E,IAAIM,CAAAA;AACvC,SAAON,MAAMkF,KAAKlF,MAAMM,KAAM2E,yBAAyBC,MAAM5E,KACvD6E,eAAeC,cAAcC,eAAeC;AACpD;AASO,SAASC,YAAY1F,OAAe0D,KAAaC,KAAa;AACnE,SAAO/C,KAAK+C,IAAID,KAAK9C,KAAK8C,IAAIC,KAAK3D,KAAAA,CAAAA;AACrC;AAMO,SAAS2F,YAAY3F,OAAe;AACzC,SAAO0F,YAAY1F,OAAO,QAAQ,KAAA;AACpC;AASO,SAAS4F,WAAW5F,OAAekF,OAAeC,KAAa1D,UAAU,MAAM;AACpF,SAAOzB,SAASY,KAAK8C,IAAIwB,OAAOC,GAAAA,IAAO1D,WAAWzB,SAASY,KAAK+C,IAAIuB,OAAOC,GAAO1D,IAAAA;AACpF;AC3LO,SAASoE,QACdC,OACA9F,OACA+F,KACA;AACAA,QAAMA,QAAQ,CAACC,UAAUF,MAAME,KAAAA,IAAShG;AACxC,MAAIiG,KAAKH,MAAMrC,SAAS;AACxB,MAAIyC,KAAK;AACT,MAAIC;AAEJ,SAAOF,KAAKC,KAAK,GAAG;AAClBC,UAAOD,KAAKD,MAAO;AACnB,QAAIF,IAAII,GAAM,GAAA;AACZD,WAAKC;WACA;AACLF,WAAKE;;EAET;AAEA,SAAO;IAACD;IAAID;EAAE;AAChB;AAUO,IAAMG,eAAe,CAC1BN,OACAjH,KACAmB,OACAqG,SAEAR,QAAQC,OAAO9F,OAAOqG,OAClBL,CAAAA,UAAS;AACT,QAAMM,KAAKR,MAAME,KAAAA,EAAOnH,GAAI;AAC5B,SAAOyH,KAAKtG,SAASsG,OAAOtG,SAAS8F,MAAME,QAAQ,CAAA,EAAGnH,GAAAA,MAASmB;IAE/DgG,CAAAA,UAASF,MAAME,KAAAA,EAAOnH,GAAAA,IAAOmB,KAAK;AAS3BuG,IAAAA,gBAAgB,CAC3BT,OACAjH,KACAmB,UAEA6F,QAAQC,OAAO9F,OAAOgG,CAAAA,UAASF,MAAME,KAAAA,EAAOnH,GAAAA,KAAQmB,KAAO;AAStD,SAASwG,eAAeC,QAAkB/C,KAAaC,KAAa;AACzE,MAAIuB,QAAQ;AACZ,MAAIC,MAAMsB,OAAOhD;AAEjB,SAAOyB,QAAQC,OAAOsB,OAAOvB,KAAAA,IAASxB,KAAK;AACzCwB;EACF;AACA,SAAOC,MAAMD,SAASuB,OAAOtB,MAAM,CAAA,IAAKxB,KAAK;AAC3CwB;EACF;AAEA,SAAOD,QAAQ,KAAKC,MAAMsB,OAAOhD,SAC7BgD,OAAOrH,MAAM8F,OAAOC,GAAAA,IACpBsB;AACN;AAEA,IAAMC,cAAc;EAAC;EAAQ;EAAO;EAAS;EAAU;AAAU;AAgB1D,SAASC,kBAAkBtD,OAAOuD,UAAU;AACjD,MAAIvD,MAAMwD,UAAU;AAClBxD,UAAMwD,SAASC,UAAUzH,KAAKuH,QAAAA;AAC9B;;AAGFG,SAAOC,eAAe3D,OAAO,YAAY;IACvC4D,cAAc;IACdC,YAAY;IACZlH,OAAO;MACL8G,WAAW;QAACF;MAAS;IACvB;EACF,CAAA;AAEAF,cAAYS,QAAQ,CAACtI,QAAQ;AAC3B,UAAMuI,SAAS,YAAYzH,YAAYd,GAAAA;AACvC,UAAMwI,OAAOhE,MAAMxE,GAAI;AAEvBkI,WAAOC,eAAe3D,OAAOxE,KAAK;MAChCoI,cAAc;MACdC,YAAY;MACZlH,SAASsH,MAAM;AACb,cAAMC,MAAMF,KAAKG,MAAM,MAAMF,IAAAA;AAE7BjE,cAAMwD,SAASC,UAAUK,QAAQ,CAACM,WAAW;AAC3C,cAAI,OAAOA,OAAOL,MAAAA,MAAY,YAAY;AACxCK,mBAAOL,MAAAA,EAAWE,GAAAA,IAAAA;;QAEtB,CAAA;AAEA,eAAOC;MACT;IACF,CAAA;EACF,CAAA;AACF;AAQO,SAASG,oBAAoBrE,OAAOuD,UAAU;AACnD,QAAMe,OAAOtE,MAAMwD;AACnB,MAAI,CAACc,MAAM;AACT;;AAGF,QAAMb,YAAYa,KAAKb;AACvB,QAAMd,QAAQc,UAAUc,QAAQhB,QAAAA;AAChC,MAAIZ,UAAU,IAAI;AAChBc,cAAUe,OAAO7B,OAAO,CAAA;;AAG1B,MAAIc,UAAUrD,SAAS,GAAG;AACxB;;AAGFiD,cAAYS,QAAQ,CAACtI,QAAQ;AAC3B,WAAOwE,MAAMxE,GAAI;EACnB,CAAA;AAEA,SAAOwE,MAAMwD;AACf;AAKO,SAASiB,aAAgBC,OAAY;AAC1C,QAAMC,OAAM,IAAIC,IAAOF,KAAAA;AAEvB,MAAIC,KAAI3H,SAAS0H,MAAMtE,QAAQ;AAC7B,WAAOsE;;AAGT,SAAOG,MAAMC,KAAKH,IAAAA;AACpB;AClLaI,IAAAA,mBAAoB,WAAW;AAC1C,MAAI,OAAOC,WAAW,aAAa;AACjC,WAAO,SAASC,WAAU;AACxB,aAAOA,UAAAA;IACT;;AAEF,SAAOD,OAAOE;AAChB,EAAK;AAME,SAASC,UACdC,IACAC,SACA;AACA,MAAIC,YAAY,CAAA;AAChB,MAAIC,UAAU;AAEd,SAAO,YAAYC,MAAa;AAE9BF,gBAAYE;AACZ,QAAI,CAACD,SAAS;AACZA,gBAAU;AACVR,uBAAiBU,KAAKT,QAAQ,MAAM;AAClCO,kBAAU;AACVH,WAAGM,MAAML,SAASC,SAAAA;MACpB,CAAA;;EAEJ;AACF;AAKO,SAASK,SAAmCP,IAA8BQ,OAAe;AAC9F,MAAIC;AACJ,SAAO,YAAYL,MAAa;AAC9B,QAAII,OAAO;AACTE,mBAAaD,OAAAA;AACbA,gBAAUE,WAAWX,IAAIQ,OAAOJ,IAAAA;WAC3B;AACLJ,SAAGM,MAAM,MAAMF,IAAAA;;AAEjB,WAAOI;EACT;AACF;AAMO,IAAMI,qBAAqB,CAACC,UAAsCA,UAAU,UAAU,SAASA,UAAU,QAAQ,UAAU;AAMrHC,IAAAA,iBAAiB,CAACD,OAAmCE,OAAeC,QAAgBH,UAAU,UAAUE,QAAQF,UAAU,QAAQG,OAAOD,QAAQC,OAAO;AAMxJC,IAAAA,SAAS,CAACJ,OAAoCK,MAAcC,OAAeC,QAAiB;AACvG,QAAMC,QAAQD,MAAM,SAAS;AAC7B,SAAOP,UAAUQ,QAAQF,QAAQN,UAAU,YAAYK,OAAOC,SAAS,IAAID;AAC7E;AAMO,SAASI,iCAAiCC,MAAqCC,QAAwBC,oBAA6B;AACzI,QAAMC,aAAaF,OAAOG;AAE1B,MAAIZ,QAAQ;AACZ,MAAIa,QAAQF;AAEZ,MAAIH,KAAKM,SAAS;AAChB,UAAM,EAACC,QAAQC,QAAQC,QAAAA,IAAWT;AAClC,UAAMU,WAAWV,KAAKW,UAAUX,KAAKW,QAAQC,UAAUZ,KAAKW,QAAQC,QAAQF,WAAW,OAAO;AAC9F,UAAMG,OAAON,OAAOM;AACpB,UAAM,EAACC,KAAKC,KAAKC,YAAYC,WAAU,IAAIV,OAAOW,cAAa;AAE/D,QAAIF,YAAY;AACdxB,cAAQ2B,KAAKL;;QAEXM,aAAaX,SAASI,MAAMC,GAAKO,EAAAA;;QAEjCnB,qBAAqBC,aAAaiB,aAAanB,QAAQY,MAAMN,OAAOe,iBAAiBR,GAAAA,CAAAA,EAAMO;MAAE;AAC/F,UAAIX,UAAU;AACZ,cAAMa,sBAAuBd,QAC1Be,MAAM,GAAGhC,QAAQ,CAAA,EACjBiC,QAAO,EACPC,UACCC,CAAAA,UAAS,CAACC,cAAcD,MAAMnB,OAAOK,IAAI,CAAC,CAAA;AAC9CrB,iBAAS2B,KAAKJ,IAAI,GAAGQ,mBAAAA;;AAEvB/B,cAAQqC,YAAYrC,OAAO,GAAGW,aAAa,CAAA;;AAE7C,QAAIc,YAAY;AACd,UAAIxB,MAAM0B,KAAKJ;;QAEbK,aAAaX,SAASF,OAAOM,MAAME,KAAK,IAAI,EAAEe,KAAK;;QAEnD5B,qBAAqB,IAAIkB,aAAanB,QAAQY,MAAMN,OAAOe,iBAAiBP,GAAAA,GAAM,IAAI,EAAEe,KAAK;MAAC;AAChG,UAAIpB,UAAU;AACZ,cAAMqB,sBAAuBtB,QAC1Be,MAAM/B,MAAM,CAAA,EACZiC,UACCC,CAAAA,UAAS,CAACC,cAAcD,MAAMnB,OAAOK,IAAI,CAAC,CAAA;AAC9CpB,eAAO0B,KAAKJ,IAAI,GAAGgB,mBAAAA;;AAErB1B,cAAQwB,YAAYpC,KAAKD,OAAOW,UAAcX,IAAAA;WACzC;AACLa,cAAQF,aAAaX;;;AAIzB,SAAO;IAACA;IAAOa;EAAK;AACtB;AAQO,SAAS2B,oBAAoBhC,MAAM;AACxC,QAAM,EAACiC,QAAQC,QAAQC,aAAAA,IAAgBnC;AACvC,QAAMoC,YAAY;IAChBC,MAAMJ,OAAOnB;IACbwB,MAAML,OAAOlB;IACbwB,MAAML,OAAOpB;IACb0B,MAAMN,OAAOnB;EACf;AACA,MAAI,CAACoB,cAAc;AACjBnC,SAAKmC,eAAeC;AACpB,WAAO;;AAET,QAAMK,UAAUN,aAAaE,SAASJ,OAAOnB,OAC1CqB,aAAaG,SAASL,OAAOlB,OAC7BoB,aAAaI,SAASL,OAAOpB,OAC7BqB,aAAaK,SAASN,OAAOnB;AAEhC2B,SAAOC,OAAOR,cAAcC,SAAAA;AAC5B,SAAOK;AACT;AChKA,IAAMG,SAAS,CAACC,MAAcA,MAAM,KAAKA,MAAM;AAC/C,IAAMC,YAAY,CAACD,GAAWE,GAAWC,MAAc,EAAE7B,KAAK8B,IAAI,GAAG,MAAMJ,KAAK,EAAM1B,IAAAA,KAAK+B,KAAKL,IAAIE,KAAKI,MAAMH,CAAC;AAChH,IAAMI,aAAa,CAACP,GAAWE,GAAWC,MAAc7B,KAAK8B,IAAI,GAAG,MAAMJ,CAAK1B,IAAAA,KAAK+B,KAAKL,IAAIE,KAAKI,MAAMH,CAAK,IAAA;AAM5G,IACKK,UAAU;EACdC,QAAQ,CAACT,MAAcA;EAEvBU,YAAY,CAACV,MAAcA,IAAIA;EAE/BW,aAAa,CAACX,MAAc,CAACA,KAAKA,IAAI;EAEtCY,eAAe,CAACZ,OAAgBA,KAAK,OAAO,IACxC,MAAMA,IAAIA,IACV,QAAS,EAAEA,KAAMA,IAAI,KAAK;EAE9Ba,aAAa,CAACb,MAAcA,IAAIA,IAAIA;EAEpCc,cAAc,CAACd,OAAeA,KAAK,KAAKA,IAAIA,IAAI;EAEhDe,gBAAgB,CAACf,OAAgBA,KAAK,OAAO,IACzC,MAAMA,IAAIA,IAAIA,IACd,QAAQA,KAAK,KAAKA,IAAIA,IAAI;EAE9BgB,aAAa,CAAChB,MAAcA,IAAIA,IAAIA,IAAIA;EAExCiB,cAAc,CAACjB,MAAc,GAAGA,KAAK,KAAKA,IAAIA,IAAIA,IAAI;EAEtDkB,gBAAgB,CAAClB,OAAgBA,KAAK,OAAO,IACzC,MAAMA,IAAIA,IAAIA,IAAIA,IAClB,SAASA,KAAK,KAAKA,IAAIA,IAAIA,IAAI;EAEnCmB,aAAa,CAACnB,MAAcA,IAAIA,IAAIA,IAAIA,IAAIA;EAE5CoB,cAAc,CAACpB,OAAeA,KAAK,KAAKA,IAAIA,IAAIA,IAAIA,IAAI;EAExDqB,gBAAgB,CAACrB,OAAgBA,KAAK,OAAO,IACzC,MAAMA,IAAIA,IAAIA,IAAIA,IAAIA,IACtB,QAAQA,KAAK,KAAKA,IAAIA,IAAIA,IAAIA,IAAI;EAEtCsB,YAAY,CAACtB,MAAc,CAAC1B,KAAKiD,IAAIvB,IAAIwB,OAAW,IAAA;EAEpDC,aAAa,CAACzB,MAAc1B,KAAK+B,IAAIL,IAAIwB,OAAAA;EAEzCE,eAAe,CAAC1B,MAAc,QAAQ1B,KAAKiD,IAAII,KAAK3B,CAAAA,IAAK;EAEzD4B,YAAY,CAAC5B,MAAc,MAAO,IAAK,IAAI1B,KAAK8B,IAAI,GAAG,MAAMJ,IAAI,EAAG;EAEpE6B,aAAa,CAAC7B,MAAc,MAAO,IAAK,IAAI,CAAC1B,KAAK8B,IAAI,GAAG,MAAMJ,CAAAA,IAAK;EAEpE8B,eAAe,CAAC9B,MAAcD,OAAOC,CAAAA,IAAKA,IAAIA,IAAI,MAC9C,MAAM1B,KAAK8B,IAAI,GAAG,MAAMJ,IAAI,IAAI,EAAA,IAChC,OAAO,CAAC1B,KAAK8B,IAAI,GAAG,OAAOJ,IAAI,IAAI,EAAA,IAAM;EAE7C+B,YAAY,CAAC/B,MAAc,KAAM,IAAKA,IAAI,EAAE1B,KAAK0D,KAAK,IAAIhC,IAAIA,CAAAA,IAAK;EAEnEiC,aAAa,CAACjC,MAAc1B,KAAK0D,KAAK,KAAKhC,KAAK,KAAKA,CAAAA;EAErDkC,eAAe,CAAClC,OAAgBA,KAAK,OAAO,IACxC,QAAQ1B,KAAK0D,KAAK,IAAIhC,IAAIA,CAAAA,IAAK,KAC/B,OAAO1B,KAAK0D,KAAK,KAAKhC,KAAK,KAAKA,CAAAA,IAAK;EAEzCmC,eAAe,CAACnC,MAAcD,OAAOC,CAAAA,IAAKA,IAAIC,UAAUD,GAAG,OAAO,GAAI;EAEtEoC,gBAAgB,CAACpC,MAAcD,OAAOC,CAAAA,IAAKA,IAAIO,WAAWP,GAAG,OAAO,GAAI;EAExEqC,iBAAiBrC,GAAW;AAC1B,UAAME,IAAI;AACV,UAAMC,IAAI;AACV,WAAOJ,OAAOC,CAAKA,IAAAA,IACjBA,IAAI,MACA,MAAMC,UAAUD,IAAI,GAAGE,GAAGC,CAAAA,IAC1B,MAAM,MAAMI,WAAWP,IAAI,IAAI,GAAGE,GAAGC,CAAE;EAC/C;EAEAmC,WAAWtC,GAAW;AACpB,UAAME,IAAI;AACV,WAAOF,IAAIA,MAAME,IAAI,KAAKF,IAAIE;EAChC;EAEAqC,YAAYvC,GAAW;AACrB,UAAME,IAAI;AACV,YAAQF,KAAK,KAAKA,MAAME,IAAI,KAAKF,IAAIE,KAAK;EAC5C;EAEAsC,cAAcxC,GAAW;AACvB,QAAIE,IAAI;AACR,SAAKF,KAAK,OAAO,GAAG;AAClB,aAAO,OAAOA,IAAIA,OAAOE,KAAM,SAAU,KAAKF,IAAIE;;AAEpD,WAAO,QAAQF,KAAK,KAAKA,OAAOE,KAAM,SAAU,KAAKF,IAAIE,KAAK;EAChE;EAEAuC,cAAc,CAACzC,MAAc,IAAIQ,QAAQkC,cAAc,IAAI1C,CAAAA;EAE3D0C,cAAc1C,GAAW;AACvB,UAAM2C,IAAI;AACV,UAAMC,IAAI;AACV,QAAI5C,IAAK,IAAI4C,GAAI;AACf,aAAOD,IAAI3C,IAAIA;;AAEjB,QAAIA,IAAK,IAAI4C,GAAI;AACf,aAAOD,KAAK3C,KAAM,MAAM4C,KAAM5C,IAAI;;AAEpC,QAAIA,IAAK,MAAM4C,GAAI;AACjB,aAAOD,KAAK3C,KAAM,OAAO4C,KAAM5C,IAAI;;AAErC,WAAO2C,KAAK3C,KAAM,QAAQ4C,KAAM5C,IAAI;EACtC;EAEA6C,iBAAiB,CAAC7C,MAAeA,IAAI,MACjCQ,QAAQiC,aAAazC,IAAI,CAAK,IAAA,MAC9BQ,QAAQkC,cAAc1C,IAAI,IAAI,CAAA,IAAK,MAAM;AAC/C;ACrHO,SAAS8C,oBAAoBC,OAAyD;AAC3F,MAAIA,SAAS,OAAOA,UAAU,UAAU;AACtC,UAAMC,OAAOD,MAAME,SAAQ;AAC3B,WAAOD,SAAS,4BAA4BA,SAAS;;AAGvD,SAAO;AACT;AAWO,SAASE,MAAMH,OAAO;AAC3B,SAAOD,oBAAoBC,KAAAA,IAASA,QAAQ,IAAII,MAAMJ,KAAM;AAC9D;AAKO,SAASK,cAAcL,OAAO;AACnC,SAAOD,oBAAoBC,KAAAA,IACvBA,QACA,IAAII,MAAMJ,KAAAA,EAAOM,SAAS,GAAKC,EAAAA,OAAO,GAAA,EAAKC,UAAS;AAC1D;AC/BA,IAAMC,UAAU;EAAC;EAAK;EAAK;EAAe;EAAU;AAAU;AAC9D,IAAMC,SAAS;EAAC;EAAS;EAAe;AAAkB;AAEnD,SAASC,wBAAwBC,WAAU;AAChDA,EAAAA,UAASC,IAAI,aAAa;IACxBxH,OAAOyH;IACPC,UAAU;IACVC,QAAQ;IACRnI,IAAIiI;IACJG,MAAMH;IACNI,MAAMJ;IACNK,IAAIL;IACJb,MAAMa;EACR,CAAA;AAEAF,EAAAA,UAASQ,SAAS,aAAa;IAC7BC,WAAW;IACXC,YAAY;IACZC,aAAa,CAACC,SAASA,SAAS,gBAAgBA,SAAS,gBAAgBA,SAAS;EACpF,CAAA;AAEAZ,EAAAA,UAASC,IAAI,cAAc;IACzBH,QAAQ;MACNT,MAAM;MACNwB,YAAYf;IACd;IACAD,SAAS;MACPR,MAAM;MACNwB,YAAYhB;IACd;EACF,CAAA;AAEAG,EAAAA,UAASQ,SAAS,cAAc;IAC9BC,WAAW;EACb,CAAA;AAEAT,EAAAA,UAASC,IAAI,eAAe;IAC1Ba,QAAQ;MACNC,WAAW;QACTZ,UAAU;MACZ;IACF;IACAa,QAAQ;MACND,WAAW;QACTZ,UAAU;MACZ;IACF;IACAc,MAAM;MACJC,YAAY;QACVpB,QAAQ;UACNO,MAAM;QACR;QACAc,SAAS;UACP9B,MAAM;UACNc,UAAU;QACZ;MACF;IACF;IACAiB,MAAM;MACJF,YAAY;QACVpB,QAAQ;UACNS,IAAI;QACN;QACAY,SAAS;UACP9B,MAAM;UACNe,QAAQ;UACRnI,IAAIoJ,CAAAA,MAAKA,IAAI;QACf;MACF;IACF;EACF,CAAA;AACF;ACvEO,SAASC,qBAAqBtB,WAAU;AAC7CA,EAAAA,UAASC,IAAI,UAAU;IACrBsB,aAAa;IACbC,SAAS;MACPC,KAAK;MACLrI,OAAO;MACPsI,QAAQ;MACRvI,MAAM;IACR;EACF,CAAA;AACF;ACTA,IAAMwI,YAAY,oBAAIC,IAAAA;AAEtB,SAASC,gBAAgBC,QAAgB1H,SAAoC;AAC3EA,YAAUA,WAAW,CAAA;AACrB,QAAM2H,WAAWD,SAASE,KAAKC,UAAU7H,OAAAA;AACzC,MAAI8H,YAAYP,UAAUQ,IAAIJ,QAAAA;AAC9B,MAAI,CAACG,WAAW;AACdA,gBAAY,IAAIE,KAAKC,aAAaP,QAAQ1H,OAAAA;AAC1CuH,cAAU1B,IAAI8B,UAAUG,SAAAA;;AAE1B,SAAOA;AACT;AAEO,SAASI,aAAaC,KAAaT,QAAgB1H,SAAoC;AAC5F,SAAOyH,gBAAgBC,QAAQ1H,OAASoI,EAAAA,OAAOD,GAAAA;AACjD;ACRA,IAAME,aAAa;EAOjBC,OAAOtD,OAAO;AACZ,WAAOuD,QAAQvD,KAAS,IAAyBA,QAAS,KAAKA;EACjE;EAUAwD,QAAQC,WAAWC,OAAOC,OAAO;AAC/B,QAAIF,cAAc,GAAG;AACnB,aAAO;;AAGT,UAAMf,SAAS,KAAKkB,MAAM5I,QAAQ0H;AAClC,QAAImB;AACJ,QAAIC,QAAQL;AAEZ,QAAIE,MAAMnJ,SAAS,GAAG;AAEpB,YAAMuJ,UAAUxI,KAAKJ,IAAII,KAAKyI,IAAIL,MAAM,CAAE,EAAC3D,KAAK,GAAGzE,KAAKyI,IAAIL,MAAMA,MAAMnJ,SAAS,CAAE,EAACwF,KAAK,CAAA;AACzF,UAAI+D,UAAU,QAAQA,UAAU,MAAO;AACrCF,mBAAW;;AAGbC,cAAQG,eAAeR,WAAWE,KAAAA;;AAGpC,UAAMO,WAAWC,MAAM5I,KAAKyI,IAAIF,KAAAA,CAAAA;AAOhC,UAAMM,aAAaC,MAAMH,QAAAA,IAAY,IAAI3I,KAAKJ,IAAII,KAAKL,IAAI,KAAKK,KAAK+I,MAAMJ,QAAAA,GAAW,EAAA,GAAK,CAAE;AAE7F,UAAMlJ,UAAU;MAAC6I;MAAUU,uBAAuBH;MAAYI,uBAAuBJ;IAAU;AAC/FtH,WAAOC,OAAO/B,SAAS,KAAKA,QAAQ2I,MAAMP,MAAM;AAEhD,WAAOF,aAAaO,WAAWf,QAAQ1H,OAAAA;EACzC;EAWAyJ,YAAYhB,WAAWC,OAAOC,OAAO;AACnC,QAAIF,cAAc,GAAG;AACnB,aAAO;;AAET,UAAMiB,SAASf,MAAMD,KAAAA,EAAOiB,eAAgBlB,YAAalI,KAAK8B,IAAI,IAAI9B,KAAK+I,MAAMH,MAAMV,SAAAA,CAAAA,CAAAA;AACvF,QAAI;MAAC;MAAG;MAAG;MAAG;MAAG;MAAI;IAAG,EAACmB,SAASF,MAAAA,KAAWhB,QAAQ,MAAMC,MAAMnJ,QAAQ;AACvE,aAAO6I,WAAWG,QAAQtK,KAAK,MAAMuK,WAAWC,OAAOC,KAAAA;;AAEzD,WAAO;EACT;AAEF;AAGA,SAASM,eAAeR,WAAWE,OAAO;AAGxC,MAAIG,QAAQH,MAAMnJ,SAAS,IAAImJ,MAAM,CAAE,EAAC3D,QAAQ2D,MAAM,CAAE,EAAC3D,QAAQ2D,MAAM,CAAE,EAAC3D,QAAQ2D,MAAM,CAAE,EAAC3D;AAG3F,MAAIzE,KAAKyI,IAAIF,KAAAA,KAAU,KAAKL,cAAclI,KAAK+I,MAAMb,SAAY,GAAA;AAE/DK,YAAQL,YAAYlI,KAAK+I,MAAMb,SAAAA;;AAEjC,SAAOK;AACT;AAMA,IAAA,QAAe;EAACT;AAAU;ACnGnB,SAASwB,mBAAmBjE,WAAU;AAC3CA,EAAAA,UAASC,IAAI,SAAS;IACpBiE,SAAS;IACTC,QAAQ;IACRlJ,SAAS;IACTmJ,aAAa;IASbC,QAAQ;IAERC,MAAM;IAMNC,OAAO;IAGPC,MAAM;MACJN,SAAS;MACTO,WAAW;MACXC,iBAAiB;MACjBC,WAAW;MACXC,YAAY;MACZC,WAAW,CAACC,MAAM1K,YAAYA,QAAQqK;MACtCM,WAAW,CAACD,MAAM1K,YAAYA,QAAQmF;MACtC4E,QAAQ;IACV;IAEAa,QAAQ;MACNd,SAAS;MACTe,MAAM,CAAA;MACNC,YAAY;MACZC,OAAO;IACT;IAGAC,OAAO;MAELlB,SAAS;MAGTmB,MAAM;MAGN7D,SAAS;QACPC,KAAK;QACLC,QAAQ;MACV;IACF;IAGAqB,OAAO;MACLuC,aAAa;MACbC,aAAa;MACbC,QAAQ;MACRC,iBAAiB;MACjBC,iBAAiB;MACjBlE,SAAS;MACT0C,SAAS;MACTyB,UAAU;MACVC,iBAAiB;MACjBC,aAAa;MAEb/N,UAAUgO,MAAMrD,WAAWC;MAC3BqD,OAAO,CAAA;MACPC,OAAO,CAAA;MACPlN,OAAO;MACPmN,YAAY;MAEZC,mBAAmB;MACnBC,eAAe;MACfC,iBAAiB;IACnB;EACF,CAAA;AAEApG,EAAAA,UAASqG,MAAM,eAAe,SAAS,IAAI,OAAA;AAC3CrG,EAAAA,UAASqG,MAAM,cAAc,SAAS,IAAI,aAAA;AAC1CrG,EAAAA,UAASqG,MAAM,gBAAgB,SAAS,IAAI,aAAA;AAC5CrG,EAAAA,UAASqG,MAAM,eAAe,SAAS,IAAI,OAAA;AAE3CrG,EAAAA,UAASQ,SAAS,SAAS;IACzBC,WAAW;IACXE,aAAa,CAACC,SAAS,CAACA,KAAK0F,WAAW,QAAA,KAAa,CAAC1F,KAAK0F,WAAW,OAAY1F,KAAAA,SAAS,cAAcA,SAAS;IAClHF,YAAY,CAACE,SAASA,SAAS,gBAAgBA,SAAS,oBAAoBA,SAAS;EACvF,CAAA;AAEAZ,EAAAA,UAASQ,SAAS,UAAU;IAC1BC,WAAW;EACb,CAAA;AAEAT,EAAAA,UAASQ,SAAS,eAAe;IAC/BG,aAAa,CAACC,SAASA,SAAS,qBAAqBA,SAAS;IAC9DF,YAAY,CAACE,SAASA,SAAS;EACjC,CAAA;AACF;IClGa2F,YAAYrK,uBAAOsK,OAAO,IAAI;IAC9BC,cAAcvK,uBAAOsK,OAAO,IAAI;AAO7C,SAASE,WAASC,MAAMC,KAAK;AAC3B,MAAI,CAACA,KAAK;AACR,WAAOD;;AAET,QAAME,OAAOD,IAAIE,MAAM,GAAA;AACvB,WAASC,IAAI,GAAGC,IAAIH,KAAKjN,QAAQmN,IAAIC,GAAG,EAAED,GAAG;AAC3C,UAAME,IAAIJ,KAAKE,CAAE;AACjBJ,WAAOA,KAAKM,CAAE,MAAKN,KAAKM,CAAAA,IAAK/K,uBAAOsK,OAAO,IAAI;EACjD;AACA,SAAOG;AACT;AAEA,SAAS1G,IAAIiH,MAAMC,OAAOzE,QAAQ;AAChC,MAAI,OAAOyE,UAAU,UAAU;AAC7B,WAAOC,MAAMV,WAASQ,MAAMC,KAAQzE,GAAAA,MAAAA;;AAEtC,SAAO0E,MAAMV,WAASQ,MAAM,EAAKC,GAAAA,KAAAA;AACnC;AAMO,IAAME,WAAN,MAAMA;EACXC,YAAYC,eAAcC,WAAW;AACnC,SAAKzG,YAAYb;AACjB,SAAKuH,kBAAkB;AACvB,SAAKC,cAAc;AACnB,SAAKnI,QAAQ;AACb,SAAKoI,WAAW,CAAA;AAChB,SAAKC,mBAAmB,CAACC,YAAYA,QAAQ7E,MAAM8E,SAASC,oBAAmB;AAC/E,SAAKC,WAAW,CAAA;AAChB,SAAKC,SAAS;MACZ;MACA;MACA;MACA;MACA;IACD;AACD,SAAKC,OAAO;MACVC,QAAQ;MACRC,MAAM;MACNC,OAAO;MACPC,YAAY;MACZC,QAAQ;IACV;AACA,SAAKC,QAAQ,CAAA;AACb,SAAKC,uBAAuB,CAACC,KAAKtO,YAAYqF,cAAcrF,QAAQqN,eAAe;AACnF,SAAKkB,mBAAmB,CAACD,KAAKtO,YAAYqF,cAAcrF,QAAQsN,WAAW;AAC3E,SAAKkB,aAAa,CAACF,KAAKtO,YAAYqF,cAAcrF,QAAQmF,KAAK;AAC/D,SAAKsJ,YAAY;AACjB,SAAKC,cAAc;MACjBC,MAAM;MACNC,WAAW;MACXC,kBAAkB;IACpB;AACA,SAAKC,sBAAsB;AAC3B,SAAKC,UAAU;AACf,SAAKC,UAAU;AACf,SAAKC,UAAU;AACf,SAAKC,UAAU,CAAA;AACf,SAAKC,aAAa;AAClB,SAAKC,QAAQtJ;AACb,SAAKuJ,SAAS,CAAA;AACd,SAAKC,WAAW;AAChB,SAAKC,0BAA0B;AAE/B,SAAKnJ,SAAS+G,aAAAA;AACd,SAAKhP,MAAMiP,SAAAA;EACb;EAMAvH,IAAIkH,OAAOzE,QAAQ;AACjB,WAAOzC,IAAI,MAAMkH,OAAOzE,MAAAA;EAC1B;EAKAP,IAAIgF,OAAO;AACT,WAAOT,WAAS,MAAMS,KAAAA;EACxB;EAMA3G,SAAS2G,OAAOzE,QAAQ;AACtB,WAAOzC,IAAIwG,aAAaU,OAAOzE,MAAAA;EACjC;EAEAkH,SAASzC,OAAOzE,QAAQ;AACtB,WAAOzC,IAAIsG,WAAWY,OAAOzE,MAAAA;EAC/B;EAmBA2D,MAAMc,OAAOvG,MAAMiJ,aAAaC,YAAY;AAC1C,UAAMC,cAAcrD,WAAS,MAAMS,KAAAA;AACnC,UAAM6C,oBAAoBtD,WAAS,MAAMmD,WAAAA;AACzC,UAAMI,cAAc,MAAMrJ;AAE1B1E,WAAOgO,iBAAiBH,aAAa;MAEnC,CAACE,WAAAA,GAAc;QACb7K,OAAO2K,YAAYnJ,IAAK;QACxBuJ,UAAU;MACZ;MAEA,CAACvJ,IAAAA,GAAO;QACNwJ,YAAY;QACZjI,MAAM;AACJ,gBAAMkI,QAAQ,KAAKJ,WAAY;AAC/B,gBAAMK,SAASN,kBAAkBF,UAAW;AAC5C,cAAIS,SAASF,KAAQ,GAAA;AACnB,mBAAOnO,OAAOC,OAAO,CAAA,GAAImO,QAAQD,KAAAA;;AAEnC,iBAAOG,eAAeH,OAAOC,MAAAA;QAC/B;QACArK,IAAIb,OAAO;AACT,eAAK6K,WAAAA,IAAe7K;QACtB;MACF;IACF,CAAA;EACF;EAEA7G,MAAMkS,UAAU;AACdA,aAASC,QAAQ,CAACnS,UAAUA,MAAM,IAAI,CAAA;EACxC;AACF;AAGA,IAAA,WAA+B,IAAI8O,SAAS;EAC1C1G,aAAa,CAACC,SAAS,CAACA,KAAK0F,WAAW,IAAA;EACxC5F,YAAY,CAACE,SAASA,SAAS;EAC/B4H,OAAO;IACL/H,WAAW;EACb;EACAqI,aAAa;IACXnI,aAAa;IACbD,YAAY;EACd;AACF,GAAG;EAACX;EAAyBuB;EAAsB2C;CAAmB;ACtJ/D,SAAS0G,aAAazC,MAAgB;AAC3C,MAAI,CAACA,QAAQ9M,cAAc8M,KAAKE,IAAI,KAAKhN,cAAc8M,KAAKC,MAAM,GAAG;AACnE,WAAO;;AAGT,UAAQD,KAAKG,QAAQH,KAAKG,QAAQ,MAAM,OACrCH,KAAKK,SAASL,KAAKK,SAAS,MAAM,MACnCL,KAAKE,OAAO,QACZF,KAAKC;AACT;AAKO,SAASyC,aACdlC,KACAmC,MACAC,IACAC,SACAC,QACA;AACA,MAAIC,YAAYJ,KAAKG,MAAO;AAC5B,MAAI,CAACC,WAAW;AACdA,gBAAYJ,KAAKG,MAAO,IAAGtC,IAAIwC,YAAYF,MAAAA,EAAQ7F;AACnD2F,OAAGK,KAAKH,MAAAA;;AAEV,MAAIC,YAAYF,SAAS;AACvBA,cAAUE;;AAEZ,SAAOF;AACT;AASO,SAASK,aACd1C,KACAR,MACAmD,eACAC,OACA;AACAA,UAAQA,SAAS,CAAA;AACjB,MAAIT,OAAOS,MAAMT,OAAOS,MAAMT,QAAQ,CAAA;AACtC,MAAIC,KAAKQ,MAAMC,iBAAiBD,MAAMC,kBAAkB,CAAA;AAExD,MAAID,MAAMpD,SAASA,MAAM;AACvB2C,WAAOS,MAAMT,OAAO,CAAA;AACpBC,SAAKQ,MAAMC,iBAAiB,CAAA;AAC5BD,UAAMpD,OAAOA;;AAGfQ,MAAI8C,KAAI;AAER9C,MAAIR,OAAOA;AACX,MAAI6C,UAAU;AACd,QAAMU,OAAOJ,cAAczR;AAC3B,MAAImN,GAAW2E,GAAWC,MAAcC,OAAwBC;AAChE,OAAK9E,IAAI,GAAGA,IAAI0E,MAAM1E,KAAK;AACzB6E,YAAQP,cAActE,CAAE;AAGxB,QAAI6E,UAAU1L,UAAa0L,UAAU,QAAQ,CAACjJ,QAAQiJ,KAAQ,GAAA;AAC5Db,gBAAUH,aAAalC,KAAKmC,MAAMC,IAAIC,SAASa,KAAAA;eACtCjJ,QAAQiJ,KAAQ,GAAA;AAGzB,WAAKF,IAAI,GAAGC,OAAOC,MAAMhS,QAAQ8R,IAAIC,MAAMD,KAAK;AAC9CG,sBAAcD,MAAMF,CAAE;AAEtB,YAAIG,gBAAgB3L,UAAa2L,gBAAgB,QAAQ,CAAClJ,QAAQkJ,WAAc,GAAA;AAC9Ed,oBAAUH,aAAalC,KAAKmC,MAAMC,IAAIC,SAASc,WAAAA;;MAEnD;;EAEJ;AAEAnD,MAAIoD,QAAO;AAEX,QAAMC,QAAQjB,GAAGlR,SAAS;AAC1B,MAAImS,QAAQV,cAAczR,QAAQ;AAChC,SAAKmN,IAAI,GAAGA,IAAIgF,OAAOhF,KAAK;AAC1B,aAAO8D,KAAKC,GAAG/D,CAAAA,CAAE;IACnB;AACA+D,OAAGkB,OAAO,GAAGD,KAAAA;;AAEf,SAAOhB;AACT;AAUO,SAASkB,YAAYjJ,OAAckJ,OAAe/G,OAAe;AACtE,QAAMyC,mBAAmB5E,MAAMmJ;AAC/B,QAAMC,YAAYjH,UAAU,IAAIxK,KAAKJ,IAAI4K,QAAQ,GAAG,GAAA,IAAO;AAC3D,SAAOxK,KAAK0R,OAAOH,QAAQE,aAAaxE,gBAAAA,IAAoBA,mBAAmBwE;AACjF;AAKO,SAASE,YAAYC,QAA4B7D,KAAgC;AACtF,MAAI,CAACA,OAAO,CAAC6D,QAAQ;AACnB;;AAGF7D,QAAMA,OAAO6D,OAAOC,WAAW,IAAA;AAE/B9D,MAAI8C,KAAI;AAGR9C,MAAI+D,eAAc;AAClB/D,MAAIgE,UAAU,GAAG,GAAGH,OAAOpH,OAAOoH,OAAOI,MAAM;AAC/CjE,MAAIoD,QAAO;AACb;AASO,SAASc,UACdlE,KACAtO,SACAyS,GACAC,GACA;AAEAC,kBAAgBrE,KAAKtO,SAASyS,GAAGC,GAAG,IAAI;AAC1C;AAGO,SAASC,gBACdrE,KACAtO,SACAyS,GACAC,GACAE,GACA;AACA,MAAI3N,MAAc4N,SAAiBC,SAAiB9E,MAAc+E,cAAsBhI,OAAeiI,UAAkBC;AACzH,QAAMhF,QAAQjO,QAAQkT;AACtB,QAAMC,WAAWnT,QAAQmT;AACzB,QAAMC,SAASpT,QAAQoT;AACvB,MAAIC,OAAOF,YAAY,KAAKG;AAE5B,MAAIrF,SAAS,OAAOA,UAAU,UAAU;AACtChJ,WAAOgJ,MAAM/I,SAAQ;AACrB,QAAID,SAAS,+BAA+BA,SAAS,8BAA8B;AACjFqJ,UAAI8C,KAAI;AACR9C,UAAIiF,UAAUd,GAAGC,CAAAA;AACjBpE,UAAIkF,OAAOH,GAAAA;AACX/E,UAAImF,UAAUxF,OAAO,CAACA,MAAMlD,QAAQ,GAAG,CAACkD,MAAMsE,SAAS,GAAGtE,MAAMlD,OAAOkD,MAAMsE,MAAM;AACnFjE,UAAIoD,QAAO;AACX;;;AAIJ,MAAIrI,MAAM+J,MAAWA,KAAAA,UAAU,GAAG;AAChC;;AAGF9E,MAAIoF,UAAS;AAEb,UAAQzF,OAAAA;;IAEN;AACE,UAAI2E,GAAG;AACLtE,YAAIqF,QAAQlB,GAAGC,GAAGE,IAAI,GAAGQ,QAAQ,GAAG,GAAG7Q,GAAAA;aAClC;AACL+L,YAAIsF,IAAInB,GAAGC,GAAGU,QAAQ,GAAG7Q,GAAAA;;AAE3B+L,UAAIuF,UAAS;AACb;IACF,KAAK;AACH9I,cAAQ6H,IAAIA,IAAI,IAAIQ;AACpB9E,UAAIwF,OAAOrB,IAAIlS,KAAK+B,IAAI+Q,GAAOtI,IAAAA,OAAO2H,IAAInS,KAAKiD,IAAI6P,GAAOD,IAAAA,MAAAA;AAC1DC,aAAOU;AACPzF,UAAI0F,OAAOvB,IAAIlS,KAAK+B,IAAI+Q,GAAOtI,IAAAA,OAAO2H,IAAInS,KAAKiD,IAAI6P,GAAOD,IAAAA,MAAAA;AAC1DC,aAAOU;AACPzF,UAAI0F,OAAOvB,IAAIlS,KAAK+B,IAAI+Q,GAAOtI,IAAAA,OAAO2H,IAAInS,KAAKiD,IAAI6P,GAAOD,IAAAA,MAAAA;AAC1D9E,UAAIuF,UAAS;AACb;IACF,KAAK;AAQHd,qBAAeK,SAAS;AACxBpF,aAAOoF,SAASL;AAChBF,gBAAUtS,KAAKiD,IAAI6P,MAAMY,UAAcjG,IAAAA;AACvCgF,iBAAWzS,KAAKiD,IAAI6P,MAAMY,UAAAA,KAAerB,IAAIA,IAAI,IAAIG,eAAe/E;AACpE8E,gBAAUvS,KAAK+B,IAAI+Q,MAAMY,UAAcjG,IAAAA;AACvCiF,iBAAW1S,KAAK+B,IAAI+Q,MAAMY,UAAAA,KAAerB,IAAIA,IAAI,IAAIG,eAAe/E;AACpEM,UAAIsF,IAAInB,IAAIO,UAAUN,IAAII,SAASC,cAAcM,MAAMzP,IAAIyP,MAAM5P,OAAAA;AACjE6K,UAAIsF,IAAInB,IAAIQ,UAAUP,IAAIG,SAASE,cAAcM,MAAM5P,SAAS4P,GAAAA;AAChE/E,UAAIsF,IAAInB,IAAIO,UAAUN,IAAII,SAASC,cAAcM,KAAKA,MAAM5P,OAAAA;AAC5D6K,UAAIsF,IAAInB,IAAIQ,UAAUP,IAAIG,SAASE,cAAcM,MAAM5P,SAAS4P,MAAMzP,EAAAA;AACtE0K,UAAIuF,UAAS;AACb;IACF,KAAK;AACH,UAAI,CAACV,UAAU;AACbnF,eAAOzN,KAAK2T,UAAUd;AACtBrI,gBAAQ6H,IAAIA,IAAI,IAAI5E;AACpBM,YAAI6F,KAAK1B,IAAI1H,OAAO2H,IAAI1E,MAAM,IAAIjD,OAAO,IAAIiD,IAAAA;AAC7C;;AAEFqF,aAAOY;;IAET,KAAK;AACHjB,iBAAWzS,KAAKiD,IAAI6P,GAAAA,KAAQT,IAAIA,IAAI,IAAIQ;AACxCP,gBAAUtS,KAAKiD,IAAI6P,GAAOD,IAAAA;AAC1BN,gBAAUvS,KAAK+B,IAAI+Q,GAAOD,IAAAA;AAC1BH,iBAAW1S,KAAK+B,IAAI+Q,GAAAA,KAAQT,IAAIA,IAAI,IAAIQ;AACxC9E,UAAIwF,OAAOrB,IAAIO,UAAUN,IAAII,OAAAA;AAC7BxE,UAAI0F,OAAOvB,IAAIQ,UAAUP,IAAIG,OAAAA;AAC7BvE,UAAI0F,OAAOvB,IAAIO,UAAUN,IAAII,OAAAA;AAC7BxE,UAAI0F,OAAOvB,IAAIQ,UAAUP,IAAIG,OAAAA;AAC7BvE,UAAIuF,UAAS;AACb;IACF,KAAK;AACHR,aAAOY;;IAET,KAAK;AACHjB,iBAAWzS,KAAKiD,IAAI6P,GAAAA,KAAQT,IAAIA,IAAI,IAAIQ;AACxCP,gBAAUtS,KAAKiD,IAAI6P,GAAOD,IAAAA;AAC1BN,gBAAUvS,KAAK+B,IAAI+Q,GAAOD,IAAAA;AAC1BH,iBAAW1S,KAAK+B,IAAI+Q,GAAAA,KAAQT,IAAIA,IAAI,IAAIQ;AACxC9E,UAAIwF,OAAOrB,IAAIO,UAAUN,IAAII,OAAAA;AAC7BxE,UAAI0F,OAAOvB,IAAIO,UAAUN,IAAII,OAAAA;AAC7BxE,UAAIwF,OAAOrB,IAAIQ,UAAUP,IAAIG,OAAAA;AAC7BvE,UAAI0F,OAAOvB,IAAIQ,UAAUP,IAAIG,OAAAA;AAC7B;IACF,KAAK;AACHG,iBAAWzS,KAAKiD,IAAI6P,GAAAA,KAAQT,IAAIA,IAAI,IAAIQ;AACxCP,gBAAUtS,KAAKiD,IAAI6P,GAAOD,IAAAA;AAC1BN,gBAAUvS,KAAK+B,IAAI+Q,GAAOD,IAAAA;AAC1BH,iBAAW1S,KAAK+B,IAAI+Q,GAAAA,KAAQT,IAAIA,IAAI,IAAIQ;AACxC9E,UAAIwF,OAAOrB,IAAIO,UAAUN,IAAII,OAAAA;AAC7BxE,UAAI0F,OAAOvB,IAAIO,UAAUN,IAAII,OAAAA;AAC7BxE,UAAIwF,OAAOrB,IAAIQ,UAAUP,IAAIG,OAAAA;AAC7BvE,UAAI0F,OAAOvB,IAAIQ,UAAUP,IAAIG,OAAAA;AAC7BQ,aAAOY;AACPjB,iBAAWzS,KAAKiD,IAAI6P,GAAAA,KAAQT,IAAIA,IAAI,IAAIQ;AACxCP,gBAAUtS,KAAKiD,IAAI6P,GAAOD,IAAAA;AAC1BN,gBAAUvS,KAAK+B,IAAI+Q,GAAOD,IAAAA;AAC1BH,iBAAW1S,KAAK+B,IAAI+Q,GAAAA,KAAQT,IAAIA,IAAI,IAAIQ;AACxC9E,UAAIwF,OAAOrB,IAAIO,UAAUN,IAAII,OAAAA;AAC7BxE,UAAI0F,OAAOvB,IAAIO,UAAUN,IAAII,OAAAA;AAC7BxE,UAAIwF,OAAOrB,IAAIQ,UAAUP,IAAIG,OAAAA;AAC7BvE,UAAI0F,OAAOvB,IAAIQ,UAAUP,IAAIG,OAAAA;AAC7B;IACF,KAAK;AACHA,gBAAUD,IAAIA,IAAI,IAAIrS,KAAKiD,IAAI6P,GAAAA,IAAOD;AACtCN,gBAAUvS,KAAK+B,IAAI+Q,GAAOD,IAAAA;AAC1B9E,UAAIwF,OAAOrB,IAAII,SAASH,IAAII,OAAAA;AAC5BxE,UAAI0F,OAAOvB,IAAII,SAASH,IAAII,OAAAA;AAC5B;IACF,KAAK;AACHxE,UAAIwF,OAAOrB,GAAGC,CAAAA;AACdpE,UAAI0F,OAAOvB,IAAIlS,KAAKiD,IAAI6P,GAAAA,KAAQT,IAAIA,IAAI,IAAIQ,SAASV,IAAInS,KAAK+B,IAAI+Q,GAAOD,IAAAA,MAAAA;AACzE;IACF,KAAK;AACH9E,UAAIuF,UAAS;AACb;EACJ;AAEAvF,MAAI8F,KAAI;AACR,MAAIpU,QAAQqU,cAAc,GAAG;AAC3B/F,QAAIgG,OAAM;;AAEd;AASO,SAASC,eACdxT,OACAyT,MACAC,QACA;AACAA,WAASA,UAAU;AAEnB,SAAO,CAACD,QAASzT,SAASA,MAAM0R,IAAI+B,KAAKzV,OAAO0V,UAAU1T,MAAM0R,IAAI+B,KAAKxV,QAAQyV,UACjF1T,MAAM2R,IAAI8B,KAAKnN,MAAMoN,UAAU1T,MAAM2R,IAAI8B,KAAKlN,SAASmN;AACzD;AAEO,SAASC,SAASpG,KAA+BkG,MAAY;AAClElG,MAAI8C,KAAI;AACR9C,MAAIoF,UAAS;AACbpF,MAAI6F,KAAKK,KAAKzV,MAAMyV,KAAKnN,KAAKmN,KAAKxV,QAAQwV,KAAKzV,MAAMyV,KAAKlN,SAASkN,KAAKnN,GAAG;AAC5EiH,MAAIpE,KAAI;AACV;AAEO,SAASyK,WAAWrG,KAA+B;AACxDA,MAAIoD,QAAO;AACb;AAKO,SAASkD,eACdtG,KACAuG,UACA3E,QACA4E,MACAnG,MACA;AACA,MAAI,CAACkG,UAAU;AACb,WAAOvG,IAAI0F,OAAO9D,OAAOuC,GAAGvC,OAAOwC,CAAC;;AAEtC,MAAI/D,SAAS,UAAU;AACrB,UAAMoG,YAAYF,SAASpC,IAAIvC,OAAOuC,KAAK;AAC3CnE,QAAI0F,OAAOe,UAAUF,SAASnC,CAAC;AAC/BpE,QAAI0F,OAAOe,UAAU7E,OAAOwC,CAAC;EAC/B,WAAW/D,SAAS,YAAY,CAAC,CAACmG,MAAM;AACtCxG,QAAI0F,OAAOa,SAASpC,GAAGvC,OAAOwC,CAAC;SAC1B;AACLpE,QAAI0F,OAAO9D,OAAOuC,GAAGoC,SAASnC,CAAC;;AAEjCpE,MAAI0F,OAAO9D,OAAOuC,GAAGvC,OAAOwC,CAAC;AAC/B;AAKO,SAASsC,eACd1G,KACAuG,UACA3E,QACA4E,MACA;AACA,MAAI,CAACD,UAAU;AACb,WAAOvG,IAAI0F,OAAO9D,OAAOuC,GAAGvC,OAAOwC,CAAC;;AAEtCpE,MAAI2G,cACFH,OAAOD,SAASK,OAAOL,SAASM,MAChCL,OAAOD,SAASO,OAAOP,SAASQ,MAChCP,OAAO5E,OAAOiF,OAAOjF,OAAOgF,MAC5BJ,OAAO5E,OAAOmF,OAAOnF,OAAOkF,MAC5BlF,OAAOuC,GACPvC,OAAOwC,CAAC;AACZ;AAEA,SAAS4C,cAAchH,KAA+BiH,MAAsB;AAC1E,MAAIA,KAAKC,aAAa;AACpBlH,QAAIiF,UAAUgC,KAAKC,YAAY,CAAA,GAAID,KAAKC,YAAY,CAAE,CAAA;;AAGxD,MAAI,CAACxU,cAAcuU,KAAKpC,QAAQ,GAAG;AACjC7E,QAAIkF,OAAO+B,KAAKpC,QAAQ;;AAG1B,MAAIoC,KAAKpQ,OAAO;AACdmJ,QAAImH,YAAYF,KAAKpQ;;AAGvB,MAAIoQ,KAAKG,WAAW;AAClBpH,QAAIoH,YAAYH,KAAKG;;AAGvB,MAAIH,KAAKI,cAAc;AACrBrH,QAAIqH,eAAeJ,KAAKI;;AAE5B;AAEA,SAASC,aACPtH,KACAmE,GACAC,GACAmD,MACAN,MACA;AACA,MAAIA,KAAKO,iBAAiBP,KAAKQ,WAAW;AAQxC,UAAMC,UAAU1H,IAAIwC,YAAY+E,IAAAA;AAChC,UAAM9W,OAAO0T,IAAIuD,QAAQC;AACzB,UAAMjX,QAAQyT,IAAIuD,QAAQE;AAC1B,UAAM7O,MAAMqL,IAAIsD,QAAQG;AACxB,UAAM7O,SAASoL,IAAIsD,QAAQI;AAC3B,UAAMC,cAAcd,KAAKO,iBAAiBzO,MAAMC,UAAU,IAAIA;AAE9DgH,QAAIgI,cAAchI,IAAImH;AACtBnH,QAAIoF,UAAS;AACbpF,QAAIjE,YAAYkL,KAAKgB,mBAAmB;AACxCjI,QAAIwF,OAAO/U,MAAMsX,WAAAA;AACjB/H,QAAI0F,OAAOhV,OAAOqX,WAAAA;AAClB/H,QAAIgG,OAAM;;AAEd;AAEA,SAASkC,aAAalI,KAA+BiH,MAAuB;AAC1E,QAAMkB,WAAWnI,IAAImH;AAErBnH,MAAImH,YAAYF,KAAKpQ;AACrBmJ,MAAIoI,SAASnB,KAAKxW,MAAMwW,KAAKlO,KAAKkO,KAAKxK,OAAOwK,KAAKhD,MAAM;AACzDjE,MAAImH,YAAYgB;AAClB;AAKO,SAASE,WACdrI,KACArD,MACAwH,GACAC,GACA5E,MACAyH,OAAuB,CAAA,GACvB;AACA,QAAMqB,QAAQrO,QAAQ0C,IAAAA,IAAQA,OAAO;IAACA;EAAK;AAC3C,QAAMqJ,SAASiB,KAAKsB,cAAc,KAAKtB,KAAKuB,gBAAgB;AAC5D,MAAInK,GAAWkJ;AAEfvH,MAAI8C,KAAI;AACR9C,MAAIR,OAAOA,KAAK8C;AAChB0E,gBAAchH,KAAKiH,IAAAA;AAEnB,OAAK5I,IAAI,GAAGA,IAAIiK,MAAMpX,QAAQ,EAAEmN,GAAG;AACjCkJ,WAAOe,MAAMjK,CAAE;AAEf,QAAI4I,KAAKwB,UAAU;AACjBP,mBAAalI,KAAKiH,KAAKwB,QAAQ;;AAGjC,QAAIzC,QAAQ;AACV,UAAIiB,KAAKuB,aAAa;AACpBxI,YAAIgI,cAAcf,KAAKuB;;AAGzB,UAAI,CAAC9V,cAAcuU,KAAKsB,WAAW,GAAG;AACpCvI,YAAIjE,YAAYkL,KAAKsB;;AAGvBvI,UAAI0I,WAAWnB,MAAMpD,GAAGC,GAAG6C,KAAK0B,QAAQ;;AAG1C3I,QAAI4I,SAASrB,MAAMpD,GAAGC,GAAG6C,KAAK0B,QAAQ;AACtCrB,iBAAatH,KAAKmE,GAAGC,GAAGmD,MAAMN,IAAAA;AAE9B7C,SAAKyE,OAAOrJ,KAAKI,UAAU;EAC7B;AAEAI,MAAIoD,QAAO;AACb;AAOO,SAAS0F,mBACd9I,KACA6F,MACA;AACA,QAAM,EAAC1B,GAAGC,GAAGE,GAAGyE,GAAGjE,OAAM,IAAIe;AAG7B7F,MAAIsF,IAAInB,IAAIW,OAAOkE,SAAS5E,IAAIU,OAAOkE,SAASlE,OAAOkE,SAAS,MAAM1T,IAAIA,IAAI,IAAI;AAGlF0K,MAAI0F,OAAOvB,GAAGC,IAAI2E,IAAIjE,OAAOmE,UAAU;AAGvCjJ,MAAIsF,IAAInB,IAAIW,OAAOmE,YAAY7E,IAAI2E,IAAIjE,OAAOmE,YAAYnE,OAAOmE,YAAY3T,IAAIH,SAAS,IAAI;AAG9F6K,MAAI0F,OAAOvB,IAAIG,IAAIQ,OAAOoE,aAAa9E,IAAI2E,CAAAA;AAG3C/I,MAAIsF,IAAInB,IAAIG,IAAIQ,OAAOoE,aAAa9E,IAAI2E,IAAIjE,OAAOoE,aAAapE,OAAOoE,aAAa/T,SAAS,GAAG,IAAI;AAGpG6K,MAAI0F,OAAOvB,IAAIG,GAAGF,IAAIU,OAAOqE,QAAQ;AAGrCnJ,MAAIsF,IAAInB,IAAIG,IAAIQ,OAAOqE,UAAU/E,IAAIU,OAAOqE,UAAUrE,OAAOqE,UAAU,GAAG,CAAChU,SAAS,IAAI;AAGxF6K,MAAI0F,OAAOvB,IAAIW,OAAOkE,SAAS5E,CAAAA;AACjC;ACxgBA,IAAMgF,cAAc;AACpB,IAAMC,aAAa;AAcZ,SAASC,aAAa5S,OAAwBgJ,MAAsB;AACzE,QAAM6J,WAAW,KAAK7S,OAAO8S,MAAMJ,WAAAA;AACnC,MAAI,CAACG,WAAWA,QAAQ,CAAA,MAAO,UAAU;AACvC,WAAO7J,OAAO;;AAGhBhJ,UAAQ,CAAC6S,QAAQ,CAAE;AAEnB,UAAQA,QAAQ,CAAE,GAAA;IAChB,KAAK;AACH,aAAO7S;IACT,KAAK;AACHA,eAAS;AACT;EAGJ;AAEA,SAAOgJ,OAAOhJ;AAChB;AAEA,IAAM+S,eAAe,CAAC9Q,MAAe,CAACA,KAAK;AAQpC,SAAS+Q,kBAAkBhT,OAAwCiT,OAA0C;AAClH,QAAMC,MAAM,CAAA;AACZ,QAAMC,WAAWhI,SAAS8H,KAAAA;AAC1B,QAAMxL,OAAO0L,WAAWrW,OAAO2K,KAAKwL,KAAAA,IAASA;AAC7C,QAAMG,OAAOjI,SAASnL,KAAAA,IAClBmT,WACEE,CAAAA,SAAQjI,eAAepL,MAAMqT,IAAAA,GAAOrT,MAAMiT,MAAMI,IAAK,CAAA,CAAC,IACtDA,CAAAA,SAAQrT,MAAMqT,IAAAA,IAChB,MAAMrT;AAEV,aAAWqT,QAAQ5L,MAAM;AACvByL,QAAIG,IAAAA,IAAQN,aAAaK,KAAKC,IAAAA,CAAAA;EAChC;AACA,SAAOH;AACT;AAUO,SAASI,OAAOtT,OAA8B;AACnD,SAAOgT,kBAAkBhT,OAAO;IAACqC,KAAK;IAAKrI,OAAO;IAAKsI,QAAQ;IAAKvI,MAAM;EAAG,CAAA;AAC/E;AASO,SAASwZ,cAAcvT,OAA6B;AACzD,SAAOgT,kBAAkBhT,OAAO;IAAC;IAAW;IAAY;IAAc;EAAc,CAAA;AACtF;AAUO,SAASwT,UAAUxT,OAAkC;AAC1D,QAAMyT,MAAMH,OAAOtT,KAAAA;AAEnByT,MAAI1N,QAAQ0N,IAAI1Z,OAAO0Z,IAAIzZ;AAC3ByZ,MAAIlG,SAASkG,IAAIpR,MAAMoR,IAAInR;AAE3B,SAAOmR;AACT;AAUO,SAASC,OAAO1Y,SAA4B2Y,UAA8B;AAC/E3Y,YAAUA,WAAW,CAAA;AACrB2Y,aAAWA,YAAY/S,SAASkI;AAEhC,MAAIE,OAAOoC,eAAepQ,QAAQgO,MAAM2K,SAAS3K,IAAI;AAErD,MAAI,OAAOA,SAAS,UAAU;AAC5BA,WAAO4K,SAAS5K,MAAM,EAAA;;AAExB,MAAIC,QAAQmC,eAAepQ,QAAQiO,OAAO0K,SAAS1K,KAAK;AACxD,MAAIA,SAAS,EAAE,KAAKA,OAAO6J,MAAMH,UAAa,GAAA;AAC5CkB,YAAQC,KAAK,oCAAoC7K,QAAQ,GAAA;AACzDA,YAAQnI;;AAGV,QAAMgI,OAAO;IACXC,QAAQqC,eAAepQ,QAAQ+N,QAAQ4K,SAAS5K,MAAM;IACtDG,YAAY0J,aAAaxH,eAAepQ,QAAQkO,YAAYyK,SAASzK,UAAU,GAAGF,IAAAA;IAClFA;IACAC;IACAE,QAAQiC,eAAepQ,QAAQmO,QAAQwK,SAASxK,MAAM;IACtDyC,QAAQ;EACV;AAEA9C,OAAK8C,SAASL,aAAazC,IAAAA;AAC3B,SAAOA;AACT;AAaO,SAASiL,QAAQC,QAAwBvL,SAAkB/E,OAAgBuQ,MAA+B;AAC/G,MAAIC,YAAY;AAChB,MAAIvM,GAAW0E,MAAcrM;AAE7B,OAAK2H,IAAI,GAAG0E,OAAO2H,OAAOxZ,QAAQmN,IAAI0E,MAAM,EAAE1E,GAAG;AAC/C3H,YAAQgU,OAAOrM,CAAE;AACjB,QAAI3H,UAAUc,QAAW;AACvB;;AAEF,QAAI2H,YAAY3H,UAAa,OAAOd,UAAU,YAAY;AACxDA,cAAQA,MAAMyI,OAAAA;AACdyL,kBAAY;;AAEd,QAAIxQ,UAAU5C,UAAayC,QAAQvD,KAAQ,GAAA;AACzCA,cAAQA,MAAM0D,QAAQ1D,MAAMxF,MAAM;AAClC0Z,kBAAY;;AAEd,QAAIlU,UAAUc,QAAW;AACvB,UAAImT,QAAQ,CAACC,WAAW;AACtBD,aAAKC,YAAY;;AAEnB,aAAOlU;;EAEX;AACF;AAQO,SAASmU,UAAUC,QAAuCjP,OAAwBH,aAAsB;AAC7G,QAAM,EAAC9J,KAAKC,IAAAA,IAAOiZ;AACnB,QAAMC,SAASC,YAAYnP,QAAQhK,MAAMD,OAAO,CAAA;AAChD,QAAMqZ,WAAW,CAACvU,OAAewU,QAAgBxP,eAAehF,UAAU,IAAI,IAAIA,QAAQwU;AAC1F,SAAO;IACLtZ,KAAKqZ,SAASrZ,KAAK,CAACK,KAAKyI,IAAIqQ,MAAAA,CAAAA;IAC7BlZ,KAAKoZ,SAASpZ,KAAKkZ,MAAAA;EACrB;AACF;AAUO,SAASI,cAAcC,eAAuBjM,SAAiB;AACpE,SAAO3L,OAAOC,OAAOD,OAAOsK,OAAOsN,aAAgBjM,GAAAA,OAAAA;AACrD;ACnLO,SAASkM,gBAIdC,QACAC,WAAW;EAAC;GACZC,YACAnB,UACAoB,YAAY,MAAMH,OAAO,CAAA,GACzB;AACA,QAAMI,kBAAkBF,cAAcF;AACtC,MAAI,OAAOjB,aAAa,aAAa;AACnCA,eAAWsB,SAAS,aAAaL,MAAAA;;AAEnC,QAAM1I,QAA6B;IACjC,CAACgJ,OAAOC,WAAW,GAAG;IACtBC,YAAY;IACZC,SAAST;IACTU,aAAaN;IACb3T,WAAWsS;IACX4B,YAAYR;IACZvK,UAAU,CAACzC,UAAqB4M,gBAAgB;MAAC5M;MAAU6M,GAAAA;IAAO,GAAEC,UAAUG,iBAAiBrB,QAAAA;EACjG;AACA,SAAO,IAAI6B,MAAMtJ,OAAO;;;;IAItBuJ,eAAevK,QAAQmI,MAAc;AACnC,aAAOnI,OAAOmI,IAAK;AACnB,aAAOnI,OAAOwK;AACd,aAAOd,OAAO,CAAA,EAAGvB,IAAAA;AACjB,aAAO;IACT;;;;IAKAtQ,IAAImI,QAAQmI,MAAc;AACxB,aAAOsC,QAAQzK,QAAQmI,MACrB,MAAMuC,qBAAqBvC,MAAMwB,UAAUD,QAAQ1J,MAAAA,CAAAA;IACvD;;;;;IAMA2K,yBAAyB3K,QAAQmI,MAAM;AACrC,aAAOyC,QAAQD,yBAAyB3K,OAAOmK,QAAQ,CAAA,GAAIhC,IAAAA;IAC7D;;;;IAKA0C,iBAAiB;AACf,aAAOD,QAAQC,eAAenB,OAAO,CAAE,CAAA;IACzC;;;;IAKAoB,IAAI9K,QAAQmI,MAAc;AACxB,aAAO4C,qBAAqB/K,MAAQtG,EAAAA,SAASyO,IAAAA;IAC/C;;;;IAKA6C,QAAQhL,QAAQ;AACd,aAAO+K,qBAAqB/K,MAAAA;IAC9B;;;;IAKArK,IAAIqK,QAAQmI,MAAcrT,OAAO;AAC/B,YAAMmW,UAAUjL,OAAOkL,aAAalL,OAAOkL,WAAWrB,UAAU;AAChE7J,aAAOmI,IAAAA,IAAQ8C,QAAQ9C,IAAK,IAAGrT;AAC/B,aAAOkL,OAAOwK;AACd,aAAO;IACT;EACF,CAAA;AACF;AAUO,SAASW,eAIdC,OACA7N,SACA8N,UACAC,oBACA;AACA,QAAMtK,QAA4B;IAChCkJ,YAAY;IACZqB,QAAQH;IACRI,UAAUjO;IACVkO,WAAWJ;IACXK,QAAQ,oBAAIC,IAAAA;IACZ1O,cAAcA,aAAamO,OAAOE,kBAAAA;IAClCM,YAAY,CAACxN,QAAmB+M,eAAeC,OAAOhN,KAAKiN,UAAUC,kBAAAA;IACrEhM,UAAU,CAACzC,UAAqBsO,eAAeC,MAAM9L,SAASzC,KAAAA,GAAQU,SAAS8N,UAAUC,kBAAAA;EAC3F;AACA,SAAO,IAAIhB,MAAMtJ,OAAO;;;;IAItBuJ,eAAevK,QAAQmI,MAAM;AAC3B,aAAOnI,OAAOmI,IAAK;AACnB,aAAOiD,MAAMjD,IAAK;AAClB,aAAO;IACT;;;;IAKAtQ,IAAImI,QAAQmI,MAAc0D,UAAU;AAClC,aAAOpB,QAAQzK,QAAQmI,MACrB,MAAM2D,oBAAoB9L,QAAQmI,MAAM0D,QAAAA,CAAAA;IAC5C;;;;;IAMAlB,yBAAyB3K,QAAQmI,MAAM;AACrC,aAAOnI,OAAO/C,aAAa8O,UACvBnB,QAAQE,IAAIM,OAAOjD,IAAQ,IAAA;QAACrI,YAAY;QAAMkM,cAAc;MAAI,IAAIpW,SACpEgV,QAAQD,yBAAyBS,OAAOjD,IAAK;IACnD;;;;IAKA0C,iBAAiB;AACf,aAAOD,QAAQC,eAAeO,KAAAA;IAChC;;;;IAKAN,IAAI9K,QAAQmI,MAAM;AAChB,aAAOyC,QAAQE,IAAIM,OAAOjD,IAAAA;IAC5B;;;;IAKA6C,UAAU;AACR,aAAOJ,QAAQI,QAAQI,KAAAA;IACzB;;;;IAKAzV,IAAIqK,QAAQmI,MAAMrT,OAAO;AACvBsW,YAAMjD,IAAAA,IAAQrT;AACd,aAAOkL,OAAOmI,IAAK;AACnB,aAAO;IACT;EACF,CAAA;AACF;AAKO,SAASlL,aACdmO,OACA1V,YAA+B;EAACuW,YAAY;EAAMC,WAAW;AAAI,GACrD;AACZ,QAAM,EAAC7V,cAAcX,UAASuW,YAAY7V,aAAaV,UAASwW,WAAWC,WAAWzW,UAASqW,QAAO,IAAIX;AAC1G,SAAO;IACLW,SAASI;IACTF,YAAY5V;IACZ6V,WAAW9V;IACXgW,cAAcC,WAAWhW,WAAAA,IAAeA,cAAc,MAAMA;IAC5DiW,aAAaD,WAAWjW,UAAAA,IAAcA,aAAa,MAAMA;EAC3D;AACF;AAEA,IAAMmW,UAAU,CAACC,QAAgBlW,SAAiBkW,SAASA,SAASC,YAAYnW,IAAAA,IAAQA;AACxF,IAAMoW,mBAAmB,CAACvE,MAAcrT,UAAmBmL,SAASnL,KAAAA,KAAUqT,SAAS,eACpFvW,OAAOiZ,eAAe/V,KAAW,MAAA,QAAQA,MAAMkI,gBAAgBpL;AAElE,SAAS6Y,QACPzK,QACAmI,MACAU,UACA;AACA,MAAIjX,OAAO+a,UAAUC,eAAe5e,KAAKgS,QAAQmI,IAASA,KAAAA,SAAS,eAAe;AAChF,WAAOnI,OAAOmI,IAAK;;AAGrB,QAAMrT,QAAQ+T,SAAAA;AAEd7I,SAAOmI,IAAAA,IAAQrT;AACf,SAAOA;AACT;AAEA,SAASgX,oBACP9L,QACAmI,MACA0D,UACA;AACA,QAAM,EAACN,QAAQC,UAAUC,WAAWxO,cAAcd,aAAW,IAAI6D;AACjE,MAAIlL,QAAQyW,OAAOpD,IAAAA;AAGnB,MAAIkE,WAAWvX,KAAAA,KAAUqH,aAAYiQ,aAAajE,IAAO,GAAA;AACvDrT,YAAQ+X,mBAAmB1E,MAAMrT,OAAOkL,QAAQ6L,QAAAA;;AAElD,MAAIxT,QAAQvD,KAAAA,KAAUA,MAAMxF,QAAQ;AAClCwF,YAAQgY,cAAc3E,MAAMrT,OAAOkL,QAAQ7D,aAAYmQ,WAAW;;AAEpE,MAAII,iBAAiBvE,MAAMrT,KAAQ,GAAA;AAEjCA,YAAQqW,eAAerW,OAAO0W,UAAUC,aAAaA,UAAUtD,IAAAA,GAAOhM,YAAAA;;AAExE,SAAOrH;AACT;AAEA,SAAS+X,mBACP1E,MACA4E,UACA/M,QACA6L,UACA;AACA,QAAM,EAACN,QAAQC,UAAUC,WAAWC,OAAM,IAAI1L;AAC9C,MAAI0L,OAAOZ,IAAI3C,IAAO,GAAA;AACpB,UAAM,IAAI6E,MAAM,yBAAyBC,MAAMlX,KAAK2V,MAAAA,EAAQwB,KAAK,IAAQ,IAAA,OAAO/E,IAAM;;AAExFuD,SAAOpC,IAAInB,IAAAA;AACX,MAAIrT,QAAQiY,SAASvB,UAAUC,aAAaI,QAAAA;AAC5CH,SAAOyB,OAAOhF,IAAAA;AACd,MAAIuE,iBAAiBvE,MAAMrT,KAAQ,GAAA;AAEjCA,YAAQsY,kBAAkB7B,OAAOpB,SAASoB,QAAQpD,MAAMrT,KAAAA;;AAE1D,SAAOA;AACT;AAEA,SAASgY,cACP3E,MACArT,OACAkL,QACAsM,aACA;AACA,QAAM,EAACf,QAAQC,UAAUC,WAAWxO,cAAcd,aAAW,IAAI6D;AAEjE,MAAI,OAAOwL,SAAShT,UAAU,eAAe8T,YAAYnE,IAAO,GAAA;AAC9D,WAAOrT,MAAM0W,SAAShT,QAAQ1D,MAAMxF,MAAM;EAC5C,WAAW2Q,SAASnL,MAAM,CAAA,CAAE,GAAG;AAE7B,UAAMuY,MAAMvY;AACZ,UAAM4U,SAAS6B,OAAOpB,QAAQmD,OAAOrb,CAAAA,MAAKA,MAAMob,GAAAA;AAChDvY,YAAQ,CAAA;AACR,eAAWyY,QAAQF,KAAK;AACtB,YAAMG,WAAWJ,kBAAkB1D,QAAQ6B,QAAQpD,MAAMoF,IAAAA;AACzDzY,YAAM+L,KAAKsK,eAAeqC,UAAUhC,UAAUC,aAAaA,UAAUtD,IAAAA,GAAOhM,YAAAA,CAAAA;IAC9E;;AAEF,SAAOrH;AACT;AAEA,SAAS2Y,gBACPhF,UACAN,MACArT,OACA;AACA,SAAOuX,WAAW5D,QAAAA,IAAYA,SAASN,MAAMrT,KAAAA,IAAS2T;AACxD;AAEA,IAAMrM,WAAW,CAACE,KAAwBoR,WAAsBpR,QAAQ,OAAOoR,SAC3E,OAAOpR,QAAQ,WAAWqR,iBAAiBD,QAAQpR,GAAAA,IAAO1G;AAE9D,SAASgY,UACPjY,MACAkY,cACAvR,KACAwR,gBACAhZ,OACA;AACA,aAAW4Y,UAAUG,cAAc;AACjC,UAAMhR,QAAQT,SAASE,KAAKoR,MAAAA;AAC5B,QAAI7Q,OAAO;AACTlH,MAAAA,KAAI2T,IAAIzM,KAAAA;AACR,YAAM4L,WAAWgF,gBAAgB5Q,MAAM1G,WAAWmG,KAAKxH,KAAAA;AACvD,UAAI,OAAO2T,aAAa,eAAeA,aAAanM,OAAOmM,aAAaqF,gBAAgB;AAGtF,eAAOrF;;eAEA5L,UAAU,SAAS,OAAOiR,mBAAmB,eAAexR,QAAQwR,gBAAgB;AAG7F,aAAO;;EAEX;AACA,SAAO;AACT;AAEA,SAASV,kBACPS,cACAL,UACArF,MACArT,OACA;AACA,QAAM8U,aAAa4D,SAASpD;AAC5B,QAAM3B,WAAWgF,gBAAgBD,SAASrX,WAAWgS,MAAMrT,KAAAA;AAC3D,QAAMiZ,YAAY;IAAIF,GAAAA;IAAiBjE,GAAAA;EAAW;AAClD,QAAMjU,OAAM,oBAAIgW,IAAAA;AAChBhW,EAAAA,KAAI2T,IAAIxU,KAAAA;AACR,MAAIwH,MAAM0R,iBAAiBrY,MAAKoY,WAAW5F,MAAMM,YAAYN,MAAMrT,KAAAA;AACnE,MAAIwH,QAAQ,MAAM;AAChB,WAAO;;AAET,MAAI,OAAOmM,aAAa,eAAeA,aAAaN,MAAM;AACxD7L,UAAM0R,iBAAiBrY,MAAKoY,WAAWtF,UAAUnM,KAAKxH,KAAAA;AACtD,QAAIwH,QAAQ,MAAM;AAChB,aAAO;;;AAGX,SAAOmN,gBAAgBwD,MAAMlX,KAAKJ,IAAM,GAAA;IAAC;EAAG,GAAEiU,YAAYnB,UACxD,MAAMwF,aAAaT,UAAUrF,MAAgBrT,KAAAA,CAAAA;AACjD;AAEA,SAASkZ,iBACPrY,MACAoY,WACAzR,KACAmM,UACA8E,MACA;AACA,SAAOjR,KAAK;AACVA,UAAMsR,UAAUjY,MAAKoY,WAAWzR,KAAKmM,UAAU8E,IAAAA;EACjD;AACA,SAAOjR;AACT;AAEA,SAAS2R,aACPT,UACArF,MACArT,OACA;AACA,QAAM4Y,SAASF,SAASnD,WAAU;AAClC,MAAI,EAAElC,QAAQuF,SAAS;AACrBA,WAAOvF,IAAK,IAAG,CAAA;;AAEjB,QAAMnI,SAAS0N,OAAOvF,IAAK;AAC3B,MAAI9P,QAAQ2H,MAAWC,KAAAA,SAASnL,KAAQ,GAAA;AAEtC,WAAOA;;AAET,SAAOkL,UAAU,CAAA;AACnB;AAEA,SAAS0K,qBACPvC,MACAwB,UACAD,QACA0B,OACA;AACA,MAAItW;AACJ,aAAW0X,UAAU7C,UAAU;AAC7B7U,YAAQiV,SAASwC,QAAQC,QAAQrE,IAAOuB,GAAAA,MAAAA;AACxC,QAAI,OAAO5U,UAAU,aAAa;AAChC,aAAO4X,iBAAiBvE,MAAMrT,KAC1BsY,IAAAA,kBAAkB1D,QAAQ0B,OAAOjD,MAAMrT,KAAAA,IACvCA;;EAER;AACF;AAEA,SAASiV,SAASzN,KAAaoN,QAAqB;AAClD,aAAW7M,SAAS6M,QAAQ;AAC1B,QAAI,CAAC7M,OAAO;AACV;;AAEF,UAAM/H,QAAQ+H,MAAMP,GAAI;AACxB,QAAI,OAAOxH,UAAU,aAAa;AAChC,aAAOA;;EAEX;AACF;AAEA,SAASiW,qBAAqB/K,QAAuB;AACnD,MAAIzD,OAAOyD,OAAOwK;AAClB,MAAI,CAACjO,MAAM;AACTA,WAAOyD,OAAOwK,QAAQ0D,yBAAyBlO,OAAOmK,OAAO;;AAE/D,SAAO5N;AACT;AAEA,SAAS2R,yBAAyBxE,QAAqB;AACrD,QAAM/T,OAAM,oBAAIgW,IAAAA;AAChB,aAAW9O,SAAS6M,QAAQ;AAC1B,eAAWpN,OAAO1K,OAAO2K,KAAKM,KAAOyQ,EAAAA,OAAO3Q,CAAAA,MAAK,CAACA,EAAEX,WAAW,GAAO,CAAA,GAAA;AACpErG,MAAAA,KAAI2T,IAAIhN,GAAAA;IACV;EACF;AACA,SAAO2Q,MAAMlX,KAAKJ,IAAAA;AACpB;AAEO,SAASwY,4BACdjf,MACAqR,MACA7R,OACAa,OACA;AACA,QAAM,EAACE,OAAM,IAAIP;AACjB,QAAM,EAACoN,MAAM,IAAA,IAAO,KAAK8R;AACzB,QAAMC,SAAS,IAAIpB,MAAoB1d,KAAAA;AACvC,MAAIkN,GAAW0E,MAAc3I,OAAe+U;AAE5C,OAAK9Q,IAAI,GAAG0E,OAAO5R,OAAOkN,IAAI0E,MAAM,EAAE1E,GAAG;AACvCjE,YAAQiE,IAAI/N;AACZ6e,WAAOhN,KAAK/H,KAAM;AAClB6V,WAAO5R,CAAAA,IAAK;MACV6R,GAAG7e,OAAO8e,MAAMZ,iBAAiBJ,MAAMjR,GAAM9D,GAAAA,KAAAA;IAC/C;EACF;AACA,SAAO6V;AACT;AClcA,IAAMG,UAAUvH,OAAOuH,WAAW;AAGlC,IAAMC,WAAW,CAACtf,QAAuBsN,MAAmCA,IAAItN,OAAOG,UAAU,CAACH,OAAOsN,CAAE,EAACiS,QAAQvf,OAAOsN,CAAE;AAC7H,IAAMkS,eAAe,CAACpQ,cAAyBA,cAAc,MAAM,MAAM;AAElE,SAASqQ,YACdC,YACAC,aACAC,YACAhd,GAIE;AAMF,QAAM4S,WAAWkK,WAAWH,OAAOI,cAAcD;AACjD,QAAMG,UAAUF;AAChB,QAAMG,OAAOF,WAAWL,OAAOI,cAAcC;AAC7C,QAAMG,MAAMC,sBAAsBH,SAASrK,QAAAA;AAC3C,QAAMyK,MAAMD,sBAAsBF,MAAMD,OAAAA;AAExC,MAAIK,MAAMH,OAAOA,MAAME;AACvB,MAAIE,MAAMF,OAAOF,MAAME;AAGvBC,QAAMlW,MAAMkW,GAAO,IAAA,IAAIA;AACvBC,QAAMnW,MAAMmW,GAAO,IAAA,IAAIA;AAEvB,QAAMC,KAAKxd,IAAIsd;AACf,QAAMG,KAAKzd,IAAIud;AAEf,SAAO;IACL3K,UAAU;MACRpC,GAAGyM,QAAQzM,IAAIgN,MAAMN,KAAK1M,IAAIoC,SAASpC;MACvCC,GAAGwM,QAAQxM,IAAI+M,MAAMN,KAAKzM,IAAImC,SAASnC;IACzC;IACAyM,MAAM;MACJ1M,GAAGyM,QAAQzM,IAAIiN,MAAMP,KAAK1M,IAAIoC,SAASpC;MACvCC,GAAGwM,QAAQxM,IAAIgN,MAAMP,KAAKzM,IAAImC,SAASnC;IACzC;EACF;AACF;AAKA,SAASiN,eAAetgB,QAAuBugB,QAAkBC,IAAc;AAC7E,QAAMC,YAAYzgB,OAAOG;AAEzB,MAAIugB,QAAgBC,OAAeC,MAAcC,kBAA0BC;AAC3E,MAAIC,aAAazB,SAAStf,QAAQ,CAAA;AAClC,WAASsN,IAAI,GAAGA,IAAImT,YAAY,GAAG,EAAEnT,GAAG;AACtCwT,mBAAeC;AACfA,iBAAazB,SAAStf,QAAQsN,IAAI,CAAA;AAClC,QAAI,CAACwT,gBAAgB,CAACC,YAAY;AAChC;;AAGF,QAAIC,aAAaT,OAAOjT,CAAE,GAAE,GAAG+R,OAAU,GAAA;AACvCmB,SAAGlT,CAAE,IAAGkT,GAAGlT,IAAI,CAAA,IAAK;AACpB;;AAGFoT,aAASF,GAAGlT,CAAAA,IAAKiT,OAAOjT,CAAE;AAC1BqT,YAAQH,GAAGlT,IAAI,CAAA,IAAKiT,OAAOjT,CAAE;AAC7BuT,uBAAmB3f,KAAK8B,IAAI0d,QAAQ,CAAA,IAAKxf,KAAK8B,IAAI2d,OAAO,CAAA;AACzD,QAAIE,oBAAoB,GAAG;AACzB;;AAGFD,WAAO,IAAI1f,KAAK0D,KAAKic,gBAAAA;AACrBL,OAAGlT,CAAE,IAAGoT,SAASE,OAAOL,OAAOjT,CAAE;AACjCkT,OAAGlT,IAAI,CAAE,IAAGqT,QAAQC,OAAOL,OAAOjT,CAAE;EACtC;AACF;AAEA,SAAS2T,gBAAgBjhB,QAAuBwgB,IAAcpR,YAAuB,KAAK;AACxF,QAAM8R,YAAY1B,aAAapQ,SAAAA;AAC/B,QAAMqR,YAAYzgB,OAAOG;AACzB,MAAIsJ,OAAe0X,aAAkCL;AACrD,MAAIC,aAAazB,SAAStf,QAAQ,CAAA;AAElC,WAASsN,IAAI,GAAGA,IAAImT,WAAW,EAAEnT,GAAG;AAClC6T,kBAAcL;AACdA,mBAAeC;AACfA,iBAAazB,SAAStf,QAAQsN,IAAI,CAAA;AAClC,QAAI,CAACwT,cAAc;AACjB;;AAGF,UAAMM,SAASN,aAAa1R,SAAU;AACtC,UAAMiS,SAASP,aAAaI,SAAU;AACtC,QAAIC,aAAa;AACf1X,eAAS2X,SAASD,YAAY/R,SAAAA,KAAc;AAC5C0R,mBAAa,MAAM1R,SAAAA,EAAW,IAAIgS,SAAS3X;AAC3CqX,mBAAa,MAAMI,SAAU,EAAC,IAAIG,SAAS5X,QAAQ+W,GAAGlT,CAAE;;AAE1D,QAAIyT,YAAY;AACdtX,eAASsX,WAAW3R,SAAU,IAAGgS,UAAU;AAC3CN,mBAAa,MAAM1R,SAAAA,EAAW,IAAIgS,SAAS3X;AAC3CqX,mBAAa,MAAMI,SAAU,EAAC,IAAIG,SAAS5X,QAAQ+W,GAAGlT,CAAE;;EAE5D;AACF;AAQO,SAASgU,oBAAoBthB,QAAuBoP,YAAuB,KAAK;AACrF,QAAM8R,YAAY1B,aAAapQ,SAAAA;AAC/B,QAAMqR,YAAYzgB,OAAOG;AACzB,QAAMogB,SAAmBzC,MAAM2C,SAAW1L,EAAAA,KAAK,CAAA;AAC/C,QAAMyL,KAAe1C,MAAM2C,SAAAA;AAG3B,MAAInT,GAAG6T,aAAkCL;AACzC,MAAIC,aAAazB,SAAStf,QAAQ,CAAA;AAElC,OAAKsN,IAAI,GAAGA,IAAImT,WAAW,EAAEnT,GAAG;AAC9B6T,kBAAcL;AACdA,mBAAeC;AACfA,iBAAazB,SAAStf,QAAQsN,IAAI,CAAA;AAClC,QAAI,CAACwT,cAAc;AACjB;;AAGF,QAAIC,YAAY;AACd,YAAMQ,aAAaR,WAAW3R,SAAAA,IAAa0R,aAAa1R,SAAU;AAGlEmR,aAAOjT,CAAE,IAAGiU,eAAe,KAAKR,WAAWG,SAAAA,IAAaJ,aAAaI,SAAAA,KAAcK,aAAa;;AAElGf,OAAGlT,CAAE,IAAG,CAAC6T,cAAcZ,OAAOjT,CAAE,IAC5B,CAACyT,aAAaR,OAAOjT,IAAI,CAAA,IACtBkU,KAAKjB,OAAOjT,IAAI,CAAA,CAAE,MAAMkU,KAAKjB,OAAOjT,CAAE,CAAA,IAAK,KACzCiT,OAAOjT,IAAI,CAAA,IAAKiT,OAAOjT,CAAE,KAAI;EACxC;AAEAgT,iBAAetgB,QAAQugB,QAAQC,EAAAA;AAE/BS,kBAAgBjhB,QAAQwgB,IAAIpR,SAAAA;AAC9B;AAEA,SAASqS,gBAAgBC,IAAY7gB,KAAaC,KAAa;AAC7D,SAAOI,KAAKJ,IAAII,KAAKL,IAAI6gB,IAAI5gB,GAAMD,GAAAA,GAAAA;AACrC;AAEA,SAAS8gB,gBAAgB3hB,QAAuBmV,MAAiB;AAC/D,MAAI7H,GAAG0E,MAAMtQ,OAAOkgB,QAAQC;AAC5B,MAAIC,aAAa5M,eAAelV,OAAO,CAAA,GAAImV,IAAAA;AAC3C,OAAK7H,IAAI,GAAG0E,OAAOhS,OAAOG,QAAQmN,IAAI0E,MAAM,EAAE1E,GAAG;AAC/CuU,iBAAaD;AACbA,aAASE;AACTA,iBAAaxU,IAAI0E,OAAO,KAAKkD,eAAelV,OAAOsN,IAAI,CAAA,GAAI6H,IAAAA;AAC3D,QAAI,CAACyM,QAAQ;AACX;;AAEFlgB,YAAQ1B,OAAOsN,CAAE;AACjB,QAAIuU,YAAY;AACdngB,YAAMmU,OAAO4L,gBAAgB/f,MAAMmU,MAAMV,KAAKzV,MAAMyV,KAAKxV,KAAK;AAC9D+B,YAAMqU,OAAO0L,gBAAgB/f,MAAMqU,MAAMZ,KAAKnN,KAAKmN,KAAKlN,MAAM;;AAEhE,QAAI6Z,YAAY;AACdpgB,YAAMoU,OAAO2L,gBAAgB/f,MAAMoU,MAAMX,KAAKzV,MAAMyV,KAAKxV,KAAK;AAC9D+B,YAAMsU,OAAOyL,gBAAgB/f,MAAMsU,MAAMb,KAAKnN,KAAKmN,KAAKlN,MAAM;;EAElE;AACF;AAKO,SAAS8Z,2BACd/hB,QACAW,SACAwU,MACAtO,MACAuI,WACA;AACA,MAAI9B,GAAW0E,MAActQ,OAAoBsgB;AAGjD,MAAIrhB,QAAQF,UAAU;AACpBT,aAASA,OAAOme,OAAO,CAACuD,OAAO,CAACA,GAAGnC,IAAI;;AAGzC,MAAI5e,QAAQshB,2BAA2B,YAAY;AACjDX,wBAAoBthB,QAAQoP,SAAAA;SACvB;AACL,QAAI8S,OAAOrb,OAAO7G,OAAOA,OAAOG,SAAS,CAAE,IAAGH,OAAO,CAAE;AACvD,SAAKsN,IAAI,GAAG0E,OAAOhS,OAAOG,QAAQmN,IAAI0E,MAAM,EAAE1E,GAAG;AAC/C5L,cAAQ1B,OAAOsN,CAAE;AACjB0U,sBAAgBvC,YACdyC,MACAxgB,OACA1B,OAAOkB,KAAKL,IAAIyM,IAAI,GAAG0E,QAAQnL,OAAO,IAAI,EAAA,IAAMmL,IAAK,GACrDrR,QAAQwhB,OAAO;AAEjBzgB,YAAMmU,OAAOmM,cAAcxM,SAASpC;AACpC1R,YAAMqU,OAAOiM,cAAcxM,SAASnC;AACpC3R,YAAMoU,OAAOkM,cAAclC,KAAK1M;AAChC1R,YAAMsU,OAAOgM,cAAclC,KAAKzM;AAChC6O,aAAOxgB;IACT;;AAGF,MAAIf,QAAQghB,iBAAiB;AAC3BA,oBAAgB3hB,QAAQmV,IAAAA;;AAE5B;ACtNO,SAASiN,kBAA2B;AACzC,SAAO,OAAOhkB,WAAW,eAAe,OAAOikB,aAAa;AAC9D;AAKO,SAASC,eAAeC,SAA+C;AAC5E,MAAIhE,SAASgE,QAAQC;AACrB,MAAIjE,UAAUA,OAAO1Y,SAAQ,MAAO,uBAAuB;AACzD0Y,aAAUA,OAAsBkE;;AAElC,SAAOlE;AACT;AAOA,SAASmE,cAAcC,YAA6BzV,MAAmB0V,gBAAwB;AAC7F,MAAIC;AACJ,MAAI,OAAOF,eAAe,UAAU;AAClCE,oBAAgBtJ,SAASoJ,YAAY,EAAA;AAErC,QAAIA,WAAWG,QAAQ,GAAA,MAAS,IAAI;AAElCD,sBAAgB,gBAAiB,MAAO3V,KAAKsV,WAAWI,cAAe;;SAEpE;AACLC,oBAAgBF;;AAGlB,SAAOE;AACT;AAEA,IAAME,mBAAmB,CAACC,YACxBA,QAAQC,cAAcC,YAAYH,iBAAiBC,SAAS,IAAI;AAE3D,SAASG,SAASC,IAAiBC,UAA0B;AAClE,SAAON,iBAAiBK,EAAIE,EAAAA,iBAAiBD,QAAAA;AAC/C;AAEA,IAAME,YAAY;EAAC;EAAO;EAAS;EAAU;AAAO;AACpD,SAASC,mBAAmBC,QAA6B7U,OAAe8U,QAA4B;AAClG,QAAMC,SAAS,CAAA;AACfD,WAASA,SAAS,MAAMA,SAAS;AACjC,WAASpW,IAAI,GAAGA,IAAI,GAAGA,KAAK;AAC1B,UAAMsW,MAAML,UAAUjW,CAAE;AACxBqW,WAAOC,GAAI,IAAGC,WAAWJ,OAAO7U,QAAQ,MAAMgV,MAAMF,MAAAA,CAAO,KAAK;EAClE;AACAC,SAAOjY,QAAQiY,OAAOjkB,OAAOikB,OAAOhkB;AACpCgkB,SAAOzQ,SAASyQ,OAAO3b,MAAM2b,OAAO1b;AACpC,SAAO0b;AACT;AAEA,IAAMG,eAAe,CAAC1Q,GAAWC,GAAWxC,YACzCuC,IAAI,KAAKC,IAAI,OAAO,CAACxC,UAAU,CAAC,OAAwBkT;AAO3D,SAASC,kBACPC,GACAnR,QAKE;AACF,QAAMoR,UAAU,EAAkBA;AAClC,QAAMC,SAAUD,WAAWA,QAAQ/jB,SAAS+jB,QAAQ,CAAE,IAAGD;AACzD,QAAM,EAACG,SAASC,QAAAA,IAAWF;AAC3B,MAAIG,MAAM;AACV,MAAIlR,GAAGC;AACP,MAAIyQ,aAAaM,SAASC,SAASJ,EAAEpT,MAAM,GAAG;AAC5CuC,QAAIgR;AACJ/Q,QAAIgR;SACC;AACL,UAAMvP,OAAOhC,OAAOyR,sBAAqB;AACzCnR,QAAI+Q,OAAOK,UAAU1P,KAAKpV;AAC1B2T,QAAI8Q,OAAOM,UAAU3P,KAAK9M;AAC1Bsc,UAAM;;AAER,SAAO;IAAClR;IAAGC;IAAGiR;EAAG;AACnB;AASO,SAASI,oBACdC,OACApb,OAC0B;AAC1B,MAAI,YAAYob,OAAO;AACrB,WAAOA;;AAGT,QAAM,EAAC7R,QAAQJ,wBAAAA,IAA2BnJ;AAC1C,QAAMqF,QAAQmU,iBAAiBjQ,MAAAA;AAC/B,QAAM8R,YAAYhW,MAAMiW,cAAc;AACtC,QAAMC,WAAWtB,mBAAmB5U,OAAO,SAAA;AAC3C,QAAMmW,UAAUvB,mBAAmB5U,OAAO,UAAU,OAAA;AACpD,QAAM,EAACwE,GAAGC,GAAGiR,IAAG,IAAIN,kBAAkBW,OAAO7R,MAAAA;AAC7C,QAAMU,UAAUsR,SAASplB,QAAQ4kB,OAAOS,QAAQrlB;AAChD,QAAM+T,UAAUqR,SAAS9c,OAAOsc,OAAOS,QAAQ/c;AAE/C,MAAI,EAAC0D,OAAOwH,OAAAA,IAAU3J;AACtB,MAAIqb,WAAW;AACblZ,aAASoZ,SAASpZ,QAAQqZ,QAAQrZ;AAClCwH,cAAU4R,SAAS5R,SAAS6R,QAAQ7R;;AAEtC,SAAO;IACLE,GAAGlS,KAAK0R,OAAOQ,IAAII,WAAW9H,QAAQoH,OAAOpH,QAAQgH,uBAAAA;IACrDW,GAAGnS,KAAK0R,OAAOS,IAAII,WAAWP,SAASJ,OAAOI,SAASR,uBAAAA;EACzD;AACF;AAEA,SAASsS,iBAAiBlS,QAA2BpH,OAAewH,QAAgC;AAClG,MAAI0E,UAAkBqN;AAEtB,MAAIvZ,UAAUjF,UAAayM,WAAWzM,QAAW;AAC/C,UAAMye,YAAYpS,UAAUwP,eAAexP,MAAAA;AAC3C,QAAI,CAACoS,WAAW;AACdxZ,cAAQoH,OAAOqS;AACfjS,eAASJ,OAAOsS;WACX;AACL,YAAMtQ,OAAOoQ,UAAUX,sBAAqB;AAC5C,YAAMc,iBAAiBtC,iBAAiBmC,SAAAA;AACxC,YAAMI,kBAAkB9B,mBAAmB6B,gBAAgB,UAAU,OAAA;AACrE,YAAME,mBAAmB/B,mBAAmB6B,gBAAgB,SAAA;AAC5D3Z,cAAQoJ,KAAKpJ,QAAQ6Z,iBAAiB7Z,QAAQ4Z,gBAAgB5Z;AAC9DwH,eAAS4B,KAAK5B,SAASqS,iBAAiBrS,SAASoS,gBAAgBpS;AACjE0E,iBAAW8K,cAAc2C,eAAezN,UAAUsN,WAAW,aAAA;AAC7DD,kBAAYvC,cAAc2C,eAAeJ,WAAWC,WAAW,cAAA;;;AAGnE,SAAO;IACLxZ;IACAwH;IACA0E,UAAUA,YAAY4N;IACtBP,WAAWA,aAAaO;EAC1B;AACF;AAEA,IAAMC,SAAS,CAAC7d,MAAc1G,KAAK0R,MAAMhL,IAAI,EAAM,IAAA;AAG5C,SAAS8d,eACd5S,QACA6S,SACAC,UACAC,aACmC;AACnC,QAAMjX,QAAQmU,iBAAiBjQ,MAAAA;AAC/B,QAAMgT,UAAUtC,mBAAmB5U,OAAO,QAAA;AAC1C,QAAMgJ,WAAW8K,cAAc9T,MAAMgJ,UAAU9E,QAAQ,aAAkB0S,KAAAA;AACzE,QAAMP,YAAYvC,cAAc9T,MAAMqW,WAAWnS,QAAQ,cAAmB0S,KAAAA;AAC5E,QAAMO,gBAAgBf,iBAAiBlS,QAAQ6S,SAASC,QAAAA;AACxD,MAAI,EAACla,OAAOwH,OAAAA,IAAU6S;AAEtB,MAAInX,MAAMiW,cAAc,eAAe;AACrC,UAAME,UAAUvB,mBAAmB5U,OAAO,UAAU,OAAA;AACpD,UAAMkW,WAAWtB,mBAAmB5U,OAAO,SAAA;AAC3ClD,aAASoZ,SAASpZ,QAAQqZ,QAAQrZ;AAClCwH,cAAU4R,SAAS5R,SAAS6R,QAAQ7R;;AAEtCxH,UAAQxK,KAAKJ,IAAI,GAAG4K,QAAQoa,QAAQpa,KAAK;AACzCwH,WAAShS,KAAKJ,IAAI,GAAG+kB,cAAcna,QAAQma,cAAc3S,SAAS4S,QAAQ5S,MAAM;AAChFxH,UAAQ+Z,OAAOvkB,KAAKL,IAAI6K,OAAOkM,UAAUmO,cAAcnO,QAAQ,CAAA;AAC/D1E,WAASuS,OAAOvkB,KAAKL,IAAIqS,QAAQ+R,WAAWc,cAAcd,SAAS,CAAA;AACnE,MAAIvZ,SAAS,CAACwH,QAAQ;AAGpBA,aAASuS,OAAO/Z,QAAQ,CAAA;;AAG1B,QAAMsa,iBAAiBL,YAAYlf,UAAamf,aAAanf;AAE7D,MAAIuf,kBAAkBH,eAAeE,cAAc7S,UAAUA,SAAS6S,cAAc7S,QAAQ;AAC1FA,aAAS6S,cAAc7S;AACvBxH,YAAQ+Z,OAAOvkB,KAAK+I,MAAMiJ,SAAS2S,WAAAA,CAAAA;;AAGrC,SAAO;IAACna;IAAOwH;EAAM;AACvB;AAQO,SAAS+S,YACd1c,OACA2c,YACAC,YACgB;AAChB,QAAMC,aAAaF,cAAc;AACjC,QAAMG,eAAenlB,KAAK+I,MAAMV,MAAM2J,SAASkT,UAAAA;AAC/C,QAAME,cAAcplB,KAAK+I,MAAMV,MAAMmC,QAAQ0a,UAAAA;AAE5C7c,QAAuB2J,SAAShS,KAAK+I,MAAMV,MAAM2J,MAAM;AACvD3J,QAAuBmC,QAAQxK,KAAK+I,MAAMV,MAAMmC,KAAK;AAEtD,QAAMoH,SAASvJ,MAAMuJ;AAKrB,MAAIA,OAAOlE,UAAUuX,cAAe,CAACrT,OAAOlE,MAAMsE,UAAU,CAACJ,OAAOlE,MAAMlD,QAAS;AACjFoH,WAAOlE,MAAMsE,SAAS,GAAG3J,MAAM2J,MAAM;AACrCJ,WAAOlE,MAAMlD,QAAQ,GAAGnC,MAAMmC,KAAK;;AAGrC,MAAInC,MAAMmJ,4BAA4B0T,cAC/BtT,OAAOI,WAAWmT,gBAClBvT,OAAOpH,UAAU4a,aAAa;AAClC/c,UAAuBmJ,0BAA0B0T;AAClDtT,WAAOI,SAASmT;AAChBvT,WAAOpH,QAAQ4a;AACf/c,UAAM0F,IAAIsX,aAAaH,YAAY,GAAG,GAAGA,YAAY,GAAG,CAAA;AACxD,WAAO;;AAET,SAAO;AACT;AAOaI,IAAAA,+BAAgC,WAAW;AACtD,MAAIC,mBAAmB;AACvB,MAAI;AACF,UAAM9lB,UAAU;MACd,IAAI+lB,UAAU;AACZD,2BAAmB;AACnB,eAAO;MACT;IACF;AAEA,QAAIrE,gBAAmB,GAAA;AACrBhkB,aAAOuoB,iBAAiB,QAAQ,MAAMhmB,OAAAA;AACtCvC,aAAOwoB,oBAAoB,QAAQ,MAAMjmB,OAAAA;;EAE7C,SAASsjB,GAAG;EAEZ;AACA,SAAOwC;AACT,EAAK;AAYE,SAASI,aACd7D,SACAK,UACoB;AACpB,QAAM1d,QAAQwd,SAASH,SAASK,QAAAA;AAChC,QAAM7K,UAAU7S,SAASA,MAAM8S,MAAM,mBAAA;AACrC,SAAOD,UAAU,CAACA,QAAQ,CAAA,IAAK/R;AACjC;ACtRO,SAASqgB,aAAaC,IAAWC,IAAWpkB,GAAW0M,MAAO;AACnE,SAAO;IACL8D,GAAG2T,GAAG3T,IAAIxQ,KAAKokB,GAAG5T,IAAI2T,GAAG3T;IACzBC,GAAG0T,GAAG1T,IAAIzQ,KAAKokB,GAAG3T,IAAI0T,GAAG1T;EAC3B;AACF;AAKO,SAAS4T,sBACdF,IACAC,IACApkB,GAAW0M,MACX;AACA,SAAO;IACL8D,GAAG2T,GAAG3T,IAAIxQ,KAAKokB,GAAG5T,IAAI2T,GAAG3T;IACzBC,GAAG/D,SAAS,WAAW1M,IAAI,MAAMmkB,GAAG1T,IAAI2T,GAAG3T,IACvC/D,SAAS,UAAU1M,IAAI,IAAImkB,GAAG1T,IAAI2T,GAAG3T,IACnCzQ,IAAI,IAAIokB,GAAG3T,IAAI0T,GAAG1T;EAC1B;AACF;AAKO,SAAS6T,qBAAqBH,IAAiBC,IAAiBpkB,GAAW0M,MAAO;AACvF,QAAM6X,MAAM;IAAC/T,GAAG2T,GAAGjR;IAAMzC,GAAG0T,GAAG/Q;EAAI;AACnC,QAAMoR,MAAM;IAAChU,GAAG4T,GAAGnR;IAAMxC,GAAG2T,GAAGjR;EAAI;AACnC,QAAMsR,IAAIP,aAAaC,IAAII,KAAKvkB,CAAAA;AAChC,QAAM0kB,IAAIR,aAAaK,KAAKC,KAAKxkB,CAAAA;AACjC,QAAM2kB,IAAIT,aAAaM,KAAKJ,IAAIpkB,CAAAA;AAChC,QAAM4C,IAAIshB,aAAaO,GAAGC,GAAG1kB,CAAAA;AAC7B,QAAMqhB,IAAI6C,aAAaQ,GAAGC,GAAG3kB,CAAAA;AAC7B,SAAOkkB,aAAathB,GAAGye,GAAGrhB,CAAAA;AAC5B;AChCA,IAAM4kB,wBAAwB,SAASC,OAAe/b,OAA2B;AAC/E,SAAO;IACL0H,EAAEA,GAAG;AACH,aAAOqU,QAAQA,QAAQ/b,QAAQ0H;IACjC;IACAsU,SAASnU,GAAG;AACV7H,cAAQ6H;IACV;IACA8C,UAAUhX,OAAO;AACf,UAAIA,UAAU,UAAU;AACtB,eAAOA;;AAET,aAAOA,UAAU,UAAU,SAAS;IACtC;IACAsoB,MAAMvU,GAAGzN,OAAO;AACd,aAAOyN,IAAIzN;IACb;IACAiiB,WAAWxU,GAAGyU,WAAW;AACvB,aAAOzU,IAAIyU;IACb;EACF;AACF;AAEA,IAAMC,wBAAwB,WAAuB;AACnD,SAAO;IACL1U,EAAEA,GAAG;AACH,aAAOA;IACT;IACAsU,SAASnU,GAAG;IAAA;IAEZ8C,UAAUhX,OAAO;AACf,aAAOA;IACT;IACAsoB,MAAMvU,GAAGzN,OAAO;AACd,aAAOyN,IAAIzN;IACb;IACAiiB,WAAWxU,GAAG2U,YAAY;AACxB,aAAO3U;IACT;EACF;AACF;AAEO,SAAS4U,cAAcpoB,KAAc6nB,OAAe/b,OAAe;AACxE,SAAO9L,MAAM4nB,sBAAsBC,OAAO/b,KAAAA,IAASoc,sBAAuB;AAC5E;AAEO,SAASG,sBAAsBhZ,KAA+BiZ,WAA0B;AAC7F,MAAItZ,OAA4BuZ;AAChC,MAAID,cAAc,SAASA,cAAc,OAAO;AAC9CtZ,YAAQK,IAAI6D,OAAOlE;AACnBuZ,eAAW;MACTvZ,MAAM0U,iBAAiB,WAAA;MACvB1U,MAAMwZ,oBAAoB,WAAA;IAC3B;AAEDxZ,UAAMyZ,YAAY,aAAaH,WAAW,WAAA;AACzCjZ,QAAiDqZ,oBAAoBH;;AAE1E;AAEO,SAASI,qBAAqBtZ,KAA+BkZ,UAA6B;AAC/F,MAAIA,aAAa1hB,QAAW;AAC1B,WAAQwI,IAAiDqZ;AACzDrZ,QAAI6D,OAAOlE,MAAMyZ,YAAY,aAAaF,SAAS,CAAA,GAAIA,SAAS,CAAE,CAAA;;AAEtE;AC/DA,SAASK,WAAWnF,UAAU;AAC5B,MAAIA,aAAa,SAAS;AACxB,WAAO;MACLoF,SAASC;MACTC,SAASC;MACTC,WAAWC;IACb;;AAEF,SAAO;IACLL,SAASM;IACTJ,SAAS,CAACtB,GAAGC,MAAMD,IAAIC;IACvBuB,WAAWzV,CAAAA,MAAKA;EAClB;AACF;AAEA,SAAS4V,iBAAiB,EAACzpB,OAAOC,KAAKY,OAAOyG,MAAM+H,MAAK,GAAG;AAC1D,SAAO;IACLrP,OAAOA,QAAQa;IACfZ,KAAKA,MAAMY;IACXyG,MAAMA,SAASrH,MAAMD,QAAQ,KAAKa,UAAU;IAC5CwO;EACF;AACF;AAEA,SAASqa,WAAWC,SAASlpB,QAAQ4K,QAAQ;AAC3C,QAAM,EAACyY,UAAU9jB,OAAO4pB,YAAY3pB,KAAK4pB,SAAQ,IAAIxe;AACrD,QAAM,EAAC6d,SAASI,UAAS,IAAIL,WAAWnF,QAAAA;AACxC,QAAMjjB,QAAQJ,OAAOG;AAErB,MAAI,EAACZ,OAAOC,KAAKqH,KAAAA,IAAQqiB;AACzB,MAAI5b,GAAG0E;AAEP,MAAInL,MAAM;AACRtH,aAASa;AACTZ,WAAOY;AACP,SAAKkN,IAAI,GAAG0E,OAAO5R,OAAOkN,IAAI0E,MAAM,EAAE1E,GAAG;AACvC,UAAI,CAACmb,QAAQI,UAAU7oB,OAAOT,QAAQa,KAAAA,EAAOijB,QAAAA,CAAS,GAAG8F,YAAYC,QAAW,GAAA;AAC9E;;AAEF7pB;AACAC;IACF;AACAD,aAASa;AACTZ,WAAOY;;AAGT,MAAIZ,MAAMD,OAAO;AACfC,WAAOY;;AAET,SAAO;IAACb;IAAOC;IAAKqH;IAAM+H,OAAOsa,QAAQta;EAAK;AAChD;AAgBO,SAASya,cAAcH,SAASlpB,QAAQ4K,QAAQ;AACrD,MAAI,CAACA,QAAQ;AACX,WAAO;MAACse;IAAQ;;AAGlB,QAAM,EAAC7F,UAAU9jB,OAAO4pB,YAAY3pB,KAAK4pB,SAAQ,IAAIxe;AACrD,QAAMxK,QAAQJ,OAAOG;AACrB,QAAM,EAACwoB,SAASF,SAASI,UAAS,IAAIL,WAAWnF,QAAAA;AACjD,QAAM,EAAC9jB,OAAOC,KAAKqH,MAAM+H,MAAAA,IAASqa,WAAWC,SAASlpB,QAAQ4K,MAAAA;AAE9D,QAAM+Y,SAAS,CAAA;AACf,MAAI2F,SAAS;AACb,MAAIC,WAAW;AACf,MAAI5jB,OAAOjE,OAAO8nB;AAElB,QAAMC,gBAAgB,MAAMhB,QAAQU,YAAYK,WAAW7jB,KAAUgjB,KAAAA,QAAQQ,YAAYK,SAAe,MAAA;AACxG,QAAME,cAAc,MAAMf,QAAQS,UAAUzjB,KAAAA,MAAW,KAAK8iB,QAAQW,UAAUI,WAAW7jB,KAAAA;AACzF,QAAMgkB,cAAc,MAAML,UAAUG,cAAAA;AACpC,QAAMG,aAAa,MAAM,CAACN,UAAUI,YAAAA;AAEpC,WAASpc,IAAI/N,OAAO2iB,OAAO3iB,OAAO+N,KAAK9N,KAAK,EAAE8N,GAAG;AAC/C5L,YAAQ1B,OAAOsN,IAAIlN,KAAM;AAEzB,QAAIsB,MAAM6d,MAAM;AACd;;AAGF5Z,YAAQkjB,UAAUnnB,MAAM2hB,QAAS,CAAA;AAEjC,QAAI1d,UAAU6jB,WAAW;AACvB;;AAGFF,aAASb,QAAQ9iB,OAAOwjB,YAAYC,QAAAA;AAEpC,QAAIG,aAAa,QAAQI,YAAe,GAAA;AACtCJ,iBAAWZ,QAAQhjB,OAAOwjB,UAAgB,MAAA,IAAI7b,IAAI4U;;AAGpD,QAAIqH,aAAa,QAAQK,WAAc,GAAA;AACrCjG,aAAOjS,KAAKsX,iBAAiB;QAACzpB,OAAOgqB;QAAU/pB,KAAK8N;QAAGzG;QAAMzG;QAAOwO;MAAK,CAAA,CAAA;AACzE2a,iBAAW;;AAEbrH,WAAO5U;AACPkc,gBAAY7jB;EACd;AAEA,MAAI4jB,aAAa,MAAM;AACrB5F,WAAOjS,KAAKsX,iBAAiB;MAACzpB,OAAOgqB;MAAU/pB;MAAKqH;MAAMzG;MAAOwO;IAAK,CAAA,CAAA;;AAGxE,SAAO+U;AACT;AAYO,SAASkG,eAAerT,MAAM5L,QAAQ;AAC3C,QAAM+Y,SAAS,CAAA;AACf,QAAMmG,WAAWtT,KAAKsT;AAEtB,WAASxc,IAAI,GAAGA,IAAIwc,SAAS3pB,QAAQmN,KAAK;AACxC,UAAMyc,MAAMV,cAAcS,SAASxc,CAAAA,GAAIkJ,KAAKxW,QAAQ4K,MAAAA;AACpD,QAAImf,IAAI5pB,QAAQ;AACdwjB,aAAOjS,KAAQqY,GAAAA,GAAAA;;EAEnB;AACA,SAAOpG;AACT;AAKA,SAASqG,gBAAgBhqB,QAAQI,OAAOyG,MAAMpG,UAAU;AACtD,MAAIlB,QAAQ;AACZ,MAAIC,MAAMY,QAAQ;AAElB,MAAIyG,QAAQ,CAACpG,UAAU;AAErB,WAAOlB,QAAQa,SAAS,CAACJ,OAAOT,KAAM,EAACggB,MAAM;AAC3ChgB;IACF;;AAIF,SAAOA,QAAQa,SAASJ,OAAOT,KAAM,EAACggB,MAAM;AAC1ChgB;EACF;AAGAA,WAASa;AAET,MAAIyG,MAAM;AAERrH,WAAOD;;AAGT,SAAOC,MAAMD,SAASS,OAAOR,MAAMY,KAAM,EAACmf,MAAM;AAC9C/f;EACF;AAGAA,SAAOY;AAEP,SAAO;IAACb;IAAOC;EAAG;AACpB;AASA,SAASyqB,cAAcjqB,QAAQT,OAAOuB,KAAK+F,MAAM;AAC/C,QAAMzG,QAAQJ,OAAOG;AACrB,QAAMwjB,SAAS,CAAA;AACf,MAAIuG,OAAO3qB;AACX,MAAI2iB,OAAOliB,OAAOT,KAAM;AACxB,MAAIC;AAEJ,OAAKA,MAAMD,QAAQ,GAAGC,OAAOsB,KAAK,EAAEtB,KAAK;AACvC,UAAM2qB,MAAMnqB,OAAOR,MAAMY,KAAM;AAC/B,QAAI+pB,IAAI5K,QAAQ4K,IAAIC,MAAM;AACxB,UAAI,CAAClI,KAAK3C,MAAM;AACd1Y,eAAO;AACP8c,eAAOjS,KAAK;UAACnS,OAAOA,QAAQa;UAAOZ,MAAMA,MAAM,KAAKY;UAAOyG;QAAI,CAAA;AAE/DtH,gBAAQ2qB,OAAOC,IAAIC,OAAO5qB,MAAM;;WAE7B;AACL0qB,aAAO1qB;AACP,UAAI0iB,KAAK3C,MAAM;AACbhgB,gBAAQC;;;AAGZ0iB,WAAOiI;EACT;AAEA,MAAID,SAAS,MAAM;AACjBvG,WAAOjS,KAAK;MAACnS,OAAOA,QAAQa;MAAOZ,KAAK0qB,OAAO9pB;MAAOyG;IAAI,CAAA;;AAG5D,SAAO8c;AACT;AAUO,SAAS0G,iBAAiB7T,MAAM8T,gBAAgB;AACrD,QAAMtqB,SAASwW,KAAKxW;AACpB,QAAMS,WAAW+V,KAAK7V,QAAQF;AAC9B,QAAML,QAAQJ,OAAOG;AAErB,MAAI,CAACC,OAAO;AACV,WAAO,CAAA;;AAGT,QAAMyG,OAAO,CAAC,CAAC2P,KAAK+T;AACpB,QAAM,EAAChrB,OAAOC,IAAAA,IAAOwqB,gBAAgBhqB,QAAQI,OAAOyG,MAAMpG,QAAAA;AAE1D,MAAIA,aAAa,MAAM;AACrB,WAAO+pB,cAAchU,MAAM;MAAC;QAACjX;QAAOC;QAAKqH;MAAI;IAAE,GAAE7G,QAAQsqB,cAAAA;;AAG3D,QAAMxpB,MAAMtB,MAAMD,QAAQC,MAAMY,QAAQZ;AACxC,QAAMirB,eAAe,CAAC,CAACjU,KAAKkU,aAAanrB,UAAU,KAAKC,QAAQY,QAAQ;AACxE,SAAOoqB,cAAchU,MAAMyT,cAAcjqB,QAAQT,OAAOuB,KAAK2pB,YAAAA,GAAezqB,QAAQsqB,cAAAA;AACtF;AAQA,SAASE,cAAchU,MAAMsT,UAAU9pB,QAAQsqB,gBAAgB;AAC7D,MAAI,CAACA,kBAAkB,CAACA,eAAe7N,cAAc,CAACzc,QAAQ;AAC5D,WAAO8pB;;AAET,SAAOa,gBAAgBnU,MAAMsT,UAAU9pB,QAAQsqB,cAAAA;AACjD;AASA,SAASK,gBAAgBnU,MAAMsT,UAAU9pB,QAAQsqB,gBAAgB;AAC/D,QAAMM,eAAepU,KAAKqU,OAAO9X,WAAU;AAC3C,QAAM+X,YAAYC,UAAUvU,KAAK7V,OAAO;AACxC,QAAM,EAACqqB,eAAeC,cAActqB,SAAS,EAACF,SAAQ,EAAC,IAAI+V;AAC3D,QAAMpW,QAAQJ,OAAOG;AACrB,QAAMwjB,SAAS,CAAA;AACf,MAAIuH,YAAYJ;AAChB,MAAIvrB,QAAQuqB,SAAS,CAAA,EAAGvqB;AACxB,MAAI+N,IAAI/N;AAER,WAAS4rB,SAASroB,GAAGmhB,GAAGmH,GAAGC,IAAI;AAC7B,UAAMC,MAAM7qB,WAAW,KAAK;AAC5B,QAAIqC,MAAMmhB,GAAG;AACX;;AAGFnhB,SAAK1C;AACL,WAAOJ,OAAO8C,IAAI1C,KAAM,EAACmf,MAAM;AAC7Bzc,WAAKwoB;IACP;AACA,WAAOtrB,OAAOikB,IAAI7jB,KAAM,EAACmf,MAAM;AAC7B0E,WAAKqH;IACP;AACA,QAAIxoB,IAAI1C,UAAU6jB,IAAI7jB,OAAO;AAC3BujB,aAAOjS,KAAK;QAACnS,OAAOuD,IAAI1C;QAAOZ,KAAKykB,IAAI7jB;QAAOyG,MAAMukB;QAAGxc,OAAOyc;MAAE,CAAA;AACjEH,kBAAYG;AACZ9rB,cAAQ0kB,IAAI7jB;;EAEhB;AAEA,aAAW8oB,WAAWY,UAAU;AAC9BvqB,YAAQkB,WAAWlB,QAAQ2pB,QAAQ3pB;AACnC,QAAI2iB,OAAOliB,OAAOT,QAAQa,KAAM;AAChC,QAAIwO;AACJ,SAAKtB,IAAI/N,QAAQ,GAAG+N,KAAK4b,QAAQ1pB,KAAK8N,KAAK;AACzC,YAAMoU,KAAK1hB,OAAOsN,IAAIlN,KAAM;AAC5BwO,cAAQmc,UAAUT,eAAe7N,WAAWrC,cAAcwQ,cAAc;QACtEhlB,MAAM;QACN2lB,IAAIrJ;QACJ6E,IAAIrF;QACJ8J,cAAcle,IAAI,KAAKlN;QACvBqrB,aAAane,IAAIlN;QACjB6qB;MACF,CAAA,CAAA,CAAA;AACA,UAAIS,aAAa9c,OAAOsc,SAAY,GAAA;AAClCC,iBAAS5rB,OAAO+N,IAAI,GAAG4b,QAAQriB,MAAMqkB,SAAAA;;AAEvChJ,aAAOR;AACPwJ,kBAAYtc;IACd;AACA,QAAIrP,QAAQ+N,IAAI,GAAG;AACjB6d,eAAS5rB,OAAO+N,IAAI,GAAG4b,QAAQriB,MAAMqkB,SAAAA;;EAEzC;AAEA,SAAOvH;AACT;AAEA,SAASoH,UAAUpqB,SAAS;AAC1B,SAAO;IACLqN,iBAAiBrN,QAAQqN;IACzB2d,gBAAgBhrB,QAAQgrB;IACxBC,YAAYjrB,QAAQirB;IACpBC,kBAAkBlrB,QAAQkrB;IAC1BC,iBAAiBnrB,QAAQmrB;IACzB9W,aAAarU,QAAQqU;IACrB/G,aAAatN,QAAQsN;EACvB;AACF;AAEA,SAASyd,aAAa9c,OAAOsc,WAAW;AACtC,MAAI,CAACA,WAAW;AACd,WAAO;;AAET,QAAMrZ,QAAQ,CAAA;AACd,QAAMka,WAAW,SAAS5e,KAAKxH,OAAO;AACpC,QAAI,CAACD,oBAAoBC,KAAQ,GAAA;AAC/B,aAAOA;;AAET,QAAI,CAACkM,MAAMtH,SAAS5E,KAAQ,GAAA;AAC1BkM,YAAMH,KAAK/L,KAAAA;;AAEb,WAAOkM,MAAMiR,QAAQnd,KAAAA;EACvB;AACA,SAAO4C,KAAKC,UAAUoG,OAAOmd,QAAAA,MAAcxjB,KAAKC,UAAU0iB,WAAWa,QAAAA;AACvE;ACzWA,SAASC,eAAejc,OAAckc,WAAsBC,OAAwB;AAClF,SAAOnc,MAAMpP,QAAQkK,OAAOkF,MAAMmc,KAAM,IAAGD,UAAUC,KAAM;AAC7D;AAEA,SAASC,eAAepsB,MAAiBksB,WAA4B;AACnE,QAAM,EAACjqB,QAAQC,OAAAA,IAAUlC;AACzB,MAAIiC,UAAUC,QAAQ;AACpB,WAAO;MACLvC,MAAMssB,eAAehqB,QAAQiqB,WAAW,MAAA;MACxCtsB,OAAOqsB,eAAehqB,QAAQiqB,WAAW,OAAA;MACzCjkB,KAAKgkB,eAAe/pB,QAAQgqB,WAAW,KAAA;MACvChkB,QAAQ+jB,eAAe/pB,QAAQgqB,WAAW,QAAA;IAC5C;;AAEF,SAAOA;AACT;AAEO,SAASG,mBAAmB7iB,OAAcxJ,MAA+B;AAC9E,QAAM8K,OAAO9K,KAAKssB;AAClB,MAAIxhB,KAAKyhB,UAAU;AACjB,WAAO;;AAET,QAAMnX,OAAOgX,eAAepsB,MAAMwJ,MAAM0iB,SAAS;AAEjD,SAAO;IACLvsB,MAAMmL,KAAKnL,SAAS,QAAQ,IAAIyV,KAAKzV,QAAQmL,KAAKnL,SAAS,OAAO,IAAImL,KAAKnL;IAC3EC,OAAOkL,KAAKlL,UAAU,QAAQ4J,MAAMmC,QAAQyJ,KAAKxV,SAASkL,KAAKlL,UAAU,OAAO,IAAIkL,KAAKlL;IACzFqI,KAAK6C,KAAK7C,QAAQ,QAAQ,IAAImN,KAAKnN,OAAO6C,KAAK7C,QAAQ,OAAO,IAAI6C,KAAK7C;IACvEC,QAAQ4C,KAAK5C,WAAW,QAAQsB,MAAM2J,SAASiC,KAAKlN,UAAU4C,KAAK5C,WAAW,OAAO,IAAI4C,KAAK5C;EAChG;AACF;", "names": ["color", "noop", "uid", "id", "isNullOrUndef", "value", "undefined", "isArray", "Array", "type", "Object", "prototype", "toString", "call", "slice", "isObject", "isNumberFinite", "Number", "isFinite", "finiteOrDefault", "defaultValue", "valueOrDefault", "toPercentage", "dimension", "endsWith", "parseFloat", "toDimension", "callback", "fn", "args", "thisArg", "apply", "each", "loopable", "reverse", "i", "len", "keys", "length", "_elementsEqual", "a0", "a1", "ilen", "v0", "v1", "datasetIndex", "index", "clone", "source", "map", "target", "create", "klen", "k", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "_merger", "options", "tval", "sval", "merge", "sources", "merger", "current", "mergeIf", "_mergerIf", "hasOwnProperty", "keyResolvers", "v", "x", "o", "y", "_splitKey", "key", "parts", "split", "keys", "tmp", "part", "endsWith", "slice", "push", "_getKeyResolver", "obj", "k", "resolveObjectKey", "resolver", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "defined", "value", "isFunction", "setsEqual", "a", "b", "size", "item", "has", "_isClickEvent", "e", "type", "PI", "Math", "TAU", "PITAU", "INFINITY", "Number", "POSITIVE_INFINITY", "RAD_PER_DEG", "HALF_PI", "QUARTER_PI", "TWO_THIRDS_PI", "log10", "sign", "almostEquals", "epsilon", "abs", "niceNum", "range", "roundedRange", "round", "niceRange", "pow", "floor", "fraction", "niceFraction", "_factorize", "result", "sqrt", "i", "sort", "pop", "isNonPrimitive", "n", "Symbol", "toPrimitive", "isNumber", "isNaN", "parseFloat", "isFinite", "almostWhole", "rounded", "_setMinAndMaxByKey", "array", "target", "property", "ilen", "length", "min", "max", "toRadians", "degrees", "toDegrees", "radians", "_decimalPlaces", "isFiniteNumber", "p", "getAngleFromPoint", "centrePoint", "anglePoint", "distanceFromXCenter", "distanceFromYCenter", "radialDistanceFromCenter", "angle", "atan2", "distance", "distanceBetweenPoints", "pt1", "pt2", "_angleDiff", "_normalizeAngle", "_angleBetween", "start", "end", "sameAngleIsFullCircle", "s", "angleToStart", "angleToEnd", "startToAngle", "endToAngle", "_limitValue", "_int16Range", "_isBetween", "_lookup", "table", "cmp", "index", "hi", "lo", "mid", "_lookup<PERSON><PERSON><PERSON><PERSON>", "last", "ti", "_rlookupByKey", "_filterBetween", "values", "arrayEvents", "listenArrayEvents", "listener", "_chartjs", "listeners", "Object", "defineProperty", "configurable", "enumerable", "for<PERSON>ach", "method", "base", "args", "res", "apply", "object", "unlistenArrayEvents", "stub", "indexOf", "splice", "_arrayUnique", "items", "set", "Set", "Array", "from", "requestAnimFrame", "window", "callback", "requestAnimationFrame", "throttled", "fn", "thisArg", "argsToUse", "ticking", "args", "call", "apply", "debounce", "delay", "timeout", "clearTimeout", "setTimeout", "_toLeftRightCenter", "align", "_alignStartEnd", "start", "end", "_textX", "left", "right", "rtl", "check", "_getStartAndCountOfVisiblePoints", "meta", "points", "animationsDisabled", "pointCount", "length", "count", "_sorted", "iScale", "vScale", "_parsed", "spanGaps", "dataset", "options", "axis", "min", "max", "minDefined", "maxDefined", "getUserBounds", "Math", "_lookup<PERSON><PERSON><PERSON><PERSON>", "lo", "getPixelForValue", "distanceToDefinedLo", "slice", "reverse", "findIndex", "point", "isNullOrUndef", "_limitValue", "hi", "distanceToDefinedHi", "_scaleRangesChanged", "xScale", "yScale", "_scaleRanges", "newRang<PERSON>", "xmin", "xmax", "ymin", "ymax", "changed", "Object", "assign", "atEdge", "t", "elasticIn", "s", "p", "pow", "sin", "TAU", "elasticOut", "effects", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInSine", "cos", "HALF_PI", "easeOutSine", "easeInOutSine", "PI", "easeInExpo", "easeOutExpo", "easeInOutExpo", "easeInCirc", "sqrt", "easeOutCirc", "easeInOutCirc", "easeInElastic", "easeOutElastic", "easeInOutElastic", "easeInBack", "easeOutBack", "easeInOutBack", "easeInBounce", "easeOutBounce", "m", "d", "easeInOutBounce", "isPatternOrGradient", "value", "type", "toString", "color", "Color", "getHoverColor", "saturate", "darken", "hexString", "numbers", "colors", "applyAnimationsDefaults", "defaults", "set", "undefined", "duration", "easing", "from", "loop", "to", "describe", "_fallback", "_indexable", "_scriptable", "name", "properties", "active", "animation", "resize", "show", "animations", "visible", "hide", "v", "applyLayoutsDefaults", "autoPadding", "padding", "top", "bottom", "intlCache", "Map", "getNumberFormat", "locale", "cache<PERSON>ey", "JSON", "stringify", "formatter", "get", "Intl", "NumberFormat", "formatNumber", "num", "format", "formatters", "values", "isArray", "numeric", "tickValue", "index", "ticks", "chart", "notation", "delta", "maxTick", "abs", "calculateDelta", "log<PERSON><PERSON><PERSON>", "log10", "numDecimal", "isNaN", "floor", "minimumFractionDigits", "maximumFractionDigits", "logarithmic", "remain", "significand", "includes", "applyScaleDefaults", "display", "offset", "beginAtZero", "bounds", "clip", "grace", "grid", "lineWidth", "drawOnChartArea", "drawTicks", "tick<PERSON><PERSON>th", "tickWidth", "_ctx", "tickColor", "border", "dash", "dashOffset", "width", "title", "text", "minRotation", "maxRotation", "mirror", "textStrokeWidth", "textStrokeColor", "autoSkip", "autoSkipPadding", "labelOffset", "Ticks", "minor", "major", "crossAlign", "showLabelBackdrop", "backdropColor", "backdropPadding", "route", "startsWith", "overrides", "create", "descriptors", "getScope", "node", "key", "keys", "split", "i", "n", "k", "root", "scope", "merge", "De<PERSON>ults", "constructor", "_descriptors", "_appliers", "backgroundColor", "borderColor", "datasets", "devicePixelRatio", "context", "platform", "getDevicePixelRatio", "elements", "events", "font", "family", "size", "style", "lineHeight", "weight", "hover", "hoverBackgroundColor", "ctx", "hoverBorderColor", "hoverColor", "indexAxis", "interaction", "mode", "intersect", "includeInvisible", "maintainAspectRatio", "onHover", "onClick", "parsing", "plugins", "responsive", "scale", "scales", "showLine", "drawActiveElementsOnTop", "override", "targetScope", "targetName", "scopeObject", "targetScopeObject", "privateName", "defineProperties", "writable", "enumerable", "local", "target", "isObject", "valueOrDefault", "appliers", "for<PERSON>ach", "toFontString", "_measureText", "data", "gc", "longest", "string", "textWidth", "measureText", "push", "_longestText", "arrayOfThings", "cache", "garbageCollect", "save", "ilen", "j", "jlen", "thing", "nestedThing", "restore", "gcLen", "splice", "_alignPixel", "pixel", "currentDevicePixelRatio", "halfWidth", "round", "clearCanvas", "canvas", "getContext", "resetTransform", "clearRect", "height", "drawPoint", "x", "y", "drawPointLegend", "w", "xOffset", "yOffset", "cornerRadius", "xOffsetW", "yOffsetW", "pointStyle", "rotation", "radius", "rad", "RAD_PER_DEG", "translate", "rotate", "drawImage", "beginPath", "ellipse", "arc", "closePath", "moveTo", "TWO_THIRDS_PI", "lineTo", "QUARTER_PI", "SQRT1_2", "rect", "fill", "borderWidth", "stroke", "_isPointInArea", "area", "margin", "clipArea", "unclipArea", "_steppedLineTo", "previous", "flip", "midpoint", "_bezierCurveTo", "bezierCurveTo", "cp1x", "cp2x", "cp1y", "cp2y", "setRenderOpts", "opts", "translation", "fillStyle", "textAlign", "textBaseline", "decorateText", "line", "strikethrough", "underline", "metrics", "actualBoundingBoxLeft", "actualBoundingBoxRight", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "yDecoration", "strokeStyle", "decorationWidth", "drawBackdrop", "oldColor", "fillRect", "renderText", "lines", "strokeWidth", "strokeColor", "backdrop", "strokeText", "max<PERSON><PERSON><PERSON>", "fillText", "Number", "addRoundedRectPath", "h", "topLeft", "bottomLeft", "bottomRight", "topRight", "LINE_HEIGHT", "FONT_STYLE", "toLineHeight", "matches", "match", "numberOrZero", "_readValueToProps", "props", "ret", "objProps", "read", "prop", "toTRBL", "toTRBLCorners", "toPadding", "obj", "toFont", "fallback", "parseInt", "console", "warn", "resolve", "inputs", "info", "cacheable", "_addGrace", "minmax", "change", "toDimension", "keepZero", "add", "createContext", "parentContext", "_createResolver", "scopes", "prefixes", "rootScopes", "get<PERSON><PERSON><PERSON>", "finalRootScopes", "_resolve", "Symbol", "toStringTag", "_cacheable", "_scopes", "_rootScopes", "_getTarget", "Proxy", "deleteProperty", "_keys", "_cached", "_resolveWithPrefixes", "getOwnPropertyDescriptor", "Reflect", "getPrototypeOf", "has", "getKeysFromAllScopes", "ownKeys", "storage", "_storage", "_attachContext", "proxy", "subProxy", "descriptor<PERSON><PERSON><PERSON><PERSON>", "_proxy", "_context", "_subProxy", "_stack", "Set", "setContext", "receiver", "_resolveWithContext", "allKeys", "configurable", "scriptable", "indexable", "_allKeys", "isScriptable", "isFunction", "isIndexable", "read<PERSON><PERSON>", "prefix", "_capitalize", "needsSubResolver", "prototype", "hasOwnProperty", "_resolveScriptable", "_resolveArray", "getValue", "Error", "Array", "join", "delete", "createSubResolver", "arr", "filter", "item", "resolver", "<PERSON><PERSON><PERSON><PERSON>", "parent", "resolveObjectKey", "addScopes", "parentScopes", "parentFallback", "allScopes", "addScopesFromKey", "subGetTarget", "resolveKeysFromAllScopes", "_parseObjectDataRadialScale", "_parsing", "parsed", "r", "parse", "EPSILON", "getPoint", "skip", "getValueAxis", "splineCurve", "firstPoint", "middlePoint", "afterPoint", "current", "next", "d01", "distanceBetweenPoints", "d12", "s01", "s12", "fa", "fb", "monotoneAdjust", "deltaK", "mK", "pointsLen", "alphaK", "betaK", "tauK", "squaredMagnitude", "pointCurrent", "pointAfter", "almostEquals", "monotoneCompute", "valueAxis", "pointBefore", "iPixel", "vPixel", "splineCurveMonotone", "slopeDel<PERSON>", "sign", "capControlPoint", "pt", "capBezierPoints", "inArea", "inAreaPrev", "inAreaNext", "_updateBezierControlPoints", "controlPoints", "cubicInterpolationMode", "prev", "tension", "_isDomSupported", "document", "_getParentNode", "domNode", "parentNode", "host", "parseMaxStyle", "styleValue", "parentProperty", "valueInPixels", "indexOf", "getComputedStyle", "element", "ownerDocument", "defaultView", "getStyle", "el", "property", "getPropertyValue", "positions", "getPositionedStyle", "styles", "suffix", "result", "pos", "parseFloat", "useOffsetPos", "shadowRoot", "getCanvasPosition", "e", "touches", "source", "offsetX", "offsetY", "box", "getBoundingClientRect", "clientX", "clientY", "getRelativePosition", "event", "borderBox", "boxSizing", "paddings", "borders", "getContainerSize", "maxHeight", "container", "clientWidth", "clientHeight", "containerStyle", "containerBorder", "containerPadding", "INFINITY", "round1", "getMaximumSize", "bb<PERSON><PERSON><PERSON>", "bbHeight", "aspectRatio", "margins", "containerSize", "maintainHeight", "retinaScale", "forceRatio", "forceStyle", "pixelRatio", "deviceHeight", "deviceWidth", "setTransform", "supportsEventListenerOptions", "passiveSupported", "passive", "addEventListener", "removeEventListener", "readUsedSize", "_pointInLine", "p1", "p2", "_steppedInterpolation", "_bezierInterpolation", "cp1", "cp2", "a", "b", "c", "getRightToLeftAdapter", "rectX", "<PERSON><PERSON><PERSON><PERSON>", "xPlus", "leftForLtr", "itemWidth", "getLeftToRightAdapter", "_itemWidth", "getRtlAdapter", "overrideTextDirection", "direction", "original", "getPropertyPriority", "setProperty", "prevTextDirection", "restoreTextDirection", "propertyFn", "between", "_angleBetween", "compare", "_angleDiff", "normalize", "_normalizeAngle", "_isBetween", "normalizeSegment", "getSegment", "segment", "startBound", "endBound", "_boundSegment", "inside", "subStart", "prevValue", "startIsBefore", "endIsBefore", "shouldStart", "shouldStop", "_boundSegments", "segments", "sub", "findStartAndEnd", "solidSegments", "last", "cur", "stop", "_computeSegments", "segmentOptions", "_loop", "splitByStyles", "completeLoop", "_fullLoop", "doSplitByStyles", "chartContext", "_chart", "baseStyle", "readStyle", "_datasetIndex", "datasetIndex", "prevStyle", "addStyle", "l", "st", "dir", "p0", "p0DataIndex", "p1DataIndex", "styleChanged", "borderCapStyle", "borderDash", "borderDashOffset", "borderJoinStyle", "replacer", "getSizeForArea", "chartArea", "field", "getDatasetArea", "getDatasetClipArea", "_clip", "disabled"]}