import { <PERSON>u, Button, Group, Text } from '@mantine/core'
import { PlusIcon, CalendarIcon, BellIcon, CalendarDaysIcon } from '@heroicons/react/24/outline'

interface NewItemMenuProps {
  onItemClick: (type: string) => void
  onClose: () => void
}

export function NewItemMenu({ onItemClick, onClose }: NewItemMenuProps) {
  const menuItems = [
    {
      id: 'meeting',
      name: 'Meeting',
      icon: CalendarIcon,
      description: 'Schedule a meeting with others'
    },
    {
      id: 'reminder',
      name: 'Reminder',
      icon: BellIcon,
      description: 'Set a personal reminder'
    },
    {
      id: 'event',
      name: 'Event',
      icon: CalendarDaysIcon,
      description: 'Create a calendar event'
    }
  ]
  
  const handleItemClick = (type: string) => {
    onItemClick(type)
    onClose()
  }

  return (
    <Menu shadow="md" width={280}>
      <Menu.Target>
        <Button
          leftSection={<CalendarIcon className="h-4 w-4" />}
          variant="subtle"
          color="white"
          size="sm"
          styles={{
            root: {
              backgroundColor: 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
              }
            }
          }}
        >
        </Button>
      </Menu.Target>

      <Menu.Dropdown>
        <Menu.Label>Create New</Menu.Label>
        {menuItems.map((item) => (
          <Menu.Item
            key={item.id}
            leftSection={<item.icon className="w-4 h-4" />}
            onClick={() => handleItemClick(item.id)}
          >
            <div>
              <Text size="sm" fw={500}>
                {item.name}
              </Text>
              <Text size="xs" c="dimmed">
                {item.description}
              </Text>
            </div>
          </Menu.Item>
        ))}
      </Menu.Dropdown>
    </Menu>
  )
}