import React, { useRef } from 'react';
import { Card, Text, Group, Badge } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { fetchServerMetrics } from '../../services/api';
import { WidgetProps } from '../../types/dashboard';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  TimeScale,
  Filler,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import zoomPlugin from 'chartjs-plugin-zoom';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  ChartTooltip,
  Legend,
  TimeScale,
  Filler,
  zoomPlugin
);

const LagTrendWidget: React.FC<WidgetProps> = ({ widget }) => {
  const chartRef = useRef<ChartJS<'line'>>(null);

  const { data: metrics, isLoading } = useQuery({
    queryKey: ['serverMetrics'],
    queryFn: fetchServerMetrics,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  if (isLoading) {
    return (
      <Card h="100%" p="md">
        <Text c="dimmed" ta="center">Loading...</Text>
      </Card>
    );
  }

  const rawData = metrics?.map(metric => ({
    time: new Date(metric.timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    }),
    lag: metric.averageLag,
    timestamp: metric.timestamp
  })) || [];

  const currentLag = rawData.length > 0 ? rawData[rawData.length - 1].lag : 0;
  const lagStatus = currentLag < 100 ? 'good' : currentLag < 300 ? 'warning' : 'critical';
  const lagColor = lagStatus === 'good' ? 'green' : lagStatus === 'warning' ? 'yellow' : 'red';

  // Prepare data for Chart.js
  const chartData = {
    labels: rawData.map(d => d.time),
    datasets: [
      {
        label: 'Average Lag',
        data: rawData.map(d => d.lag),
        borderColor: '#3b82f6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 6,
        pointBackgroundColor: '#3b82f6',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        callbacks: {
          label: function(context: any) {
            return `Average Lag: ${context.parsed.y}s`;
          }
        }
      },
      zoom: {
        zoom: {
          wheel: {
            enabled: true,
          },
          pinch: {
            enabled: true,
          },
          mode: 'x' as const,
        },
        pan: {
          enabled: true,
          mode: 'x' as const,
        },
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          color: '#6b7280',
          font: {
            size: 12,
          },
        },
      },
      y: {
        display: true,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          color: '#6b7280',
          font: {
            size: 12,
          },
        },
        title: {
          display: true,
          text: 'Lag (seconds)',
          color: '#6b7280',
        },
      },
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false,
    },
  };

  return (
    <Card h="100%" p="md">
      <Group justify="space-between" mb="md">
        <Text fw={600} size="sm">{widget.title}</Text>
        <Group gap="xs">
          <Badge color={lagColor} variant="light" size="xs">
            {currentLag}s avg
          </Badge>
          <Badge color="blue" variant="light" size="xs">Live</Badge>
        </Group>
      </Group>

      <div style={{ height: 'calc(100% - 40px)' }}>
        <Line ref={chartRef} data={chartData} options={options} />
      </div>
    </Card>
  );
};

export default LagTrendWidget;
