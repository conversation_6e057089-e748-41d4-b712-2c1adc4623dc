{"version": 3, "sources": ["../../hammerjs/hammer.js", "../../chartjs-plugin-zoom/dist/chartjs-plugin-zoom.esm.js"], "sourcesContent": ["/*! Hammer.JS - v2.0.7 - 2016-04-22\n * http://hammerjs.github.io/\n *\n * Copyright (c) 2016 <PERSON><PERSON>;\n * Licensed under the MIT license */\n(function(window, document, exportName, undefined) {\n  'use strict';\n\nvar VENDOR_PREFIXES = ['', 'webkit', 'Moz', 'MS', 'ms', 'o'];\nvar TEST_ELEMENT = document.createElement('div');\n\nvar TYPE_FUNCTION = 'function';\n\nvar round = Math.round;\nvar abs = Math.abs;\nvar now = Date.now;\n\n/**\n * set a timeout with a given scope\n * @param {Function} fn\n * @param {Number} timeout\n * @param {Object} context\n * @returns {number}\n */\nfunction setTimeoutContext(fn, timeout, context) {\n    return setTimeout(bindFn(fn, context), timeout);\n}\n\n/**\n * if the argument is an array, we want to execute the fn on each entry\n * if it aint an array we don't want to do a thing.\n * this is used by all the methods that accept a single and array argument.\n * @param {*|Array} arg\n * @param {String} fn\n * @param {Object} [context]\n * @returns {Boolean}\n */\nfunction invokeArrayArg(arg, fn, context) {\n    if (Array.isArray(arg)) {\n        each(arg, context[fn], context);\n        return true;\n    }\n    return false;\n}\n\n/**\n * walk objects and arrays\n * @param {Object} obj\n * @param {Function} iterator\n * @param {Object} context\n */\nfunction each(obj, iterator, context) {\n    var i;\n\n    if (!obj) {\n        return;\n    }\n\n    if (obj.forEach) {\n        obj.forEach(iterator, context);\n    } else if (obj.length !== undefined) {\n        i = 0;\n        while (i < obj.length) {\n            iterator.call(context, obj[i], i, obj);\n            i++;\n        }\n    } else {\n        for (i in obj) {\n            obj.hasOwnProperty(i) && iterator.call(context, obj[i], i, obj);\n        }\n    }\n}\n\n/**\n * wrap a method with a deprecation warning and stack trace\n * @param {Function} method\n * @param {String} name\n * @param {String} message\n * @returns {Function} A new function wrapping the supplied method.\n */\nfunction deprecate(method, name, message) {\n    var deprecationMessage = 'DEPRECATED METHOD: ' + name + '\\n' + message + ' AT \\n';\n    return function() {\n        var e = new Error('get-stack-trace');\n        var stack = e && e.stack ? e.stack.replace(/^[^\\(]+?[\\n$]/gm, '')\n            .replace(/^\\s+at\\s+/gm, '')\n            .replace(/^Object.<anonymous>\\s*\\(/gm, '{anonymous}()@') : 'Unknown Stack Trace';\n\n        var log = window.console && (window.console.warn || window.console.log);\n        if (log) {\n            log.call(window.console, deprecationMessage, stack);\n        }\n        return method.apply(this, arguments);\n    };\n}\n\n/**\n * extend object.\n * means that properties in dest will be overwritten by the ones in src.\n * @param {Object} target\n * @param {...Object} objects_to_assign\n * @returns {Object} target\n */\nvar assign;\nif (typeof Object.assign !== 'function') {\n    assign = function assign(target) {\n        if (target === undefined || target === null) {\n            throw new TypeError('Cannot convert undefined or null to object');\n        }\n\n        var output = Object(target);\n        for (var index = 1; index < arguments.length; index++) {\n            var source = arguments[index];\n            if (source !== undefined && source !== null) {\n                for (var nextKey in source) {\n                    if (source.hasOwnProperty(nextKey)) {\n                        output[nextKey] = source[nextKey];\n                    }\n                }\n            }\n        }\n        return output;\n    };\n} else {\n    assign = Object.assign;\n}\n\n/**\n * extend object.\n * means that properties in dest will be overwritten by the ones in src.\n * @param {Object} dest\n * @param {Object} src\n * @param {Boolean} [merge=false]\n * @returns {Object} dest\n */\nvar extend = deprecate(function extend(dest, src, merge) {\n    var keys = Object.keys(src);\n    var i = 0;\n    while (i < keys.length) {\n        if (!merge || (merge && dest[keys[i]] === undefined)) {\n            dest[keys[i]] = src[keys[i]];\n        }\n        i++;\n    }\n    return dest;\n}, 'extend', 'Use `assign`.');\n\n/**\n * merge the values from src in the dest.\n * means that properties that exist in dest will not be overwritten by src\n * @param {Object} dest\n * @param {Object} src\n * @returns {Object} dest\n */\nvar merge = deprecate(function merge(dest, src) {\n    return extend(dest, src, true);\n}, 'merge', 'Use `assign`.');\n\n/**\n * simple class inheritance\n * @param {Function} child\n * @param {Function} base\n * @param {Object} [properties]\n */\nfunction inherit(child, base, properties) {\n    var baseP = base.prototype,\n        childP;\n\n    childP = child.prototype = Object.create(baseP);\n    childP.constructor = child;\n    childP._super = baseP;\n\n    if (properties) {\n        assign(childP, properties);\n    }\n}\n\n/**\n * simple function bind\n * @param {Function} fn\n * @param {Object} context\n * @returns {Function}\n */\nfunction bindFn(fn, context) {\n    return function boundFn() {\n        return fn.apply(context, arguments);\n    };\n}\n\n/**\n * let a boolean value also be a function that must return a boolean\n * this first item in args will be used as the context\n * @param {Boolean|Function} val\n * @param {Array} [args]\n * @returns {Boolean}\n */\nfunction boolOrFn(val, args) {\n    if (typeof val == TYPE_FUNCTION) {\n        return val.apply(args ? args[0] || undefined : undefined, args);\n    }\n    return val;\n}\n\n/**\n * use the val2 when val1 is undefined\n * @param {*} val1\n * @param {*} val2\n * @returns {*}\n */\nfunction ifUndefined(val1, val2) {\n    return (val1 === undefined) ? val2 : val1;\n}\n\n/**\n * addEventListener with multiple events at once\n * @param {EventTarget} target\n * @param {String} types\n * @param {Function} handler\n */\nfunction addEventListeners(target, types, handler) {\n    each(splitStr(types), function(type) {\n        target.addEventListener(type, handler, false);\n    });\n}\n\n/**\n * removeEventListener with multiple events at once\n * @param {EventTarget} target\n * @param {String} types\n * @param {Function} handler\n */\nfunction removeEventListeners(target, types, handler) {\n    each(splitStr(types), function(type) {\n        target.removeEventListener(type, handler, false);\n    });\n}\n\n/**\n * find if a node is in the given parent\n * @method hasParent\n * @param {HTMLElement} node\n * @param {HTMLElement} parent\n * @return {Boolean} found\n */\nfunction hasParent(node, parent) {\n    while (node) {\n        if (node == parent) {\n            return true;\n        }\n        node = node.parentNode;\n    }\n    return false;\n}\n\n/**\n * small indexOf wrapper\n * @param {String} str\n * @param {String} find\n * @returns {Boolean} found\n */\nfunction inStr(str, find) {\n    return str.indexOf(find) > -1;\n}\n\n/**\n * split string on whitespace\n * @param {String} str\n * @returns {Array} words\n */\nfunction splitStr(str) {\n    return str.trim().split(/\\s+/g);\n}\n\n/**\n * find if a array contains the object using indexOf or a simple polyFill\n * @param {Array} src\n * @param {String} find\n * @param {String} [findByKey]\n * @return {Boolean|Number} false when not found, or the index\n */\nfunction inArray(src, find, findByKey) {\n    if (src.indexOf && !findByKey) {\n        return src.indexOf(find);\n    } else {\n        var i = 0;\n        while (i < src.length) {\n            if ((findByKey && src[i][findByKey] == find) || (!findByKey && src[i] === find)) {\n                return i;\n            }\n            i++;\n        }\n        return -1;\n    }\n}\n\n/**\n * convert array-like objects to real arrays\n * @param {Object} obj\n * @returns {Array}\n */\nfunction toArray(obj) {\n    return Array.prototype.slice.call(obj, 0);\n}\n\n/**\n * unique array with objects based on a key (like 'id') or just by the array's value\n * @param {Array} src [{id:1},{id:2},{id:1}]\n * @param {String} [key]\n * @param {Boolean} [sort=False]\n * @returns {Array} [{id:1},{id:2}]\n */\nfunction uniqueArray(src, key, sort) {\n    var results = [];\n    var values = [];\n    var i = 0;\n\n    while (i < src.length) {\n        var val = key ? src[i][key] : src[i];\n        if (inArray(values, val) < 0) {\n            results.push(src[i]);\n        }\n        values[i] = val;\n        i++;\n    }\n\n    if (sort) {\n        if (!key) {\n            results = results.sort();\n        } else {\n            results = results.sort(function sortUniqueArray(a, b) {\n                return a[key] > b[key];\n            });\n        }\n    }\n\n    return results;\n}\n\n/**\n * get the prefixed property\n * @param {Object} obj\n * @param {String} property\n * @returns {String|Undefined} prefixed\n */\nfunction prefixed(obj, property) {\n    var prefix, prop;\n    var camelProp = property[0].toUpperCase() + property.slice(1);\n\n    var i = 0;\n    while (i < VENDOR_PREFIXES.length) {\n        prefix = VENDOR_PREFIXES[i];\n        prop = (prefix) ? prefix + camelProp : property;\n\n        if (prop in obj) {\n            return prop;\n        }\n        i++;\n    }\n    return undefined;\n}\n\n/**\n * get a unique id\n * @returns {number} uniqueId\n */\nvar _uniqueId = 1;\nfunction uniqueId() {\n    return _uniqueId++;\n}\n\n/**\n * get the window object of an element\n * @param {HTMLElement} element\n * @returns {DocumentView|Window}\n */\nfunction getWindowForElement(element) {\n    var doc = element.ownerDocument || element;\n    return (doc.defaultView || doc.parentWindow || window);\n}\n\nvar MOBILE_REGEX = /mobile|tablet|ip(ad|hone|od)|android/i;\n\nvar SUPPORT_TOUCH = ('ontouchstart' in window);\nvar SUPPORT_POINTER_EVENTS = prefixed(window, 'PointerEvent') !== undefined;\nvar SUPPORT_ONLY_TOUCH = SUPPORT_TOUCH && MOBILE_REGEX.test(navigator.userAgent);\n\nvar INPUT_TYPE_TOUCH = 'touch';\nvar INPUT_TYPE_PEN = 'pen';\nvar INPUT_TYPE_MOUSE = 'mouse';\nvar INPUT_TYPE_KINECT = 'kinect';\n\nvar COMPUTE_INTERVAL = 25;\n\nvar INPUT_START = 1;\nvar INPUT_MOVE = 2;\nvar INPUT_END = 4;\nvar INPUT_CANCEL = 8;\n\nvar DIRECTION_NONE = 1;\nvar DIRECTION_LEFT = 2;\nvar DIRECTION_RIGHT = 4;\nvar DIRECTION_UP = 8;\nvar DIRECTION_DOWN = 16;\n\nvar DIRECTION_HORIZONTAL = DIRECTION_LEFT | DIRECTION_RIGHT;\nvar DIRECTION_VERTICAL = DIRECTION_UP | DIRECTION_DOWN;\nvar DIRECTION_ALL = DIRECTION_HORIZONTAL | DIRECTION_VERTICAL;\n\nvar PROPS_XY = ['x', 'y'];\nvar PROPS_CLIENT_XY = ['clientX', 'clientY'];\n\n/**\n * create new input type manager\n * @param {Manager} manager\n * @param {Function} callback\n * @returns {Input}\n * @constructor\n */\nfunction Input(manager, callback) {\n    var self = this;\n    this.manager = manager;\n    this.callback = callback;\n    this.element = manager.element;\n    this.target = manager.options.inputTarget;\n\n    // smaller wrapper around the handler, for the scope and the enabled state of the manager,\n    // so when disabled the input events are completely bypassed.\n    this.domHandler = function(ev) {\n        if (boolOrFn(manager.options.enable, [manager])) {\n            self.handler(ev);\n        }\n    };\n\n    this.init();\n\n}\n\nInput.prototype = {\n    /**\n     * should handle the inputEvent data and trigger the callback\n     * @virtual\n     */\n    handler: function() { },\n\n    /**\n     * bind the events\n     */\n    init: function() {\n        this.evEl && addEventListeners(this.element, this.evEl, this.domHandler);\n        this.evTarget && addEventListeners(this.target, this.evTarget, this.domHandler);\n        this.evWin && addEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n    },\n\n    /**\n     * unbind the events\n     */\n    destroy: function() {\n        this.evEl && removeEventListeners(this.element, this.evEl, this.domHandler);\n        this.evTarget && removeEventListeners(this.target, this.evTarget, this.domHandler);\n        this.evWin && removeEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n    }\n};\n\n/**\n * create new input type manager\n * called by the Manager constructor\n * @param {Hammer} manager\n * @returns {Input}\n */\nfunction createInputInstance(manager) {\n    var Type;\n    var inputClass = manager.options.inputClass;\n\n    if (inputClass) {\n        Type = inputClass;\n    } else if (SUPPORT_POINTER_EVENTS) {\n        Type = PointerEventInput;\n    } else if (SUPPORT_ONLY_TOUCH) {\n        Type = TouchInput;\n    } else if (!SUPPORT_TOUCH) {\n        Type = MouseInput;\n    } else {\n        Type = TouchMouseInput;\n    }\n    return new (Type)(manager, inputHandler);\n}\n\n/**\n * handle input events\n * @param {Manager} manager\n * @param {String} eventType\n * @param {Object} input\n */\nfunction inputHandler(manager, eventType, input) {\n    var pointersLen = input.pointers.length;\n    var changedPointersLen = input.changedPointers.length;\n    var isFirst = (eventType & INPUT_START && (pointersLen - changedPointersLen === 0));\n    var isFinal = (eventType & (INPUT_END | INPUT_CANCEL) && (pointersLen - changedPointersLen === 0));\n\n    input.isFirst = !!isFirst;\n    input.isFinal = !!isFinal;\n\n    if (isFirst) {\n        manager.session = {};\n    }\n\n    // source event is the normalized value of the domEvents\n    // like 'touchstart, mouseup, pointerdown'\n    input.eventType = eventType;\n\n    // compute scale, rotation etc\n    computeInputData(manager, input);\n\n    // emit secret event\n    manager.emit('hammer.input', input);\n\n    manager.recognize(input);\n    manager.session.prevInput = input;\n}\n\n/**\n * extend the data with some usable properties like scale, rotate, velocity etc\n * @param {Object} manager\n * @param {Object} input\n */\nfunction computeInputData(manager, input) {\n    var session = manager.session;\n    var pointers = input.pointers;\n    var pointersLength = pointers.length;\n\n    // store the first input to calculate the distance and direction\n    if (!session.firstInput) {\n        session.firstInput = simpleCloneInputData(input);\n    }\n\n    // to compute scale and rotation we need to store the multiple touches\n    if (pointersLength > 1 && !session.firstMultiple) {\n        session.firstMultiple = simpleCloneInputData(input);\n    } else if (pointersLength === 1) {\n        session.firstMultiple = false;\n    }\n\n    var firstInput = session.firstInput;\n    var firstMultiple = session.firstMultiple;\n    var offsetCenter = firstMultiple ? firstMultiple.center : firstInput.center;\n\n    var center = input.center = getCenter(pointers);\n    input.timeStamp = now();\n    input.deltaTime = input.timeStamp - firstInput.timeStamp;\n\n    input.angle = getAngle(offsetCenter, center);\n    input.distance = getDistance(offsetCenter, center);\n\n    computeDeltaXY(session, input);\n    input.offsetDirection = getDirection(input.deltaX, input.deltaY);\n\n    var overallVelocity = getVelocity(input.deltaTime, input.deltaX, input.deltaY);\n    input.overallVelocityX = overallVelocity.x;\n    input.overallVelocityY = overallVelocity.y;\n    input.overallVelocity = (abs(overallVelocity.x) > abs(overallVelocity.y)) ? overallVelocity.x : overallVelocity.y;\n\n    input.scale = firstMultiple ? getScale(firstMultiple.pointers, pointers) : 1;\n    input.rotation = firstMultiple ? getRotation(firstMultiple.pointers, pointers) : 0;\n\n    input.maxPointers = !session.prevInput ? input.pointers.length : ((input.pointers.length >\n        session.prevInput.maxPointers) ? input.pointers.length : session.prevInput.maxPointers);\n\n    computeIntervalInputData(session, input);\n\n    // find the correct target\n    var target = manager.element;\n    if (hasParent(input.srcEvent.target, target)) {\n        target = input.srcEvent.target;\n    }\n    input.target = target;\n}\n\nfunction computeDeltaXY(session, input) {\n    var center = input.center;\n    var offset = session.offsetDelta || {};\n    var prevDelta = session.prevDelta || {};\n    var prevInput = session.prevInput || {};\n\n    if (input.eventType === INPUT_START || prevInput.eventType === INPUT_END) {\n        prevDelta = session.prevDelta = {\n            x: prevInput.deltaX || 0,\n            y: prevInput.deltaY || 0\n        };\n\n        offset = session.offsetDelta = {\n            x: center.x,\n            y: center.y\n        };\n    }\n\n    input.deltaX = prevDelta.x + (center.x - offset.x);\n    input.deltaY = prevDelta.y + (center.y - offset.y);\n}\n\n/**\n * velocity is calculated every x ms\n * @param {Object} session\n * @param {Object} input\n */\nfunction computeIntervalInputData(session, input) {\n    var last = session.lastInterval || input,\n        deltaTime = input.timeStamp - last.timeStamp,\n        velocity, velocityX, velocityY, direction;\n\n    if (input.eventType != INPUT_CANCEL && (deltaTime > COMPUTE_INTERVAL || last.velocity === undefined)) {\n        var deltaX = input.deltaX - last.deltaX;\n        var deltaY = input.deltaY - last.deltaY;\n\n        var v = getVelocity(deltaTime, deltaX, deltaY);\n        velocityX = v.x;\n        velocityY = v.y;\n        velocity = (abs(v.x) > abs(v.y)) ? v.x : v.y;\n        direction = getDirection(deltaX, deltaY);\n\n        session.lastInterval = input;\n    } else {\n        // use latest velocity info if it doesn't overtake a minimum period\n        velocity = last.velocity;\n        velocityX = last.velocityX;\n        velocityY = last.velocityY;\n        direction = last.direction;\n    }\n\n    input.velocity = velocity;\n    input.velocityX = velocityX;\n    input.velocityY = velocityY;\n    input.direction = direction;\n}\n\n/**\n * create a simple clone from the input used for storage of firstInput and firstMultiple\n * @param {Object} input\n * @returns {Object} clonedInputData\n */\nfunction simpleCloneInputData(input) {\n    // make a simple copy of the pointers because we will get a reference if we don't\n    // we only need clientXY for the calculations\n    var pointers = [];\n    var i = 0;\n    while (i < input.pointers.length) {\n        pointers[i] = {\n            clientX: round(input.pointers[i].clientX),\n            clientY: round(input.pointers[i].clientY)\n        };\n        i++;\n    }\n\n    return {\n        timeStamp: now(),\n        pointers: pointers,\n        center: getCenter(pointers),\n        deltaX: input.deltaX,\n        deltaY: input.deltaY\n    };\n}\n\n/**\n * get the center of all the pointers\n * @param {Array} pointers\n * @return {Object} center contains `x` and `y` properties\n */\nfunction getCenter(pointers) {\n    var pointersLength = pointers.length;\n\n    // no need to loop when only one touch\n    if (pointersLength === 1) {\n        return {\n            x: round(pointers[0].clientX),\n            y: round(pointers[0].clientY)\n        };\n    }\n\n    var x = 0, y = 0, i = 0;\n    while (i < pointersLength) {\n        x += pointers[i].clientX;\n        y += pointers[i].clientY;\n        i++;\n    }\n\n    return {\n        x: round(x / pointersLength),\n        y: round(y / pointersLength)\n    };\n}\n\n/**\n * calculate the velocity between two points. unit is in px per ms.\n * @param {Number} deltaTime\n * @param {Number} x\n * @param {Number} y\n * @return {Object} velocity `x` and `y`\n */\nfunction getVelocity(deltaTime, x, y) {\n    return {\n        x: x / deltaTime || 0,\n        y: y / deltaTime || 0\n    };\n}\n\n/**\n * get the direction between two points\n * @param {Number} x\n * @param {Number} y\n * @return {Number} direction\n */\nfunction getDirection(x, y) {\n    if (x === y) {\n        return DIRECTION_NONE;\n    }\n\n    if (abs(x) >= abs(y)) {\n        return x < 0 ? DIRECTION_LEFT : DIRECTION_RIGHT;\n    }\n    return y < 0 ? DIRECTION_UP : DIRECTION_DOWN;\n}\n\n/**\n * calculate the absolute distance between two points\n * @param {Object} p1 {x, y}\n * @param {Object} p2 {x, y}\n * @param {Array} [props] containing x and y keys\n * @return {Number} distance\n */\nfunction getDistance(p1, p2, props) {\n    if (!props) {\n        props = PROPS_XY;\n    }\n    var x = p2[props[0]] - p1[props[0]],\n        y = p2[props[1]] - p1[props[1]];\n\n    return Math.sqrt((x * x) + (y * y));\n}\n\n/**\n * calculate the angle between two coordinates\n * @param {Object} p1\n * @param {Object} p2\n * @param {Array} [props] containing x and y keys\n * @return {Number} angle\n */\nfunction getAngle(p1, p2, props) {\n    if (!props) {\n        props = PROPS_XY;\n    }\n    var x = p2[props[0]] - p1[props[0]],\n        y = p2[props[1]] - p1[props[1]];\n    return Math.atan2(y, x) * 180 / Math.PI;\n}\n\n/**\n * calculate the rotation degrees between two pointersets\n * @param {Array} start array of pointers\n * @param {Array} end array of pointers\n * @return {Number} rotation\n */\nfunction getRotation(start, end) {\n    return getAngle(end[1], end[0], PROPS_CLIENT_XY) + getAngle(start[1], start[0], PROPS_CLIENT_XY);\n}\n\n/**\n * calculate the scale factor between two pointersets\n * no scale is 1, and goes down to 0 when pinched together, and bigger when pinched out\n * @param {Array} start array of pointers\n * @param {Array} end array of pointers\n * @return {Number} scale\n */\nfunction getScale(start, end) {\n    return getDistance(end[0], end[1], PROPS_CLIENT_XY) / getDistance(start[0], start[1], PROPS_CLIENT_XY);\n}\n\nvar MOUSE_INPUT_MAP = {\n    mousedown: INPUT_START,\n    mousemove: INPUT_MOVE,\n    mouseup: INPUT_END\n};\n\nvar MOUSE_ELEMENT_EVENTS = 'mousedown';\nvar MOUSE_WINDOW_EVENTS = 'mousemove mouseup';\n\n/**\n * Mouse events input\n * @constructor\n * @extends Input\n */\nfunction MouseInput() {\n    this.evEl = MOUSE_ELEMENT_EVENTS;\n    this.evWin = MOUSE_WINDOW_EVENTS;\n\n    this.pressed = false; // mousedown state\n\n    Input.apply(this, arguments);\n}\n\ninherit(MouseInput, Input, {\n    /**\n     * handle mouse events\n     * @param {Object} ev\n     */\n    handler: function MEhandler(ev) {\n        var eventType = MOUSE_INPUT_MAP[ev.type];\n\n        // on start we want to have the left mouse button down\n        if (eventType & INPUT_START && ev.button === 0) {\n            this.pressed = true;\n        }\n\n        if (eventType & INPUT_MOVE && ev.which !== 1) {\n            eventType = INPUT_END;\n        }\n\n        // mouse must be down\n        if (!this.pressed) {\n            return;\n        }\n\n        if (eventType & INPUT_END) {\n            this.pressed = false;\n        }\n\n        this.callback(this.manager, eventType, {\n            pointers: [ev],\n            changedPointers: [ev],\n            pointerType: INPUT_TYPE_MOUSE,\n            srcEvent: ev\n        });\n    }\n});\n\nvar POINTER_INPUT_MAP = {\n    pointerdown: INPUT_START,\n    pointermove: INPUT_MOVE,\n    pointerup: INPUT_END,\n    pointercancel: INPUT_CANCEL,\n    pointerout: INPUT_CANCEL\n};\n\n// in IE10 the pointer types is defined as an enum\nvar IE10_POINTER_TYPE_ENUM = {\n    2: INPUT_TYPE_TOUCH,\n    3: INPUT_TYPE_PEN,\n    4: INPUT_TYPE_MOUSE,\n    5: INPUT_TYPE_KINECT // see https://twitter.com/jacobrossi/status/480596438489890816\n};\n\nvar POINTER_ELEMENT_EVENTS = 'pointerdown';\nvar POINTER_WINDOW_EVENTS = 'pointermove pointerup pointercancel';\n\n// IE10 has prefixed support, and case-sensitive\nif (window.MSPointerEvent && !window.PointerEvent) {\n    POINTER_ELEMENT_EVENTS = 'MSPointerDown';\n    POINTER_WINDOW_EVENTS = 'MSPointerMove MSPointerUp MSPointerCancel';\n}\n\n/**\n * Pointer events input\n * @constructor\n * @extends Input\n */\nfunction PointerEventInput() {\n    this.evEl = POINTER_ELEMENT_EVENTS;\n    this.evWin = POINTER_WINDOW_EVENTS;\n\n    Input.apply(this, arguments);\n\n    this.store = (this.manager.session.pointerEvents = []);\n}\n\ninherit(PointerEventInput, Input, {\n    /**\n     * handle mouse events\n     * @param {Object} ev\n     */\n    handler: function PEhandler(ev) {\n        var store = this.store;\n        var removePointer = false;\n\n        var eventTypeNormalized = ev.type.toLowerCase().replace('ms', '');\n        var eventType = POINTER_INPUT_MAP[eventTypeNormalized];\n        var pointerType = IE10_POINTER_TYPE_ENUM[ev.pointerType] || ev.pointerType;\n\n        var isTouch = (pointerType == INPUT_TYPE_TOUCH);\n\n        // get index of the event in the store\n        var storeIndex = inArray(store, ev.pointerId, 'pointerId');\n\n        // start and mouse must be down\n        if (eventType & INPUT_START && (ev.button === 0 || isTouch)) {\n            if (storeIndex < 0) {\n                store.push(ev);\n                storeIndex = store.length - 1;\n            }\n        } else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n            removePointer = true;\n        }\n\n        // it not found, so the pointer hasn't been down (so it's probably a hover)\n        if (storeIndex < 0) {\n            return;\n        }\n\n        // update the event in the store\n        store[storeIndex] = ev;\n\n        this.callback(this.manager, eventType, {\n            pointers: store,\n            changedPointers: [ev],\n            pointerType: pointerType,\n            srcEvent: ev\n        });\n\n        if (removePointer) {\n            // remove from the store\n            store.splice(storeIndex, 1);\n        }\n    }\n});\n\nvar SINGLE_TOUCH_INPUT_MAP = {\n    touchstart: INPUT_START,\n    touchmove: INPUT_MOVE,\n    touchend: INPUT_END,\n    touchcancel: INPUT_CANCEL\n};\n\nvar SINGLE_TOUCH_TARGET_EVENTS = 'touchstart';\nvar SINGLE_TOUCH_WINDOW_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n/**\n * Touch events input\n * @constructor\n * @extends Input\n */\nfunction SingleTouchInput() {\n    this.evTarget = SINGLE_TOUCH_TARGET_EVENTS;\n    this.evWin = SINGLE_TOUCH_WINDOW_EVENTS;\n    this.started = false;\n\n    Input.apply(this, arguments);\n}\n\ninherit(SingleTouchInput, Input, {\n    handler: function TEhandler(ev) {\n        var type = SINGLE_TOUCH_INPUT_MAP[ev.type];\n\n        // should we handle the touch events?\n        if (type === INPUT_START) {\n            this.started = true;\n        }\n\n        if (!this.started) {\n            return;\n        }\n\n        var touches = normalizeSingleTouches.call(this, ev, type);\n\n        // when done, reset the started state\n        if (type & (INPUT_END | INPUT_CANCEL) && touches[0].length - touches[1].length === 0) {\n            this.started = false;\n        }\n\n        this.callback(this.manager, type, {\n            pointers: touches[0],\n            changedPointers: touches[1],\n            pointerType: INPUT_TYPE_TOUCH,\n            srcEvent: ev\n        });\n    }\n});\n\n/**\n * @this {TouchInput}\n * @param {Object} ev\n * @param {Number} type flag\n * @returns {undefined|Array} [all, changed]\n */\nfunction normalizeSingleTouches(ev, type) {\n    var all = toArray(ev.touches);\n    var changed = toArray(ev.changedTouches);\n\n    if (type & (INPUT_END | INPUT_CANCEL)) {\n        all = uniqueArray(all.concat(changed), 'identifier', true);\n    }\n\n    return [all, changed];\n}\n\nvar TOUCH_INPUT_MAP = {\n    touchstart: INPUT_START,\n    touchmove: INPUT_MOVE,\n    touchend: INPUT_END,\n    touchcancel: INPUT_CANCEL\n};\n\nvar TOUCH_TARGET_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n/**\n * Multi-user touch events input\n * @constructor\n * @extends Input\n */\nfunction TouchInput() {\n    this.evTarget = TOUCH_TARGET_EVENTS;\n    this.targetIds = {};\n\n    Input.apply(this, arguments);\n}\n\ninherit(TouchInput, Input, {\n    handler: function MTEhandler(ev) {\n        var type = TOUCH_INPUT_MAP[ev.type];\n        var touches = getTouches.call(this, ev, type);\n        if (!touches) {\n            return;\n        }\n\n        this.callback(this.manager, type, {\n            pointers: touches[0],\n            changedPointers: touches[1],\n            pointerType: INPUT_TYPE_TOUCH,\n            srcEvent: ev\n        });\n    }\n});\n\n/**\n * @this {TouchInput}\n * @param {Object} ev\n * @param {Number} type flag\n * @returns {undefined|Array} [all, changed]\n */\nfunction getTouches(ev, type) {\n    var allTouches = toArray(ev.touches);\n    var targetIds = this.targetIds;\n\n    // when there is only one touch, the process can be simplified\n    if (type & (INPUT_START | INPUT_MOVE) && allTouches.length === 1) {\n        targetIds[allTouches[0].identifier] = true;\n        return [allTouches, allTouches];\n    }\n\n    var i,\n        targetTouches,\n        changedTouches = toArray(ev.changedTouches),\n        changedTargetTouches = [],\n        target = this.target;\n\n    // get target touches from touches\n    targetTouches = allTouches.filter(function(touch) {\n        return hasParent(touch.target, target);\n    });\n\n    // collect touches\n    if (type === INPUT_START) {\n        i = 0;\n        while (i < targetTouches.length) {\n            targetIds[targetTouches[i].identifier] = true;\n            i++;\n        }\n    }\n\n    // filter changed touches to only contain touches that exist in the collected target ids\n    i = 0;\n    while (i < changedTouches.length) {\n        if (targetIds[changedTouches[i].identifier]) {\n            changedTargetTouches.push(changedTouches[i]);\n        }\n\n        // cleanup removed touches\n        if (type & (INPUT_END | INPUT_CANCEL)) {\n            delete targetIds[changedTouches[i].identifier];\n        }\n        i++;\n    }\n\n    if (!changedTargetTouches.length) {\n        return;\n    }\n\n    return [\n        // merge targetTouches with changedTargetTouches so it contains ALL touches, including 'end' and 'cancel'\n        uniqueArray(targetTouches.concat(changedTargetTouches), 'identifier', true),\n        changedTargetTouches\n    ];\n}\n\n/**\n * Combined touch and mouse input\n *\n * Touch has a higher priority then mouse, and while touching no mouse events are allowed.\n * This because touch devices also emit mouse events while doing a touch.\n *\n * @constructor\n * @extends Input\n */\n\nvar DEDUP_TIMEOUT = 2500;\nvar DEDUP_DISTANCE = 25;\n\nfunction TouchMouseInput() {\n    Input.apply(this, arguments);\n\n    var handler = bindFn(this.handler, this);\n    this.touch = new TouchInput(this.manager, handler);\n    this.mouse = new MouseInput(this.manager, handler);\n\n    this.primaryTouch = null;\n    this.lastTouches = [];\n}\n\ninherit(TouchMouseInput, Input, {\n    /**\n     * handle mouse and touch events\n     * @param {Hammer} manager\n     * @param {String} inputEvent\n     * @param {Object} inputData\n     */\n    handler: function TMEhandler(manager, inputEvent, inputData) {\n        var isTouch = (inputData.pointerType == INPUT_TYPE_TOUCH),\n            isMouse = (inputData.pointerType == INPUT_TYPE_MOUSE);\n\n        if (isMouse && inputData.sourceCapabilities && inputData.sourceCapabilities.firesTouchEvents) {\n            return;\n        }\n\n        // when we're in a touch event, record touches to  de-dupe synthetic mouse event\n        if (isTouch) {\n            recordTouches.call(this, inputEvent, inputData);\n        } else if (isMouse && isSyntheticEvent.call(this, inputData)) {\n            return;\n        }\n\n        this.callback(manager, inputEvent, inputData);\n    },\n\n    /**\n     * remove the event listeners\n     */\n    destroy: function destroy() {\n        this.touch.destroy();\n        this.mouse.destroy();\n    }\n});\n\nfunction recordTouches(eventType, eventData) {\n    if (eventType & INPUT_START) {\n        this.primaryTouch = eventData.changedPointers[0].identifier;\n        setLastTouch.call(this, eventData);\n    } else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n        setLastTouch.call(this, eventData);\n    }\n}\n\nfunction setLastTouch(eventData) {\n    var touch = eventData.changedPointers[0];\n\n    if (touch.identifier === this.primaryTouch) {\n        var lastTouch = {x: touch.clientX, y: touch.clientY};\n        this.lastTouches.push(lastTouch);\n        var lts = this.lastTouches;\n        var removeLastTouch = function() {\n            var i = lts.indexOf(lastTouch);\n            if (i > -1) {\n                lts.splice(i, 1);\n            }\n        };\n        setTimeout(removeLastTouch, DEDUP_TIMEOUT);\n    }\n}\n\nfunction isSyntheticEvent(eventData) {\n    var x = eventData.srcEvent.clientX, y = eventData.srcEvent.clientY;\n    for (var i = 0; i < this.lastTouches.length; i++) {\n        var t = this.lastTouches[i];\n        var dx = Math.abs(x - t.x), dy = Math.abs(y - t.y);\n        if (dx <= DEDUP_DISTANCE && dy <= DEDUP_DISTANCE) {\n            return true;\n        }\n    }\n    return false;\n}\n\nvar PREFIXED_TOUCH_ACTION = prefixed(TEST_ELEMENT.style, 'touchAction');\nvar NATIVE_TOUCH_ACTION = PREFIXED_TOUCH_ACTION !== undefined;\n\n// magical touchAction value\nvar TOUCH_ACTION_COMPUTE = 'compute';\nvar TOUCH_ACTION_AUTO = 'auto';\nvar TOUCH_ACTION_MANIPULATION = 'manipulation'; // not implemented\nvar TOUCH_ACTION_NONE = 'none';\nvar TOUCH_ACTION_PAN_X = 'pan-x';\nvar TOUCH_ACTION_PAN_Y = 'pan-y';\nvar TOUCH_ACTION_MAP = getTouchActionProps();\n\n/**\n * Touch Action\n * sets the touchAction property or uses the js alternative\n * @param {Manager} manager\n * @param {String} value\n * @constructor\n */\nfunction TouchAction(manager, value) {\n    this.manager = manager;\n    this.set(value);\n}\n\nTouchAction.prototype = {\n    /**\n     * set the touchAction value on the element or enable the polyfill\n     * @param {String} value\n     */\n    set: function(value) {\n        // find out the touch-action by the event handlers\n        if (value == TOUCH_ACTION_COMPUTE) {\n            value = this.compute();\n        }\n\n        if (NATIVE_TOUCH_ACTION && this.manager.element.style && TOUCH_ACTION_MAP[value]) {\n            this.manager.element.style[PREFIXED_TOUCH_ACTION] = value;\n        }\n        this.actions = value.toLowerCase().trim();\n    },\n\n    /**\n     * just re-set the touchAction value\n     */\n    update: function() {\n        this.set(this.manager.options.touchAction);\n    },\n\n    /**\n     * compute the value for the touchAction property based on the recognizer's settings\n     * @returns {String} value\n     */\n    compute: function() {\n        var actions = [];\n        each(this.manager.recognizers, function(recognizer) {\n            if (boolOrFn(recognizer.options.enable, [recognizer])) {\n                actions = actions.concat(recognizer.getTouchAction());\n            }\n        });\n        return cleanTouchActions(actions.join(' '));\n    },\n\n    /**\n     * this method is called on each input cycle and provides the preventing of the browser behavior\n     * @param {Object} input\n     */\n    preventDefaults: function(input) {\n        var srcEvent = input.srcEvent;\n        var direction = input.offsetDirection;\n\n        // if the touch action did prevented once this session\n        if (this.manager.session.prevented) {\n            srcEvent.preventDefault();\n            return;\n        }\n\n        var actions = this.actions;\n        var hasNone = inStr(actions, TOUCH_ACTION_NONE) && !TOUCH_ACTION_MAP[TOUCH_ACTION_NONE];\n        var hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_Y];\n        var hasPanX = inStr(actions, TOUCH_ACTION_PAN_X) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_X];\n\n        if (hasNone) {\n            //do not prevent defaults if this is a tap gesture\n\n            var isTapPointer = input.pointers.length === 1;\n            var isTapMovement = input.distance < 2;\n            var isTapTouchTime = input.deltaTime < 250;\n\n            if (isTapPointer && isTapMovement && isTapTouchTime) {\n                return;\n            }\n        }\n\n        if (hasPanX && hasPanY) {\n            // `pan-x pan-y` means browser handles all scrolling/panning, do not prevent\n            return;\n        }\n\n        if (hasNone ||\n            (hasPanY && direction & DIRECTION_HORIZONTAL) ||\n            (hasPanX && direction & DIRECTION_VERTICAL)) {\n            return this.preventSrc(srcEvent);\n        }\n    },\n\n    /**\n     * call preventDefault to prevent the browser's default behavior (scrolling in most cases)\n     * @param {Object} srcEvent\n     */\n    preventSrc: function(srcEvent) {\n        this.manager.session.prevented = true;\n        srcEvent.preventDefault();\n    }\n};\n\n/**\n * when the touchActions are collected they are not a valid value, so we need to clean things up. *\n * @param {String} actions\n * @returns {*}\n */\nfunction cleanTouchActions(actions) {\n    // none\n    if (inStr(actions, TOUCH_ACTION_NONE)) {\n        return TOUCH_ACTION_NONE;\n    }\n\n    var hasPanX = inStr(actions, TOUCH_ACTION_PAN_X);\n    var hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y);\n\n    // if both pan-x and pan-y are set (different recognizers\n    // for different directions, e.g. horizontal pan but vertical swipe?)\n    // we need none (as otherwise with pan-x pan-y combined none of these\n    // recognizers will work, since the browser would handle all panning\n    if (hasPanX && hasPanY) {\n        return TOUCH_ACTION_NONE;\n    }\n\n    // pan-x OR pan-y\n    if (hasPanX || hasPanY) {\n        return hasPanX ? TOUCH_ACTION_PAN_X : TOUCH_ACTION_PAN_Y;\n    }\n\n    // manipulation\n    if (inStr(actions, TOUCH_ACTION_MANIPULATION)) {\n        return TOUCH_ACTION_MANIPULATION;\n    }\n\n    return TOUCH_ACTION_AUTO;\n}\n\nfunction getTouchActionProps() {\n    if (!NATIVE_TOUCH_ACTION) {\n        return false;\n    }\n    var touchMap = {};\n    var cssSupports = window.CSS && window.CSS.supports;\n    ['auto', 'manipulation', 'pan-y', 'pan-x', 'pan-x pan-y', 'none'].forEach(function(val) {\n\n        // If css.supports is not supported but there is native touch-action assume it supports\n        // all values. This is the case for IE 10 and 11.\n        touchMap[val] = cssSupports ? window.CSS.supports('touch-action', val) : true;\n    });\n    return touchMap;\n}\n\n/**\n * Recognizer flow explained; *\n * All recognizers have the initial state of POSSIBLE when a input session starts.\n * The definition of a input session is from the first input until the last input, with all it's movement in it. *\n * Example session for mouse-input: mousedown -> mousemove -> mouseup\n *\n * On each recognizing cycle (see Manager.recognize) the .recognize() method is executed\n * which determines with state it should be.\n *\n * If the recognizer has the state FAILED, CANCELLED or RECOGNIZED (equals ENDED), it is reset to\n * POSSIBLE to give it another change on the next cycle.\n *\n *               Possible\n *                  |\n *            +-----+---------------+\n *            |                     |\n *      +-----+-----+               |\n *      |           |               |\n *   Failed      Cancelled          |\n *                          +-------+------+\n *                          |              |\n *                      Recognized       Began\n *                                         |\n *                                      Changed\n *                                         |\n *                                  Ended/Recognized\n */\nvar STATE_POSSIBLE = 1;\nvar STATE_BEGAN = 2;\nvar STATE_CHANGED = 4;\nvar STATE_ENDED = 8;\nvar STATE_RECOGNIZED = STATE_ENDED;\nvar STATE_CANCELLED = 16;\nvar STATE_FAILED = 32;\n\n/**\n * Recognizer\n * Every recognizer needs to extend from this class.\n * @constructor\n * @param {Object} options\n */\nfunction Recognizer(options) {\n    this.options = assign({}, this.defaults, options || {});\n\n    this.id = uniqueId();\n\n    this.manager = null;\n\n    // default is enable true\n    this.options.enable = ifUndefined(this.options.enable, true);\n\n    this.state = STATE_POSSIBLE;\n\n    this.simultaneous = {};\n    this.requireFail = [];\n}\n\nRecognizer.prototype = {\n    /**\n     * @virtual\n     * @type {Object}\n     */\n    defaults: {},\n\n    /**\n     * set options\n     * @param {Object} options\n     * @return {Recognizer}\n     */\n    set: function(options) {\n        assign(this.options, options);\n\n        // also update the touchAction, in case something changed about the directions/enabled state\n        this.manager && this.manager.touchAction.update();\n        return this;\n    },\n\n    /**\n     * recognize simultaneous with an other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    recognizeWith: function(otherRecognizer) {\n        if (invokeArrayArg(otherRecognizer, 'recognizeWith', this)) {\n            return this;\n        }\n\n        var simultaneous = this.simultaneous;\n        otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n        if (!simultaneous[otherRecognizer.id]) {\n            simultaneous[otherRecognizer.id] = otherRecognizer;\n            otherRecognizer.recognizeWith(this);\n        }\n        return this;\n    },\n\n    /**\n     * drop the simultaneous link. it doesnt remove the link on the other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    dropRecognizeWith: function(otherRecognizer) {\n        if (invokeArrayArg(otherRecognizer, 'dropRecognizeWith', this)) {\n            return this;\n        }\n\n        otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n        delete this.simultaneous[otherRecognizer.id];\n        return this;\n    },\n\n    /**\n     * recognizer can only run when an other is failing\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    requireFailure: function(otherRecognizer) {\n        if (invokeArrayArg(otherRecognizer, 'requireFailure', this)) {\n            return this;\n        }\n\n        var requireFail = this.requireFail;\n        otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n        if (inArray(requireFail, otherRecognizer) === -1) {\n            requireFail.push(otherRecognizer);\n            otherRecognizer.requireFailure(this);\n        }\n        return this;\n    },\n\n    /**\n     * drop the requireFailure link. it does not remove the link on the other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    dropRequireFailure: function(otherRecognizer) {\n        if (invokeArrayArg(otherRecognizer, 'dropRequireFailure', this)) {\n            return this;\n        }\n\n        otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n        var index = inArray(this.requireFail, otherRecognizer);\n        if (index > -1) {\n            this.requireFail.splice(index, 1);\n        }\n        return this;\n    },\n\n    /**\n     * has require failures boolean\n     * @returns {boolean}\n     */\n    hasRequireFailures: function() {\n        return this.requireFail.length > 0;\n    },\n\n    /**\n     * if the recognizer can recognize simultaneous with an other recognizer\n     * @param {Recognizer} otherRecognizer\n     * @returns {Boolean}\n     */\n    canRecognizeWith: function(otherRecognizer) {\n        return !!this.simultaneous[otherRecognizer.id];\n    },\n\n    /**\n     * You should use `tryEmit` instead of `emit` directly to check\n     * that all the needed recognizers has failed before emitting.\n     * @param {Object} input\n     */\n    emit: function(input) {\n        var self = this;\n        var state = this.state;\n\n        function emit(event) {\n            self.manager.emit(event, input);\n        }\n\n        // 'panstart' and 'panmove'\n        if (state < STATE_ENDED) {\n            emit(self.options.event + stateStr(state));\n        }\n\n        emit(self.options.event); // simple 'eventName' events\n\n        if (input.additionalEvent) { // additional event(panleft, panright, pinchin, pinchout...)\n            emit(input.additionalEvent);\n        }\n\n        // panend and pancancel\n        if (state >= STATE_ENDED) {\n            emit(self.options.event + stateStr(state));\n        }\n    },\n\n    /**\n     * Check that all the require failure recognizers has failed,\n     * if true, it emits a gesture event,\n     * otherwise, setup the state to FAILED.\n     * @param {Object} input\n     */\n    tryEmit: function(input) {\n        if (this.canEmit()) {\n            return this.emit(input);\n        }\n        // it's failing anyway\n        this.state = STATE_FAILED;\n    },\n\n    /**\n     * can we emit?\n     * @returns {boolean}\n     */\n    canEmit: function() {\n        var i = 0;\n        while (i < this.requireFail.length) {\n            if (!(this.requireFail[i].state & (STATE_FAILED | STATE_POSSIBLE))) {\n                return false;\n            }\n            i++;\n        }\n        return true;\n    },\n\n    /**\n     * update the recognizer\n     * @param {Object} inputData\n     */\n    recognize: function(inputData) {\n        // make a new copy of the inputData\n        // so we can change the inputData without messing up the other recognizers\n        var inputDataClone = assign({}, inputData);\n\n        // is is enabled and allow recognizing?\n        if (!boolOrFn(this.options.enable, [this, inputDataClone])) {\n            this.reset();\n            this.state = STATE_FAILED;\n            return;\n        }\n\n        // reset when we've reached the end\n        if (this.state & (STATE_RECOGNIZED | STATE_CANCELLED | STATE_FAILED)) {\n            this.state = STATE_POSSIBLE;\n        }\n\n        this.state = this.process(inputDataClone);\n\n        // the recognizer has recognized a gesture\n        // so trigger an event\n        if (this.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED | STATE_CANCELLED)) {\n            this.tryEmit(inputDataClone);\n        }\n    },\n\n    /**\n     * return the state of the recognizer\n     * the actual recognizing happens in this method\n     * @virtual\n     * @param {Object} inputData\n     * @returns {Const} STATE\n     */\n    process: function(inputData) { }, // jshint ignore:line\n\n    /**\n     * return the preferred touch-action\n     * @virtual\n     * @returns {Array}\n     */\n    getTouchAction: function() { },\n\n    /**\n     * called when the gesture isn't allowed to recognize\n     * like when another is being recognized or it is disabled\n     * @virtual\n     */\n    reset: function() { }\n};\n\n/**\n * get a usable string, used as event postfix\n * @param {Const} state\n * @returns {String} state\n */\nfunction stateStr(state) {\n    if (state & STATE_CANCELLED) {\n        return 'cancel';\n    } else if (state & STATE_ENDED) {\n        return 'end';\n    } else if (state & STATE_CHANGED) {\n        return 'move';\n    } else if (state & STATE_BEGAN) {\n        return 'start';\n    }\n    return '';\n}\n\n/**\n * direction cons to string\n * @param {Const} direction\n * @returns {String}\n */\nfunction directionStr(direction) {\n    if (direction == DIRECTION_DOWN) {\n        return 'down';\n    } else if (direction == DIRECTION_UP) {\n        return 'up';\n    } else if (direction == DIRECTION_LEFT) {\n        return 'left';\n    } else if (direction == DIRECTION_RIGHT) {\n        return 'right';\n    }\n    return '';\n}\n\n/**\n * get a recognizer by name if it is bound to a manager\n * @param {Recognizer|String} otherRecognizer\n * @param {Recognizer} recognizer\n * @returns {Recognizer}\n */\nfunction getRecognizerByNameIfManager(otherRecognizer, recognizer) {\n    var manager = recognizer.manager;\n    if (manager) {\n        return manager.get(otherRecognizer);\n    }\n    return otherRecognizer;\n}\n\n/**\n * This recognizer is just used as a base for the simple attribute recognizers.\n * @constructor\n * @extends Recognizer\n */\nfunction AttrRecognizer() {\n    Recognizer.apply(this, arguments);\n}\n\ninherit(AttrRecognizer, Recognizer, {\n    /**\n     * @namespace\n     * @memberof AttrRecognizer\n     */\n    defaults: {\n        /**\n         * @type {Number}\n         * @default 1\n         */\n        pointers: 1\n    },\n\n    /**\n     * Used to check if it the recognizer receives valid input, like input.distance > 10.\n     * @memberof AttrRecognizer\n     * @param {Object} input\n     * @returns {Boolean} recognized\n     */\n    attrTest: function(input) {\n        var optionPointers = this.options.pointers;\n        return optionPointers === 0 || input.pointers.length === optionPointers;\n    },\n\n    /**\n     * Process the input and return the state for the recognizer\n     * @memberof AttrRecognizer\n     * @param {Object} input\n     * @returns {*} State\n     */\n    process: function(input) {\n        var state = this.state;\n        var eventType = input.eventType;\n\n        var isRecognized = state & (STATE_BEGAN | STATE_CHANGED);\n        var isValid = this.attrTest(input);\n\n        // on cancel input and we've recognized before, return STATE_CANCELLED\n        if (isRecognized && (eventType & INPUT_CANCEL || !isValid)) {\n            return state | STATE_CANCELLED;\n        } else if (isRecognized || isValid) {\n            if (eventType & INPUT_END) {\n                return state | STATE_ENDED;\n            } else if (!(state & STATE_BEGAN)) {\n                return STATE_BEGAN;\n            }\n            return state | STATE_CHANGED;\n        }\n        return STATE_FAILED;\n    }\n});\n\n/**\n * Pan\n * Recognized when the pointer is down and moved in the allowed direction.\n * @constructor\n * @extends AttrRecognizer\n */\nfunction PanRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n\n    this.pX = null;\n    this.pY = null;\n}\n\ninherit(PanRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof PanRecognizer\n     */\n    defaults: {\n        event: 'pan',\n        threshold: 10,\n        pointers: 1,\n        direction: DIRECTION_ALL\n    },\n\n    getTouchAction: function() {\n        var direction = this.options.direction;\n        var actions = [];\n        if (direction & DIRECTION_HORIZONTAL) {\n            actions.push(TOUCH_ACTION_PAN_Y);\n        }\n        if (direction & DIRECTION_VERTICAL) {\n            actions.push(TOUCH_ACTION_PAN_X);\n        }\n        return actions;\n    },\n\n    directionTest: function(input) {\n        var options = this.options;\n        var hasMoved = true;\n        var distance = input.distance;\n        var direction = input.direction;\n        var x = input.deltaX;\n        var y = input.deltaY;\n\n        // lock to axis?\n        if (!(direction & options.direction)) {\n            if (options.direction & DIRECTION_HORIZONTAL) {\n                direction = (x === 0) ? DIRECTION_NONE : (x < 0) ? DIRECTION_LEFT : DIRECTION_RIGHT;\n                hasMoved = x != this.pX;\n                distance = Math.abs(input.deltaX);\n            } else {\n                direction = (y === 0) ? DIRECTION_NONE : (y < 0) ? DIRECTION_UP : DIRECTION_DOWN;\n                hasMoved = y != this.pY;\n                distance = Math.abs(input.deltaY);\n            }\n        }\n        input.direction = direction;\n        return hasMoved && distance > options.threshold && direction & options.direction;\n    },\n\n    attrTest: function(input) {\n        return AttrRecognizer.prototype.attrTest.call(this, input) &&\n            (this.state & STATE_BEGAN || (!(this.state & STATE_BEGAN) && this.directionTest(input)));\n    },\n\n    emit: function(input) {\n\n        this.pX = input.deltaX;\n        this.pY = input.deltaY;\n\n        var direction = directionStr(input.direction);\n\n        if (direction) {\n            input.additionalEvent = this.options.event + direction;\n        }\n        this._super.emit.call(this, input);\n    }\n});\n\n/**\n * Pinch\n * Recognized when two or more pointers are moving toward (zoom-in) or away from each other (zoom-out).\n * @constructor\n * @extends AttrRecognizer\n */\nfunction PinchRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n}\n\ninherit(PinchRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof PinchRecognizer\n     */\n    defaults: {\n        event: 'pinch',\n        threshold: 0,\n        pointers: 2\n    },\n\n    getTouchAction: function() {\n        return [TOUCH_ACTION_NONE];\n    },\n\n    attrTest: function(input) {\n        return this._super.attrTest.call(this, input) &&\n            (Math.abs(input.scale - 1) > this.options.threshold || this.state & STATE_BEGAN);\n    },\n\n    emit: function(input) {\n        if (input.scale !== 1) {\n            var inOut = input.scale < 1 ? 'in' : 'out';\n            input.additionalEvent = this.options.event + inOut;\n        }\n        this._super.emit.call(this, input);\n    }\n});\n\n/**\n * Press\n * Recognized when the pointer is down for x ms without any movement.\n * @constructor\n * @extends Recognizer\n */\nfunction PressRecognizer() {\n    Recognizer.apply(this, arguments);\n\n    this._timer = null;\n    this._input = null;\n}\n\ninherit(PressRecognizer, Recognizer, {\n    /**\n     * @namespace\n     * @memberof PressRecognizer\n     */\n    defaults: {\n        event: 'press',\n        pointers: 1,\n        time: 251, // minimal time of the pointer to be pressed\n        threshold: 9 // a minimal movement is ok, but keep it low\n    },\n\n    getTouchAction: function() {\n        return [TOUCH_ACTION_AUTO];\n    },\n\n    process: function(input) {\n        var options = this.options;\n        var validPointers = input.pointers.length === options.pointers;\n        var validMovement = input.distance < options.threshold;\n        var validTime = input.deltaTime > options.time;\n\n        this._input = input;\n\n        // we only allow little movement\n        // and we've reached an end event, so a tap is possible\n        if (!validMovement || !validPointers || (input.eventType & (INPUT_END | INPUT_CANCEL) && !validTime)) {\n            this.reset();\n        } else if (input.eventType & INPUT_START) {\n            this.reset();\n            this._timer = setTimeoutContext(function() {\n                this.state = STATE_RECOGNIZED;\n                this.tryEmit();\n            }, options.time, this);\n        } else if (input.eventType & INPUT_END) {\n            return STATE_RECOGNIZED;\n        }\n        return STATE_FAILED;\n    },\n\n    reset: function() {\n        clearTimeout(this._timer);\n    },\n\n    emit: function(input) {\n        if (this.state !== STATE_RECOGNIZED) {\n            return;\n        }\n\n        if (input && (input.eventType & INPUT_END)) {\n            this.manager.emit(this.options.event + 'up', input);\n        } else {\n            this._input.timeStamp = now();\n            this.manager.emit(this.options.event, this._input);\n        }\n    }\n});\n\n/**\n * Rotate\n * Recognized when two or more pointer are moving in a circular motion.\n * @constructor\n * @extends AttrRecognizer\n */\nfunction RotateRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n}\n\ninherit(RotateRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof RotateRecognizer\n     */\n    defaults: {\n        event: 'rotate',\n        threshold: 0,\n        pointers: 2\n    },\n\n    getTouchAction: function() {\n        return [TOUCH_ACTION_NONE];\n    },\n\n    attrTest: function(input) {\n        return this._super.attrTest.call(this, input) &&\n            (Math.abs(input.rotation) > this.options.threshold || this.state & STATE_BEGAN);\n    }\n});\n\n/**\n * Swipe\n * Recognized when the pointer is moving fast (velocity), with enough distance in the allowed direction.\n * @constructor\n * @extends AttrRecognizer\n */\nfunction SwipeRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n}\n\ninherit(SwipeRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof SwipeRecognizer\n     */\n    defaults: {\n        event: 'swipe',\n        threshold: 10,\n        velocity: 0.3,\n        direction: DIRECTION_HORIZONTAL | DIRECTION_VERTICAL,\n        pointers: 1\n    },\n\n    getTouchAction: function() {\n        return PanRecognizer.prototype.getTouchAction.call(this);\n    },\n\n    attrTest: function(input) {\n        var direction = this.options.direction;\n        var velocity;\n\n        if (direction & (DIRECTION_HORIZONTAL | DIRECTION_VERTICAL)) {\n            velocity = input.overallVelocity;\n        } else if (direction & DIRECTION_HORIZONTAL) {\n            velocity = input.overallVelocityX;\n        } else if (direction & DIRECTION_VERTICAL) {\n            velocity = input.overallVelocityY;\n        }\n\n        return this._super.attrTest.call(this, input) &&\n            direction & input.offsetDirection &&\n            input.distance > this.options.threshold &&\n            input.maxPointers == this.options.pointers &&\n            abs(velocity) > this.options.velocity && input.eventType & INPUT_END;\n    },\n\n    emit: function(input) {\n        var direction = directionStr(input.offsetDirection);\n        if (direction) {\n            this.manager.emit(this.options.event + direction, input);\n        }\n\n        this.manager.emit(this.options.event, input);\n    }\n});\n\n/**\n * A tap is ecognized when the pointer is doing a small tap/click. Multiple taps are recognized if they occur\n * between the given interval and position. The delay option can be used to recognize multi-taps without firing\n * a single tap.\n *\n * The eventData from the emitted event contains the property `tapCount`, which contains the amount of\n * multi-taps being recognized.\n * @constructor\n * @extends Recognizer\n */\nfunction TapRecognizer() {\n    Recognizer.apply(this, arguments);\n\n    // previous time and center,\n    // used for tap counting\n    this.pTime = false;\n    this.pCenter = false;\n\n    this._timer = null;\n    this._input = null;\n    this.count = 0;\n}\n\ninherit(TapRecognizer, Recognizer, {\n    /**\n     * @namespace\n     * @memberof PinchRecognizer\n     */\n    defaults: {\n        event: 'tap',\n        pointers: 1,\n        taps: 1,\n        interval: 300, // max time between the multi-tap taps\n        time: 250, // max time of the pointer to be down (like finger on the screen)\n        threshold: 9, // a minimal movement is ok, but keep it low\n        posThreshold: 10 // a multi-tap can be a bit off the initial position\n    },\n\n    getTouchAction: function() {\n        return [TOUCH_ACTION_MANIPULATION];\n    },\n\n    process: function(input) {\n        var options = this.options;\n\n        var validPointers = input.pointers.length === options.pointers;\n        var validMovement = input.distance < options.threshold;\n        var validTouchTime = input.deltaTime < options.time;\n\n        this.reset();\n\n        if ((input.eventType & INPUT_START) && (this.count === 0)) {\n            return this.failTimeout();\n        }\n\n        // we only allow little movement\n        // and we've reached an end event, so a tap is possible\n        if (validMovement && validTouchTime && validPointers) {\n            if (input.eventType != INPUT_END) {\n                return this.failTimeout();\n            }\n\n            var validInterval = this.pTime ? (input.timeStamp - this.pTime < options.interval) : true;\n            var validMultiTap = !this.pCenter || getDistance(this.pCenter, input.center) < options.posThreshold;\n\n            this.pTime = input.timeStamp;\n            this.pCenter = input.center;\n\n            if (!validMultiTap || !validInterval) {\n                this.count = 1;\n            } else {\n                this.count += 1;\n            }\n\n            this._input = input;\n\n            // if tap count matches we have recognized it,\n            // else it has began recognizing...\n            var tapCount = this.count % options.taps;\n            if (tapCount === 0) {\n                // no failing requirements, immediately trigger the tap event\n                // or wait as long as the multitap interval to trigger\n                if (!this.hasRequireFailures()) {\n                    return STATE_RECOGNIZED;\n                } else {\n                    this._timer = setTimeoutContext(function() {\n                        this.state = STATE_RECOGNIZED;\n                        this.tryEmit();\n                    }, options.interval, this);\n                    return STATE_BEGAN;\n                }\n            }\n        }\n        return STATE_FAILED;\n    },\n\n    failTimeout: function() {\n        this._timer = setTimeoutContext(function() {\n            this.state = STATE_FAILED;\n        }, this.options.interval, this);\n        return STATE_FAILED;\n    },\n\n    reset: function() {\n        clearTimeout(this._timer);\n    },\n\n    emit: function() {\n        if (this.state == STATE_RECOGNIZED) {\n            this._input.tapCount = this.count;\n            this.manager.emit(this.options.event, this._input);\n        }\n    }\n});\n\n/**\n * Simple way to create a manager with a default set of recognizers.\n * @param {HTMLElement} element\n * @param {Object} [options]\n * @constructor\n */\nfunction Hammer(element, options) {\n    options = options || {};\n    options.recognizers = ifUndefined(options.recognizers, Hammer.defaults.preset);\n    return new Manager(element, options);\n}\n\n/**\n * @const {string}\n */\nHammer.VERSION = '2.0.7';\n\n/**\n * default settings\n * @namespace\n */\nHammer.defaults = {\n    /**\n     * set if DOM events are being triggered.\n     * But this is slower and unused by simple implementations, so disabled by default.\n     * @type {Boolean}\n     * @default false\n     */\n    domEvents: false,\n\n    /**\n     * The value for the touchAction property/fallback.\n     * When set to `compute` it will magically set the correct value based on the added recognizers.\n     * @type {String}\n     * @default compute\n     */\n    touchAction: TOUCH_ACTION_COMPUTE,\n\n    /**\n     * @type {Boolean}\n     * @default true\n     */\n    enable: true,\n\n    /**\n     * EXPERIMENTAL FEATURE -- can be removed/changed\n     * Change the parent input target element.\n     * If Null, then it is being set the to main element.\n     * @type {Null|EventTarget}\n     * @default null\n     */\n    inputTarget: null,\n\n    /**\n     * force an input class\n     * @type {Null|Function}\n     * @default null\n     */\n    inputClass: null,\n\n    /**\n     * Default recognizer setup when calling `Hammer()`\n     * When creating a new Manager these will be skipped.\n     * @type {Array}\n     */\n    preset: [\n        // RecognizerClass, options, [recognizeWith, ...], [requireFailure, ...]\n        [RotateRecognizer, {enable: false}],\n        [PinchRecognizer, {enable: false}, ['rotate']],\n        [SwipeRecognizer, {direction: DIRECTION_HORIZONTAL}],\n        [PanRecognizer, {direction: DIRECTION_HORIZONTAL}, ['swipe']],\n        [TapRecognizer],\n        [TapRecognizer, {event: 'doubletap', taps: 2}, ['tap']],\n        [PressRecognizer]\n    ],\n\n    /**\n     * Some CSS properties can be used to improve the working of Hammer.\n     * Add them to this method and they will be set when creating a new Manager.\n     * @namespace\n     */\n    cssProps: {\n        /**\n         * Disables text selection to improve the dragging gesture. Mainly for desktop browsers.\n         * @type {String}\n         * @default 'none'\n         */\n        userSelect: 'none',\n\n        /**\n         * Disable the Windows Phone grippers when pressing an element.\n         * @type {String}\n         * @default 'none'\n         */\n        touchSelect: 'none',\n\n        /**\n         * Disables the default callout shown when you touch and hold a touch target.\n         * On iOS, when you touch and hold a touch target such as a link, Safari displays\n         * a callout containing information about the link. This property allows you to disable that callout.\n         * @type {String}\n         * @default 'none'\n         */\n        touchCallout: 'none',\n\n        /**\n         * Specifies whether zooming is enabled. Used by IE10>\n         * @type {String}\n         * @default 'none'\n         */\n        contentZooming: 'none',\n\n        /**\n         * Specifies that an entire element should be draggable instead of its contents. Mainly for desktop browsers.\n         * @type {String}\n         * @default 'none'\n         */\n        userDrag: 'none',\n\n        /**\n         * Overrides the highlight color shown when the user taps a link or a JavaScript\n         * clickable element in iOS. This property obeys the alpha value, if specified.\n         * @type {String}\n         * @default 'rgba(0,0,0,0)'\n         */\n        tapHighlightColor: 'rgba(0,0,0,0)'\n    }\n};\n\nvar STOP = 1;\nvar FORCED_STOP = 2;\n\n/**\n * Manager\n * @param {HTMLElement} element\n * @param {Object} [options]\n * @constructor\n */\nfunction Manager(element, options) {\n    this.options = assign({}, Hammer.defaults, options || {});\n\n    this.options.inputTarget = this.options.inputTarget || element;\n\n    this.handlers = {};\n    this.session = {};\n    this.recognizers = [];\n    this.oldCssProps = {};\n\n    this.element = element;\n    this.input = createInputInstance(this);\n    this.touchAction = new TouchAction(this, this.options.touchAction);\n\n    toggleCssProps(this, true);\n\n    each(this.options.recognizers, function(item) {\n        var recognizer = this.add(new (item[0])(item[1]));\n        item[2] && recognizer.recognizeWith(item[2]);\n        item[3] && recognizer.requireFailure(item[3]);\n    }, this);\n}\n\nManager.prototype = {\n    /**\n     * set options\n     * @param {Object} options\n     * @returns {Manager}\n     */\n    set: function(options) {\n        assign(this.options, options);\n\n        // Options that need a little more setup\n        if (options.touchAction) {\n            this.touchAction.update();\n        }\n        if (options.inputTarget) {\n            // Clean up existing event listeners and reinitialize\n            this.input.destroy();\n            this.input.target = options.inputTarget;\n            this.input.init();\n        }\n        return this;\n    },\n\n    /**\n     * stop recognizing for this session.\n     * This session will be discarded, when a new [input]start event is fired.\n     * When forced, the recognizer cycle is stopped immediately.\n     * @param {Boolean} [force]\n     */\n    stop: function(force) {\n        this.session.stopped = force ? FORCED_STOP : STOP;\n    },\n\n    /**\n     * run the recognizers!\n     * called by the inputHandler function on every movement of the pointers (touches)\n     * it walks through all the recognizers and tries to detect the gesture that is being made\n     * @param {Object} inputData\n     */\n    recognize: function(inputData) {\n        var session = this.session;\n        if (session.stopped) {\n            return;\n        }\n\n        // run the touch-action polyfill\n        this.touchAction.preventDefaults(inputData);\n\n        var recognizer;\n        var recognizers = this.recognizers;\n\n        // this holds the recognizer that is being recognized.\n        // so the recognizer's state needs to be BEGAN, CHANGED, ENDED or RECOGNIZED\n        // if no recognizer is detecting a thing, it is set to `null`\n        var curRecognizer = session.curRecognizer;\n\n        // reset when the last recognizer is recognized\n        // or when we're in a new session\n        if (!curRecognizer || (curRecognizer && curRecognizer.state & STATE_RECOGNIZED)) {\n            curRecognizer = session.curRecognizer = null;\n        }\n\n        var i = 0;\n        while (i < recognizers.length) {\n            recognizer = recognizers[i];\n\n            // find out if we are allowed try to recognize the input for this one.\n            // 1.   allow if the session is NOT forced stopped (see the .stop() method)\n            // 2.   allow if we still haven't recognized a gesture in this session, or the this recognizer is the one\n            //      that is being recognized.\n            // 3.   allow if the recognizer is allowed to run simultaneous with the current recognized recognizer.\n            //      this can be setup with the `recognizeWith()` method on the recognizer.\n            if (session.stopped !== FORCED_STOP && ( // 1\n                    !curRecognizer || recognizer == curRecognizer || // 2\n                    recognizer.canRecognizeWith(curRecognizer))) { // 3\n                recognizer.recognize(inputData);\n            } else {\n                recognizer.reset();\n            }\n\n            // if the recognizer has been recognizing the input as a valid gesture, we want to store this one as the\n            // current active recognizer. but only if we don't already have an active recognizer\n            if (!curRecognizer && recognizer.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED)) {\n                curRecognizer = session.curRecognizer = recognizer;\n            }\n            i++;\n        }\n    },\n\n    /**\n     * get a recognizer by its event name.\n     * @param {Recognizer|String} recognizer\n     * @returns {Recognizer|Null}\n     */\n    get: function(recognizer) {\n        if (recognizer instanceof Recognizer) {\n            return recognizer;\n        }\n\n        var recognizers = this.recognizers;\n        for (var i = 0; i < recognizers.length; i++) {\n            if (recognizers[i].options.event == recognizer) {\n                return recognizers[i];\n            }\n        }\n        return null;\n    },\n\n    /**\n     * add a recognizer to the manager\n     * existing recognizers with the same event name will be removed\n     * @param {Recognizer} recognizer\n     * @returns {Recognizer|Manager}\n     */\n    add: function(recognizer) {\n        if (invokeArrayArg(recognizer, 'add', this)) {\n            return this;\n        }\n\n        // remove existing\n        var existing = this.get(recognizer.options.event);\n        if (existing) {\n            this.remove(existing);\n        }\n\n        this.recognizers.push(recognizer);\n        recognizer.manager = this;\n\n        this.touchAction.update();\n        return recognizer;\n    },\n\n    /**\n     * remove a recognizer by name or instance\n     * @param {Recognizer|String} recognizer\n     * @returns {Manager}\n     */\n    remove: function(recognizer) {\n        if (invokeArrayArg(recognizer, 'remove', this)) {\n            return this;\n        }\n\n        recognizer = this.get(recognizer);\n\n        // let's make sure this recognizer exists\n        if (recognizer) {\n            var recognizers = this.recognizers;\n            var index = inArray(recognizers, recognizer);\n\n            if (index !== -1) {\n                recognizers.splice(index, 1);\n                this.touchAction.update();\n            }\n        }\n\n        return this;\n    },\n\n    /**\n     * bind event\n     * @param {String} events\n     * @param {Function} handler\n     * @returns {EventEmitter} this\n     */\n    on: function(events, handler) {\n        if (events === undefined) {\n            return;\n        }\n        if (handler === undefined) {\n            return;\n        }\n\n        var handlers = this.handlers;\n        each(splitStr(events), function(event) {\n            handlers[event] = handlers[event] || [];\n            handlers[event].push(handler);\n        });\n        return this;\n    },\n\n    /**\n     * unbind event, leave emit blank to remove all handlers\n     * @param {String} events\n     * @param {Function} [handler]\n     * @returns {EventEmitter} this\n     */\n    off: function(events, handler) {\n        if (events === undefined) {\n            return;\n        }\n\n        var handlers = this.handlers;\n        each(splitStr(events), function(event) {\n            if (!handler) {\n                delete handlers[event];\n            } else {\n                handlers[event] && handlers[event].splice(inArray(handlers[event], handler), 1);\n            }\n        });\n        return this;\n    },\n\n    /**\n     * emit event to the listeners\n     * @param {String} event\n     * @param {Object} data\n     */\n    emit: function(event, data) {\n        // we also want to trigger dom events\n        if (this.options.domEvents) {\n            triggerDomEvent(event, data);\n        }\n\n        // no handlers, so skip it all\n        var handlers = this.handlers[event] && this.handlers[event].slice();\n        if (!handlers || !handlers.length) {\n            return;\n        }\n\n        data.type = event;\n        data.preventDefault = function() {\n            data.srcEvent.preventDefault();\n        };\n\n        var i = 0;\n        while (i < handlers.length) {\n            handlers[i](data);\n            i++;\n        }\n    },\n\n    /**\n     * destroy the manager and unbinds all events\n     * it doesn't unbind dom events, that is the user own responsibility\n     */\n    destroy: function() {\n        this.element && toggleCssProps(this, false);\n\n        this.handlers = {};\n        this.session = {};\n        this.input.destroy();\n        this.element = null;\n    }\n};\n\n/**\n * add/remove the css properties as defined in manager.options.cssProps\n * @param {Manager} manager\n * @param {Boolean} add\n */\nfunction toggleCssProps(manager, add) {\n    var element = manager.element;\n    if (!element.style) {\n        return;\n    }\n    var prop;\n    each(manager.options.cssProps, function(value, name) {\n        prop = prefixed(element.style, name);\n        if (add) {\n            manager.oldCssProps[prop] = element.style[prop];\n            element.style[prop] = value;\n        } else {\n            element.style[prop] = manager.oldCssProps[prop] || '';\n        }\n    });\n    if (!add) {\n        manager.oldCssProps = {};\n    }\n}\n\n/**\n * trigger dom event\n * @param {String} event\n * @param {Object} data\n */\nfunction triggerDomEvent(event, data) {\n    var gestureEvent = document.createEvent('Event');\n    gestureEvent.initEvent(event, true, true);\n    gestureEvent.gesture = data;\n    data.target.dispatchEvent(gestureEvent);\n}\n\nassign(Hammer, {\n    INPUT_START: INPUT_START,\n    INPUT_MOVE: INPUT_MOVE,\n    INPUT_END: INPUT_END,\n    INPUT_CANCEL: INPUT_CANCEL,\n\n    STATE_POSSIBLE: STATE_POSSIBLE,\n    STATE_BEGAN: STATE_BEGAN,\n    STATE_CHANGED: STATE_CHANGED,\n    STATE_ENDED: STATE_ENDED,\n    STATE_RECOGNIZED: STATE_RECOGNIZED,\n    STATE_CANCELLED: STATE_CANCELLED,\n    STATE_FAILED: STATE_FAILED,\n\n    DIRECTION_NONE: DIRECTION_NONE,\n    DIRECTION_LEFT: DIRECTION_LEFT,\n    DIRECTION_RIGHT: DIRECTION_RIGHT,\n    DIRECTION_UP: DIRECTION_UP,\n    DIRECTION_DOWN: DIRECTION_DOWN,\n    DIRECTION_HORIZONTAL: DIRECTION_HORIZONTAL,\n    DIRECTION_VERTICAL: DIRECTION_VERTICAL,\n    DIRECTION_ALL: DIRECTION_ALL,\n\n    Manager: Manager,\n    Input: Input,\n    TouchAction: TouchAction,\n\n    TouchInput: TouchInput,\n    MouseInput: MouseInput,\n    PointerEventInput: PointerEventInput,\n    TouchMouseInput: TouchMouseInput,\n    SingleTouchInput: SingleTouchInput,\n\n    Recognizer: Recognizer,\n    AttrRecognizer: AttrRecognizer,\n    Tap: TapRecognizer,\n    Pan: PanRecognizer,\n    Swipe: SwipeRecognizer,\n    Pinch: PinchRecognizer,\n    Rotate: RotateRecognizer,\n    Press: PressRecognizer,\n\n    on: addEventListeners,\n    off: removeEventListeners,\n    each: each,\n    merge: merge,\n    extend: extend,\n    assign: assign,\n    inherit: inherit,\n    bindFn: bindFn,\n    prefixed: prefixed\n});\n\n// this prevents errors when Hammer is loaded in the presence of an AMD\n//  style loader but by script tag, not by the loader.\nvar freeGlobal = (typeof window !== 'undefined' ? window : (typeof self !== 'undefined' ? self : {})); // jshint ignore:line\nfreeGlobal.Hammer = Hammer;\n\nif (typeof define === 'function' && define.amd) {\n    define(function() {\n        return Hammer;\n    });\n} else if (typeof module != 'undefined' && module.exports) {\n    module.exports = Hammer;\n} else {\n    window[exportName] = Hammer;\n}\n\n})(window, document, 'Hammer');\n", "/*!\n* chartjs-plugin-zoom v2.2.0\n* https://www.chartjs.org/chartjs-plugin-zoom/2.2.0/\n * (c) 2016-2024 chartjs-plugin-zoom Contributors\n * Released under the MIT License\n */\nimport Hammer from 'hammerjs';\nimport { each, valueOrDefault, almostEquals, callback, sign, getRelativePosition, _isPointInArea } from 'chart.js/helpers';\n\nconst getModifierKey = opts => opts && opts.enabled && opts.modifierKey;\nconst keyPressed = (key, event) => key && event[key + 'Key'];\nconst keyNotPressed = (key, event) => key && !event[key + 'Key'];\nfunction directionEnabled(mode, dir, chart) {\n  if (mode === undefined) {\n    return true;\n  } else if (typeof mode === 'string') {\n    return mode.indexOf(dir) !== -1;\n  } else if (typeof mode === 'function') {\n    return mode({chart}).indexOf(dir) !== -1;\n  }\n  return false;\n}\nfunction directionsEnabled(mode, chart) {\n  if (typeof mode === 'function') {\n    mode = mode({chart});\n  }\n  if (typeof mode === 'string') {\n    return {x: mode.indexOf('x') !== -1, y: mode.indexOf('y') !== -1};\n  }\n  return {x: false, y: false};\n}\nfunction debounce(fn, delay) {\n  let timeout;\n  return function() {\n    clearTimeout(timeout);\n    timeout = setTimeout(fn, delay);\n    return delay;\n  };\n}\nfunction getScaleUnderPoint({x, y}, chart) {\n  const scales = chart.scales;\n  const scaleIds = Object.keys(scales);\n  for (let i = 0; i < scaleIds.length; i++) {\n    const scale = scales[scaleIds[i]];\n    if (y >= scale.top && y <= scale.bottom && x >= scale.left && x <= scale.right) {\n      return scale;\n    }\n  }\n  return null;\n}\nfunction getEnabledScalesByPoint(options, point, chart) {\n  const {mode = 'xy', scaleMode, overScaleMode} = options || {};\n  const scale = getScaleUnderPoint(point, chart);\n  const enabled = directionsEnabled(mode, chart);\n  const scaleEnabled = directionsEnabled(scaleMode, chart);\n  if (overScaleMode) {\n    const overScaleEnabled = directionsEnabled(overScaleMode, chart);\n    for (const axis of ['x', 'y']) {\n      if (overScaleEnabled[axis]) {\n        scaleEnabled[axis] = enabled[axis];\n        enabled[axis] = false;\n      }\n    }\n  }\n  if (scale && scaleEnabled[scale.axis]) {\n    return [scale];\n  }\n  const enabledScales = [];\n  each(chart.scales, function(scaleItem) {\n    if (enabled[scaleItem.axis]) {\n      enabledScales.push(scaleItem);\n    }\n  });\n  return enabledScales;\n}\n\nconst chartStates = new WeakMap();\nfunction getState(chart) {\n  let state = chartStates.get(chart);\n  if (!state) {\n    state = {\n      originalScaleLimits: {},\n      updatedScaleLimits: {},\n      handlers: {},\n      panDelta: {},\n      dragging: false,\n      panning: false\n    };\n    chartStates.set(chart, state);\n  }\n  return state;\n}\nfunction removeState(chart) {\n  chartStates.delete(chart);\n}\n\nfunction zoomDelta(val, min, range, newRange) {\n  const minPercent = Math.max(0, Math.min(1, (val - min) / range || 0));\n  const maxPercent = 1 - minPercent;\n  return {\n    min: newRange * minPercent,\n    max: newRange * maxPercent\n  };\n}\nfunction getValueAtPoint(scale, point) {\n  const pixel = scale.isHorizontal() ? point.x : point.y;\n  return scale.getValueForPixel(pixel);\n}\nfunction linearZoomDelta(scale, zoom, center) {\n  const range = scale.max - scale.min;\n  const newRange = range * (zoom - 1);\n  const centerValue = getValueAtPoint(scale, center);\n  return zoomDelta(centerValue, scale.min, range, newRange);\n}\nfunction logarithmicZoomRange(scale, zoom, center) {\n  const centerValue = getValueAtPoint(scale, center);\n  if (centerValue === undefined) {\n    return {min: scale.min, max: scale.max};\n  }\n  const logMin = Math.log10(scale.min);\n  const logMax = Math.log10(scale.max);\n  const logCenter = Math.log10(centerValue);\n  const logRange = logMax - logMin;\n  const newLogRange = logRange * (zoom - 1);\n  const delta = zoomDelta(logCenter, logMin, logRange, newLogRange);\n  return {\n    min: Math.pow(10, logMin + delta.min),\n    max: Math.pow(10, logMax - delta.max),\n  };\n}\nfunction getScaleLimits(scale, limits) {\n  return limits && (limits[scale.id] || limits[scale.axis]) || {};\n}\nfunction getLimit(state, scale, scaleLimits, prop, fallback) {\n  let limit = scaleLimits[prop];\n  if (limit === 'original') {\n    const original = state.originalScaleLimits[scale.id][prop];\n    limit = valueOrDefault(original.options, original.scale);\n  }\n  return valueOrDefault(limit, fallback);\n}\nfunction linearRange(scale, pixel0, pixel1) {\n  const v0 = scale.getValueForPixel(pixel0);\n  const v1 = scale.getValueForPixel(pixel1);\n  return {\n    min: Math.min(v0, v1),\n    max: Math.max(v0, v1)\n  };\n}\nfunction fixRange(range, {min, max, minLimit, maxLimit}, originalLimits) {\n  const offset = (range - max + min) / 2;\n  min -= offset;\n  max += offset;\n  const origMin = originalLimits.min.options ?? originalLimits.min.scale;\n  const origMax = originalLimits.max.options ?? originalLimits.max.scale;\n  const epsilon = range / 1e6;\n  if (almostEquals(min, origMin, epsilon)) {\n    min = origMin;\n  }\n  if (almostEquals(max, origMax, epsilon)) {\n    max = origMax;\n  }\n  if (min < minLimit) {\n    min = minLimit;\n    max = Math.min(minLimit + range, maxLimit);\n  } else if (max > maxLimit) {\n    max = maxLimit;\n    min = Math.max(maxLimit - range, minLimit);\n  }\n  return {min, max};\n}\nfunction updateRange(scale, {min, max}, limits, zoom = false) {\n  const state = getState(scale.chart);\n  const {options: scaleOpts} = scale;\n  const scaleLimits = getScaleLimits(scale, limits);\n  const {minRange = 0} = scaleLimits;\n  const minLimit = getLimit(state, scale, scaleLimits, 'min', -Infinity);\n  const maxLimit = getLimit(state, scale, scaleLimits, 'max', Infinity);\n  if (zoom === 'pan' && (min < minLimit || max > maxLimit)) {\n    return true;\n  }\n  const scaleRange = scale.max - scale.min;\n  const range = zoom ? Math.max(max - min, minRange) : scaleRange;\n  if (zoom && range === minRange && scaleRange <= minRange) {\n    return true;\n  }\n  const newRange = fixRange(range, {min, max, minLimit, maxLimit}, state.originalScaleLimits[scale.id]);\n  scaleOpts.min = newRange.min;\n  scaleOpts.max = newRange.max;\n  state.updatedScaleLimits[scale.id] = newRange;\n  return scale.parse(newRange.min) !== scale.min || scale.parse(newRange.max) !== scale.max;\n}\nfunction zoomNumericalScale(scale, zoom, center, limits) {\n  const delta = linearZoomDelta(scale, zoom, center);\n  const newRange = {min: scale.min + delta.min, max: scale.max - delta.max};\n  return updateRange(scale, newRange, limits, true);\n}\nfunction zoomLogarithmicScale(scale, zoom, center, limits) {\n  const newRange = logarithmicZoomRange(scale, zoom, center);\n  return updateRange(scale, newRange, limits, true);\n}\nfunction zoomRectNumericalScale(scale, from, to, limits) {\n  updateRange(scale, linearRange(scale, from, to), limits, true);\n}\nconst integerChange = (v) => v === 0 || isNaN(v) ? 0 : v < 0 ? Math.min(Math.round(v), -1) : Math.max(Math.round(v), 1);\nfunction existCategoryFromMaxZoom(scale) {\n  const labels = scale.getLabels();\n  const maxIndex = labels.length - 1;\n  if (scale.min > 0) {\n    scale.min -= 1;\n  }\n  if (scale.max < maxIndex) {\n    scale.max += 1;\n  }\n}\nfunction zoomCategoryScale(scale, zoom, center, limits) {\n  const delta = linearZoomDelta(scale, zoom, center);\n  if (scale.min === scale.max && zoom < 1) {\n    existCategoryFromMaxZoom(scale);\n  }\n  const newRange = {min: scale.min + integerChange(delta.min), max: scale.max - integerChange(delta.max)};\n  return updateRange(scale, newRange, limits, true);\n}\nfunction scaleLength(scale) {\n  return scale.isHorizontal() ? scale.width : scale.height;\n}\nfunction panCategoryScale(scale, delta, limits) {\n  const labels = scale.getLabels();\n  const lastLabelIndex = labels.length - 1;\n  let {min, max} = scale;\n  const range = Math.max(max - min, 1);\n  const stepDelta = Math.round(scaleLength(scale) / Math.max(range, 10));\n  const stepSize = Math.round(Math.abs(delta / stepDelta));\n  let applied;\n  if (delta < -stepDelta) {\n    max = Math.min(max + stepSize, lastLabelIndex);\n    min = range === 1 ? max : max - range;\n    applied = max === lastLabelIndex;\n  } else if (delta > stepDelta) {\n    min = Math.max(0, min - stepSize);\n    max = range === 1 ? min : min + range;\n    applied = min === 0;\n  }\n  return updateRange(scale, {min, max}, limits) || applied;\n}\nconst OFFSETS = {\n  second: 500,\n  minute: 30 * 1000,\n  hour: 30 * 60 * 1000,\n  day: 12 * 60 * 60 * 1000,\n  week: 3.5 * 24 * 60 * 60 * 1000,\n  month: 15 * 24 * 60 * 60 * 1000,\n  quarter: 60 * 24 * 60 * 60 * 1000,\n  year: 182 * 24 * 60 * 60 * 1000\n};\nfunction panNumericalScale(scale, delta, limits, pan = false) {\n  const {min: prevStart, max: prevEnd, options} = scale;\n  const round = options.time && options.time.round;\n  const offset = OFFSETS[round] || 0;\n  const newMin = scale.getValueForPixel(scale.getPixelForValue(prevStart + offset) - delta);\n  const newMax = scale.getValueForPixel(scale.getPixelForValue(prevEnd + offset) - delta);\n  if (isNaN(newMin) || isNaN(newMax)) {\n    return true;\n  }\n  return updateRange(scale, {min: newMin, max: newMax}, limits, pan ? 'pan' : false);\n}\nfunction panNonLinearScale(scale, delta, limits) {\n  return panNumericalScale(scale, delta, limits, true);\n}\nconst zoomFunctions = {\n  category: zoomCategoryScale,\n  default: zoomNumericalScale,\n  logarithmic: zoomLogarithmicScale,\n};\nconst zoomRectFunctions = {\n  default: zoomRectNumericalScale,\n};\nconst panFunctions = {\n  category: panCategoryScale,\n  default: panNumericalScale,\n  logarithmic: panNonLinearScale,\n  timeseries: panNonLinearScale,\n};\n\nfunction shouldUpdateScaleLimits(scale, originalScaleLimits, updatedScaleLimits) {\n  const {id, options: {min, max}} = scale;\n  if (!originalScaleLimits[id] || !updatedScaleLimits[id]) {\n    return true;\n  }\n  const previous = updatedScaleLimits[id];\n  return previous.min !== min || previous.max !== max;\n}\nfunction removeMissingScales(limits, scales) {\n  each(limits, (opt, key) => {\n    if (!scales[key]) {\n      delete limits[key];\n    }\n  });\n}\nfunction storeOriginalScaleLimits(chart, state) {\n  const {scales} = chart;\n  const {originalScaleLimits, updatedScaleLimits} = state;\n  each(scales, function(scale) {\n    if (shouldUpdateScaleLimits(scale, originalScaleLimits, updatedScaleLimits)) {\n      originalScaleLimits[scale.id] = {\n        min: {scale: scale.min, options: scale.options.min},\n        max: {scale: scale.max, options: scale.options.max},\n      };\n    }\n  });\n  removeMissingScales(originalScaleLimits, scales);\n  removeMissingScales(updatedScaleLimits, scales);\n  return originalScaleLimits;\n}\nfunction doZoom(scale, amount, center, limits) {\n  const fn = zoomFunctions[scale.type] || zoomFunctions.default;\n  callback(fn, [scale, amount, center, limits]);\n}\nfunction doZoomRect(scale, from, to, limits) {\n  const fn = zoomRectFunctions[scale.type] || zoomRectFunctions.default;\n  callback(fn, [scale, from, to, limits]);\n}\nfunction getCenter(chart) {\n  const ca = chart.chartArea;\n  return {\n    x: (ca.left + ca.right) / 2,\n    y: (ca.top + ca.bottom) / 2,\n  };\n}\nfunction zoom(chart, amount, transition = 'none', trigger = 'api') {\n  const {x = 1, y = 1, focalPoint = getCenter(chart)} = typeof amount === 'number' ? {x: amount, y: amount} : amount;\n  const state = getState(chart);\n  const {options: {limits, zoom: zoomOptions}} = state;\n  storeOriginalScaleLimits(chart, state);\n  const xEnabled = x !== 1;\n  const yEnabled = y !== 1;\n  const enabledScales = getEnabledScalesByPoint(zoomOptions, focalPoint, chart);\n  each(enabledScales || chart.scales, function(scale) {\n    if (scale.isHorizontal() && xEnabled) {\n      doZoom(scale, x, focalPoint, limits);\n    } else if (!scale.isHorizontal() && yEnabled) {\n      doZoom(scale, y, focalPoint, limits);\n    }\n  });\n  chart.update(transition);\n  callback(zoomOptions.onZoom, [{chart, trigger}]);\n}\nfunction zoomRect(chart, p0, p1, transition = 'none', trigger = 'api') {\n  const state = getState(chart);\n  const {options: {limits, zoom: zoomOptions}} = state;\n  const {mode = 'xy'} = zoomOptions;\n  storeOriginalScaleLimits(chart, state);\n  const xEnabled = directionEnabled(mode, 'x', chart);\n  const yEnabled = directionEnabled(mode, 'y', chart);\n  each(chart.scales, function(scale) {\n    if (scale.isHorizontal() && xEnabled) {\n      doZoomRect(scale, p0.x, p1.x, limits);\n    } else if (!scale.isHorizontal() && yEnabled) {\n      doZoomRect(scale, p0.y, p1.y, limits);\n    }\n  });\n  chart.update(transition);\n  callback(zoomOptions.onZoom, [{chart, trigger}]);\n}\nfunction zoomScale(chart, scaleId, range, transition = 'none', trigger = 'api') {\n  const state = getState(chart);\n  storeOriginalScaleLimits(chart, state);\n  const scale = chart.scales[scaleId];\n  updateRange(scale, range, undefined, true);\n  chart.update(transition);\n  callback(state.options.zoom?.onZoom, [{chart, trigger}]);\n}\nfunction resetZoom(chart, transition = 'default') {\n  const state = getState(chart);\n  const originalScaleLimits = storeOriginalScaleLimits(chart, state);\n  each(chart.scales, function(scale) {\n    const scaleOptions = scale.options;\n    if (originalScaleLimits[scale.id]) {\n      scaleOptions.min = originalScaleLimits[scale.id].min.options;\n      scaleOptions.max = originalScaleLimits[scale.id].max.options;\n    } else {\n      delete scaleOptions.min;\n      delete scaleOptions.max;\n    }\n    delete state.updatedScaleLimits[scale.id];\n  });\n  chart.update(transition);\n  callback(state.options.zoom.onZoomComplete, [{chart}]);\n}\nfunction getOriginalRange(state, scaleId) {\n  const original = state.originalScaleLimits[scaleId];\n  if (!original) {\n    return;\n  }\n  const {min, max} = original;\n  return valueOrDefault(max.options, max.scale) - valueOrDefault(min.options, min.scale);\n}\nfunction getZoomLevel(chart) {\n  const state = getState(chart);\n  let min = 1;\n  let max = 1;\n  each(chart.scales, function(scale) {\n    const origRange = getOriginalRange(state, scale.id);\n    if (origRange) {\n      const level = Math.round(origRange / (scale.max - scale.min) * 100) / 100;\n      min = Math.min(min, level);\n      max = Math.max(max, level);\n    }\n  });\n  return min < 1 ? min : max;\n}\nfunction panScale(scale, delta, limits, state) {\n  const {panDelta} = state;\n  const storedDelta = panDelta[scale.id] || 0;\n  if (sign(storedDelta) === sign(delta)) {\n    delta += storedDelta;\n  }\n  const fn = panFunctions[scale.type] || panFunctions.default;\n  if (callback(fn, [scale, delta, limits])) {\n    panDelta[scale.id] = 0;\n  } else {\n    panDelta[scale.id] = delta;\n  }\n}\nfunction pan(chart, delta, enabledScales, transition = 'none') {\n  const {x = 0, y = 0} = typeof delta === 'number' ? {x: delta, y: delta} : delta;\n  const state = getState(chart);\n  const {options: {pan: panOptions, limits}} = state;\n  const {onPan} = panOptions || {};\n  storeOriginalScaleLimits(chart, state);\n  const xEnabled = x !== 0;\n  const yEnabled = y !== 0;\n  each(enabledScales || chart.scales, function(scale) {\n    if (scale.isHorizontal() && xEnabled) {\n      panScale(scale, x, limits, state);\n    } else if (!scale.isHorizontal() && yEnabled) {\n      panScale(scale, y, limits, state);\n    }\n  });\n  chart.update(transition);\n  callback(onPan, [{chart}]);\n}\nfunction getInitialScaleBounds(chart) {\n  const state = getState(chart);\n  storeOriginalScaleLimits(chart, state);\n  const scaleBounds = {};\n  for (const scaleId of Object.keys(chart.scales)) {\n    const {min, max} = state.originalScaleLimits[scaleId] || {min: {}, max: {}};\n    scaleBounds[scaleId] = {min: min.scale, max: max.scale};\n  }\n  return scaleBounds;\n}\nfunction getZoomedScaleBounds(chart) {\n  const state = getState(chart);\n  const scaleBounds = {};\n  for (const scaleId of Object.keys(chart.scales)) {\n    scaleBounds[scaleId] = state.updatedScaleLimits[scaleId];\n  }\n  return scaleBounds;\n}\nfunction isZoomedOrPanned(chart) {\n  const scaleBounds = getInitialScaleBounds(chart);\n  for (const scaleId of Object.keys(chart.scales)) {\n    const {min: originalMin, max: originalMax} = scaleBounds[scaleId];\n    if (originalMin !== undefined && chart.scales[scaleId].min !== originalMin) {\n      return true;\n    }\n    if (originalMax !== undefined && chart.scales[scaleId].max !== originalMax) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction isZoomingOrPanning(chart) {\n  const state = getState(chart);\n  return state.panning || state.dragging;\n}\n\nconst clamp = (x, from, to) => Math.min(to, Math.max(from, x));\nfunction removeHandler(chart, type) {\n  const {handlers} = getState(chart);\n  const handler = handlers[type];\n  if (handler && handler.target) {\n    handler.target.removeEventListener(type, handler);\n    delete handlers[type];\n  }\n}\nfunction addHandler(chart, target, type, handler) {\n  const {handlers, options} = getState(chart);\n  const oldHandler = handlers[type];\n  if (oldHandler && oldHandler.target === target) {\n    return;\n  }\n  removeHandler(chart, type);\n  handlers[type] = (event) => handler(chart, event, options);\n  handlers[type].target = target;\n  const passive = type === 'wheel' ? false : undefined;\n  target.addEventListener(type, handlers[type], {passive});\n}\nfunction mouseMove(chart, event) {\n  const state = getState(chart);\n  if (state.dragStart) {\n    state.dragging = true;\n    state.dragEnd = event;\n    chart.update('none');\n  }\n}\nfunction keyDown(chart, event) {\n  const state = getState(chart);\n  if (!state.dragStart || event.key !== 'Escape') {\n    return;\n  }\n  removeHandler(chart, 'keydown');\n  state.dragging = false;\n  state.dragStart = state.dragEnd = null;\n  chart.update('none');\n}\nfunction getPointPosition(event, chart) {\n  if (event.target !== chart.canvas) {\n    const canvasArea = chart.canvas.getBoundingClientRect();\n    return {\n      x: event.clientX - canvasArea.left,\n      y: event.clientY - canvasArea.top,\n    };\n  }\n  return getRelativePosition(event, chart);\n}\nfunction zoomStart(chart, event, zoomOptions) {\n  const {onZoomStart, onZoomRejected} = zoomOptions;\n  if (onZoomStart) {\n    const point = getPointPosition(event, chart);\n    if (callback(onZoomStart, [{chart, event, point}]) === false) {\n      callback(onZoomRejected, [{chart, event}]);\n      return false;\n    }\n  }\n}\nfunction mouseDown(chart, event) {\n  if (chart.legend) {\n    const point = getRelativePosition(event, chart);\n    if (_isPointInArea(point, chart.legend)) {\n      return;\n    }\n  }\n  const state = getState(chart);\n  const {pan: panOptions, zoom: zoomOptions = {}} = state.options;\n  if (\n    event.button !== 0 ||\n    keyPressed(getModifierKey(panOptions), event) ||\n    keyNotPressed(getModifierKey(zoomOptions.drag), event)\n  ) {\n    return callback(zoomOptions.onZoomRejected, [{chart, event}]);\n  }\n  if (zoomStart(chart, event, zoomOptions) === false) {\n    return;\n  }\n  state.dragStart = event;\n  addHandler(chart, chart.canvas.ownerDocument, 'mousemove', mouseMove);\n  addHandler(chart, window.document, 'keydown', keyDown);\n}\nfunction applyAspectRatio({begin, end}, aspectRatio) {\n  let width = end.x - begin.x;\n  let height = end.y - begin.y;\n  const ratio = Math.abs(width / height);\n  if (ratio > aspectRatio) {\n    width = Math.sign(width) * Math.abs(height * aspectRatio);\n  } else if (ratio < aspectRatio) {\n    height = Math.sign(height) * Math.abs(width / aspectRatio);\n  }\n  end.x = begin.x + width;\n  end.y = begin.y + height;\n}\nfunction applyMinMaxProps(rect, chartArea, points, {min, max, prop}) {\n  rect[min] = clamp(Math.min(points.begin[prop], points.end[prop]), chartArea[min], chartArea[max]);\n  rect[max] = clamp(Math.max(points.begin[prop], points.end[prop]), chartArea[min], chartArea[max]);\n}\nfunction getRelativePoints(chart, pointEvents, maintainAspectRatio) {\n  const points = {\n    begin: getPointPosition(pointEvents.dragStart, chart),\n    end: getPointPosition(pointEvents.dragEnd, chart),\n  };\n  if (maintainAspectRatio) {\n    const aspectRatio = chart.chartArea.width / chart.chartArea.height;\n    applyAspectRatio(points, aspectRatio);\n  }\n  return points;\n}\nfunction computeDragRect(chart, mode, pointEvents, maintainAspectRatio) {\n  const xEnabled = directionEnabled(mode, 'x', chart);\n  const yEnabled = directionEnabled(mode, 'y', chart);\n  const {top, left, right, bottom, width: chartWidth, height: chartHeight} = chart.chartArea;\n  const rect = {top, left, right, bottom};\n  const points = getRelativePoints(chart, pointEvents, maintainAspectRatio && xEnabled && yEnabled);\n  if (xEnabled) {\n    applyMinMaxProps(rect, chart.chartArea, points, {min: 'left', max: 'right', prop: 'x'});\n  }\n  if (yEnabled) {\n    applyMinMaxProps(rect, chart.chartArea, points, {min: 'top', max: 'bottom', prop: 'y'});\n  }\n  const width = rect.right - rect.left;\n  const height = rect.bottom - rect.top;\n  return {\n    ...rect,\n    width,\n    height,\n    zoomX: xEnabled && width ? 1 + ((chartWidth - width) / chartWidth) : 1,\n    zoomY: yEnabled && height ? 1 + ((chartHeight - height) / chartHeight) : 1\n  };\n}\nfunction mouseUp(chart, event) {\n  const state = getState(chart);\n  if (!state.dragStart) {\n    return;\n  }\n  removeHandler(chart, 'mousemove');\n  const {mode, onZoomComplete, drag: {threshold = 0, maintainAspectRatio}} = state.options.zoom;\n  const rect = computeDragRect(chart, mode, {dragStart: state.dragStart, dragEnd: event}, maintainAspectRatio);\n  const distanceX = directionEnabled(mode, 'x', chart) ? rect.width : 0;\n  const distanceY = directionEnabled(mode, 'y', chart) ? rect.height : 0;\n  const distance = Math.sqrt(distanceX * distanceX + distanceY * distanceY);\n  state.dragStart = state.dragEnd = null;\n  if (distance <= threshold) {\n    state.dragging = false;\n    chart.update('none');\n    return;\n  }\n  zoomRect(chart, {x: rect.left, y: rect.top}, {x: rect.right, y: rect.bottom}, 'zoom', 'drag');\n  state.dragging = false;\n  state.filterNextClick = true;\n  callback(onZoomComplete, [{chart}]);\n}\nfunction wheelPreconditions(chart, event, zoomOptions) {\n  if (keyNotPressed(getModifierKey(zoomOptions.wheel), event)) {\n    callback(zoomOptions.onZoomRejected, [{chart, event}]);\n    return;\n  }\n  if (zoomStart(chart, event, zoomOptions) === false) {\n    return;\n  }\n  if (event.cancelable) {\n    event.preventDefault();\n  }\n  if (event.deltaY === undefined) {\n    return;\n  }\n  return true;\n}\nfunction wheel(chart, event) {\n  const {handlers: {onZoomComplete}, options: {zoom: zoomOptions}} = getState(chart);\n  if (!wheelPreconditions(chart, event, zoomOptions)) {\n    return;\n  }\n  const rect = event.target.getBoundingClientRect();\n  const speed = zoomOptions.wheel.speed;\n  const percentage = event.deltaY >= 0 ? 2 - 1 / (1 - speed) : 1 + speed;\n  const amount = {\n    x: percentage,\n    y: percentage,\n    focalPoint: {\n      x: event.clientX - rect.left,\n      y: event.clientY - rect.top\n    }\n  };\n  zoom(chart, amount, 'zoom', 'wheel');\n  callback(onZoomComplete, [{chart}]);\n}\nfunction addDebouncedHandler(chart, name, handler, delay) {\n  if (handler) {\n    getState(chart).handlers[name] = debounce(() => callback(handler, [{chart}]), delay);\n  }\n}\nfunction addListeners(chart, options) {\n  const canvas = chart.canvas;\n  const {wheel: wheelOptions, drag: dragOptions, onZoomComplete} = options.zoom;\n  if (wheelOptions.enabled) {\n    addHandler(chart, canvas, 'wheel', wheel);\n    addDebouncedHandler(chart, 'onZoomComplete', onZoomComplete, 250);\n  } else {\n    removeHandler(chart, 'wheel');\n  }\n  if (dragOptions.enabled) {\n    addHandler(chart, canvas, 'mousedown', mouseDown);\n    addHandler(chart, canvas.ownerDocument, 'mouseup', mouseUp);\n  } else {\n    removeHandler(chart, 'mousedown');\n    removeHandler(chart, 'mousemove');\n    removeHandler(chart, 'mouseup');\n    removeHandler(chart, 'keydown');\n  }\n}\nfunction removeListeners(chart) {\n  removeHandler(chart, 'mousedown');\n  removeHandler(chart, 'mousemove');\n  removeHandler(chart, 'mouseup');\n  removeHandler(chart, 'wheel');\n  removeHandler(chart, 'click');\n  removeHandler(chart, 'keydown');\n}\n\nfunction createEnabler(chart, state) {\n  return function(recognizer, event) {\n    const {pan: panOptions, zoom: zoomOptions = {}} = state.options;\n    if (!panOptions || !panOptions.enabled) {\n      return false;\n    }\n    const srcEvent = event && event.srcEvent;\n    if (!srcEvent) {\n      return true;\n    }\n    if (!state.panning && event.pointerType === 'mouse' && (\n      keyNotPressed(getModifierKey(panOptions), srcEvent) || keyPressed(getModifierKey(zoomOptions.drag), srcEvent))\n    ) {\n      callback(panOptions.onPanRejected, [{chart, event}]);\n      return false;\n    }\n    return true;\n  };\n}\nfunction pinchAxes(p0, p1) {\n  const pinchX = Math.abs(p0.clientX - p1.clientX);\n  const pinchY = Math.abs(p0.clientY - p1.clientY);\n  const p = pinchX / pinchY;\n  let x, y;\n  if (p > 0.3 && p < 1.7) {\n    x = y = true;\n  } else if (pinchX > pinchY) {\n    x = true;\n  } else {\n    y = true;\n  }\n  return {x, y};\n}\nfunction handlePinch(chart, state, e) {\n  if (state.scale) {\n    const {center, pointers} = e;\n    const zoomPercent = 1 / state.scale * e.scale;\n    const rect = e.target.getBoundingClientRect();\n    const pinch = pinchAxes(pointers[0], pointers[1]);\n    const mode = state.options.zoom.mode;\n    const amount = {\n      x: pinch.x && directionEnabled(mode, 'x', chart) ? zoomPercent : 1,\n      y: pinch.y && directionEnabled(mode, 'y', chart) ? zoomPercent : 1,\n      focalPoint: {\n        x: center.x - rect.left,\n        y: center.y - rect.top\n      }\n    };\n    zoom(chart, amount, 'zoom', 'pinch');\n    state.scale = e.scale;\n  }\n}\nfunction startPinch(chart, state, event) {\n  if (state.options.zoom.pinch.enabled) {\n    const point = getRelativePosition(event, chart);\n    if (callback(state.options.zoom.onZoomStart, [{chart, event, point}]) === false) {\n      state.scale = null;\n      callback(state.options.zoom.onZoomRejected, [{chart, event}]);\n    } else {\n      state.scale = 1;\n    }\n  }\n}\nfunction endPinch(chart, state, e) {\n  if (state.scale) {\n    handlePinch(chart, state, e);\n    state.scale = null;\n    callback(state.options.zoom.onZoomComplete, [{chart}]);\n  }\n}\nfunction handlePan(chart, state, e) {\n  const delta = state.delta;\n  if (delta) {\n    state.panning = true;\n    pan(chart, {x: e.deltaX - delta.x, y: e.deltaY - delta.y}, state.panScales);\n    state.delta = {x: e.deltaX, y: e.deltaY};\n  }\n}\nfunction startPan(chart, state, event) {\n  const {enabled, onPanStart, onPanRejected} = state.options.pan;\n  if (!enabled) {\n    return;\n  }\n  const rect = event.target.getBoundingClientRect();\n  const point = {\n    x: event.center.x - rect.left,\n    y: event.center.y - rect.top\n  };\n  if (callback(onPanStart, [{chart, event, point}]) === false) {\n    return callback(onPanRejected, [{chart, event}]);\n  }\n  state.panScales = getEnabledScalesByPoint(state.options.pan, point, chart);\n  state.delta = {x: 0, y: 0};\n  handlePan(chart, state, event);\n}\nfunction endPan(chart, state) {\n  state.delta = null;\n  if (state.panning) {\n    state.panning = false;\n    state.filterNextClick = true;\n    callback(state.options.pan.onPanComplete, [{chart}]);\n  }\n}\nconst hammers = new WeakMap();\nfunction startHammer(chart, options) {\n  const state = getState(chart);\n  const canvas = chart.canvas;\n  const {pan: panOptions, zoom: zoomOptions} = options;\n  const mc = new Hammer.Manager(canvas);\n  if (zoomOptions && zoomOptions.pinch.enabled) {\n    mc.add(new Hammer.Pinch());\n    mc.on('pinchstart', (e) => startPinch(chart, state, e));\n    mc.on('pinch', (e) => handlePinch(chart, state, e));\n    mc.on('pinchend', (e) => endPinch(chart, state, e));\n  }\n  if (panOptions && panOptions.enabled) {\n    mc.add(new Hammer.Pan({\n      threshold: panOptions.threshold,\n      enable: createEnabler(chart, state)\n    }));\n    mc.on('panstart', (e) => startPan(chart, state, e));\n    mc.on('panmove', (e) => handlePan(chart, state, e));\n    mc.on('panend', () => endPan(chart, state));\n  }\n  hammers.set(chart, mc);\n}\nfunction stopHammer(chart) {\n  const mc = hammers.get(chart);\n  if (mc) {\n    mc.remove('pinchstart');\n    mc.remove('pinch');\n    mc.remove('pinchend');\n    mc.remove('panstart');\n    mc.remove('pan');\n    mc.remove('panend');\n    mc.destroy();\n    hammers.delete(chart);\n  }\n}\nfunction hammerOptionsChanged(oldOptions, newOptions) {\n  const {pan: oldPan, zoom: oldZoom} = oldOptions;\n  const {pan: newPan, zoom: newZoom} = newOptions;\n  if (oldZoom?.zoom?.pinch?.enabled !== newZoom?.zoom?.pinch?.enabled) {\n    return true;\n  }\n  if (oldPan?.enabled !== newPan?.enabled) {\n    return true;\n  }\n  if (oldPan?.threshold !== newPan?.threshold) {\n    return true;\n  }\n  return false;\n}\n\nvar version = \"2.2.0\";\n\nfunction draw(chart, caller, options) {\n  const dragOptions = options.zoom.drag;\n  const {dragStart, dragEnd} = getState(chart);\n  if (dragOptions.drawTime !== caller || !dragEnd) {\n    return;\n  }\n  const {left, top, width, height} = computeDragRect(chart, options.zoom.mode, {dragStart, dragEnd}, dragOptions.maintainAspectRatio);\n  const ctx = chart.ctx;\n  ctx.save();\n  ctx.beginPath();\n  ctx.fillStyle = dragOptions.backgroundColor || 'rgba(225,225,225,0.3)';\n  ctx.fillRect(left, top, width, height);\n  if (dragOptions.borderWidth > 0) {\n    ctx.lineWidth = dragOptions.borderWidth;\n    ctx.strokeStyle = dragOptions.borderColor || 'rgba(225,225,225)';\n    ctx.strokeRect(left, top, width, height);\n  }\n  ctx.restore();\n}\nvar plugin = {\n  id: 'zoom',\n  version,\n  defaults: {\n    pan: {\n      enabled: false,\n      mode: 'xy',\n      threshold: 10,\n      modifierKey: null,\n    },\n    zoom: {\n      wheel: {\n        enabled: false,\n        speed: 0.1,\n        modifierKey: null\n      },\n      drag: {\n        enabled: false,\n        drawTime: 'beforeDatasetsDraw',\n        modifierKey: null\n      },\n      pinch: {\n        enabled: false\n      },\n      mode: 'xy',\n    }\n  },\n  start: function(chart, _args, options) {\n    const state = getState(chart);\n    state.options = options;\n    if (Object.prototype.hasOwnProperty.call(options.zoom, 'enabled')) {\n      console.warn('The option `zoom.enabled` is no longer supported. Please use `zoom.wheel.enabled`, `zoom.drag.enabled`, or `zoom.pinch.enabled`.');\n    }\n    if (Object.prototype.hasOwnProperty.call(options.zoom, 'overScaleMode')\n      || Object.prototype.hasOwnProperty.call(options.pan, 'overScaleMode')) {\n      console.warn('The option `overScaleMode` is deprecated. Please use `scaleMode` instead (and update `mode` as desired).');\n    }\n    if (Hammer) {\n      startHammer(chart, options);\n    }\n    chart.pan = (delta, panScales, transition) => pan(chart, delta, panScales, transition);\n    chart.zoom = (args, transition) => zoom(chart, args, transition);\n    chart.zoomRect = (p0, p1, transition) => zoomRect(chart, p0, p1, transition);\n    chart.zoomScale = (id, range, transition) => zoomScale(chart, id, range, transition);\n    chart.resetZoom = (transition) => resetZoom(chart, transition);\n    chart.getZoomLevel = () => getZoomLevel(chart);\n    chart.getInitialScaleBounds = () => getInitialScaleBounds(chart);\n    chart.getZoomedScaleBounds = () => getZoomedScaleBounds(chart);\n    chart.isZoomedOrPanned = () => isZoomedOrPanned(chart);\n    chart.isZoomingOrPanning = () => isZoomingOrPanning(chart);\n  },\n  beforeEvent(chart, {event}) {\n    if (isZoomingOrPanning(chart)) {\n      return false;\n    }\n    if (event.type === 'click' || event.type === 'mouseup') {\n      const state = getState(chart);\n      if (state.filterNextClick) {\n        state.filterNextClick = false;\n        return false;\n      }\n    }\n  },\n  beforeUpdate: function(chart, args, options) {\n    const state = getState(chart);\n    const previousOptions = state.options;\n    state.options = options;\n    if (hammerOptionsChanged(previousOptions, options)) {\n      stopHammer(chart);\n      startHammer(chart, options);\n    }\n    addListeners(chart, options);\n  },\n  beforeDatasetsDraw(chart, _args, options) {\n    draw(chart, 'beforeDatasetsDraw', options);\n  },\n  afterDatasetsDraw(chart, _args, options) {\n    draw(chart, 'afterDatasetsDraw', options);\n  },\n  beforeDraw(chart, _args, options) {\n    draw(chart, 'beforeDraw', options);\n  },\n  afterDraw(chart, _args, options) {\n    draw(chart, 'afterDraw', options);\n  },\n  stop: function(chart) {\n    removeListeners(chart);\n    if (Hammer) {\n      stopHammer(chart);\n    }\n    removeState(chart);\n  },\n  panFunctions,\n  zoomFunctions,\n  zoomRectFunctions,\n};\n\nexport { plugin as default, pan, resetZoom, zoom, zoomRect, zoomScale };\n"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAKA,KAAC,SAASA,SAAQC,WAAU,YAAYC,YAAW;AACjD;AAEF,UAAI,kBAAkB,CAAC,IAAI,UAAU,OAAO,MAAM,MAAM,GAAG;AAC3D,UAAI,eAAeD,UAAS,cAAc,KAAK;AAE/C,UAAI,gBAAgB;AAEpB,UAAI,QAAQ,KAAK;AACjB,UAAI,MAAM,KAAK;AACf,UAAI,MAAM,KAAK;AASf,eAAS,kBAAkB,IAAI,SAAS,SAAS;AAC7C,eAAO,WAAW,OAAO,IAAI,OAAO,GAAG,OAAO;AAAA,MAClD;AAWA,eAAS,eAAe,KAAK,IAAI,SAAS;AACtC,YAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,UAAAE,MAAK,KAAK,QAAQ,EAAE,GAAG,OAAO;AAC9B,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAQA,eAASA,MAAK,KAAK,UAAU,SAAS;AAClC,YAAI;AAEJ,YAAI,CAAC,KAAK;AACN;AAAA,QACJ;AAEA,YAAI,IAAI,SAAS;AACb,cAAI,QAAQ,UAAU,OAAO;AAAA,QACjC,WAAW,IAAI,WAAWD,YAAW;AACjC,cAAI;AACJ,iBAAO,IAAI,IAAI,QAAQ;AACnB,qBAAS,KAAK,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG;AACrC;AAAA,UACJ;AAAA,QACJ,OAAO;AACH,eAAK,KAAK,KAAK;AACX,gBAAI,eAAe,CAAC,KAAK,SAAS,KAAK,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG;AAAA,UAClE;AAAA,QACJ;AAAA,MACJ;AASA,eAAS,UAAU,QAAQ,MAAM,SAAS;AACtC,YAAI,qBAAqB,wBAAwB,OAAO,OAAO,UAAU;AACzE,eAAO,WAAW;AACd,cAAI,IAAI,IAAI,MAAM,iBAAiB;AACnC,cAAI,QAAQ,KAAK,EAAE,QAAQ,EAAE,MAAM,QAAQ,mBAAmB,EAAE,EAC3D,QAAQ,eAAe,EAAE,EACzB,QAAQ,8BAA8B,gBAAgB,IAAI;AAE/D,cAAI,MAAMF,QAAO,YAAYA,QAAO,QAAQ,QAAQA,QAAO,QAAQ;AACnE,cAAI,KAAK;AACL,gBAAI,KAAKA,QAAO,SAAS,oBAAoB,KAAK;AAAA,UACtD;AACA,iBAAO,OAAO,MAAM,MAAM,SAAS;AAAA,QACvC;AAAA,MACJ;AASA,UAAI;AACJ,UAAI,OAAO,OAAO,WAAW,YAAY;AACrC,iBAAS,SAASI,QAAO,QAAQ;AAC7B,cAAI,WAAWF,cAAa,WAAW,MAAM;AACzC,kBAAM,IAAI,UAAU,4CAA4C;AAAA,UACpE;AAEA,cAAI,SAAS,OAAO,MAAM;AAC1B,mBAAS,QAAQ,GAAG,QAAQ,UAAU,QAAQ,SAAS;AACnD,gBAAI,SAAS,UAAU,KAAK;AAC5B,gBAAI,WAAWA,cAAa,WAAW,MAAM;AACzC,uBAAS,WAAW,QAAQ;AACxB,oBAAI,OAAO,eAAe,OAAO,GAAG;AAChC,yBAAO,OAAO,IAAI,OAAO,OAAO;AAAA,gBACpC;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,MACJ,OAAO;AACH,iBAAS,OAAO;AAAA,MACpB;AAUA,UAAI,SAAS,UAAU,SAASG,QAAO,MAAM,KAAKC,QAAO;AACrD,YAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,YAAI,IAAI;AACR,eAAO,IAAI,KAAK,QAAQ;AACpB,cAAI,CAACA,UAAUA,UAAS,KAAK,KAAK,CAAC,CAAC,MAAMJ,YAAY;AAClD,iBAAK,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;AAAA,UAC/B;AACA;AAAA,QACJ;AACA,eAAO;AAAA,MACX,GAAG,UAAU,eAAe;AAS5B,UAAII,SAAQ,UAAU,SAASA,OAAM,MAAM,KAAK;AAC5C,eAAO,OAAO,MAAM,KAAK,IAAI;AAAA,MACjC,GAAG,SAAS,eAAe;AAQ3B,eAAS,QAAQ,OAAO,MAAM,YAAY;AACtC,YAAI,QAAQ,KAAK,WACb;AAEJ,iBAAS,MAAM,YAAY,OAAO,OAAO,KAAK;AAC9C,eAAO,cAAc;AACrB,eAAO,SAAS;AAEhB,YAAI,YAAY;AACZ,iBAAO,QAAQ,UAAU;AAAA,QAC7B;AAAA,MACJ;AAQA,eAAS,OAAO,IAAI,SAAS;AACzB,eAAO,SAAS,UAAU;AACtB,iBAAO,GAAG,MAAM,SAAS,SAAS;AAAA,QACtC;AAAA,MACJ;AASA,eAAS,SAAS,KAAK,MAAM;AACzB,YAAI,OAAO,OAAO,eAAe;AAC7B,iBAAO,IAAI,MAAM,OAAO,KAAK,CAAC,KAAKJ,aAAYA,YAAW,IAAI;AAAA,QAClE;AACA,eAAO;AAAA,MACX;AAQA,eAAS,YAAY,MAAM,MAAM;AAC7B,eAAQ,SAASA,aAAa,OAAO;AAAA,MACzC;AAQA,eAAS,kBAAkB,QAAQ,OAAO,SAAS;AAC/C,QAAAC,MAAK,SAAS,KAAK,GAAG,SAAS,MAAM;AACjC,iBAAO,iBAAiB,MAAM,SAAS,KAAK;AAAA,QAChD,CAAC;AAAA,MACL;AAQA,eAAS,qBAAqB,QAAQ,OAAO,SAAS;AAClD,QAAAA,MAAK,SAAS,KAAK,GAAG,SAAS,MAAM;AACjC,iBAAO,oBAAoB,MAAM,SAAS,KAAK;AAAA,QACnD,CAAC;AAAA,MACL;AASA,eAAS,UAAU,MAAM,QAAQ;AAC7B,eAAO,MAAM;AACT,cAAI,QAAQ,QAAQ;AAChB,mBAAO;AAAA,UACX;AACA,iBAAO,KAAK;AAAA,QAChB;AACA,eAAO;AAAA,MACX;AAQA,eAAS,MAAM,KAAK,MAAM;AACtB,eAAO,IAAI,QAAQ,IAAI,IAAI;AAAA,MAC/B;AAOA,eAAS,SAAS,KAAK;AACnB,eAAO,IAAI,KAAK,EAAE,MAAM,MAAM;AAAA,MAClC;AASA,eAAS,QAAQ,KAAK,MAAM,WAAW;AACnC,YAAI,IAAI,WAAW,CAAC,WAAW;AAC3B,iBAAO,IAAI,QAAQ,IAAI;AAAA,QAC3B,OAAO;AACH,cAAI,IAAI;AACR,iBAAO,IAAI,IAAI,QAAQ;AACnB,gBAAK,aAAa,IAAI,CAAC,EAAE,SAAS,KAAK,QAAU,CAAC,aAAa,IAAI,CAAC,MAAM,MAAO;AAC7E,qBAAO;AAAA,YACX;AACA;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AAOA,eAAS,QAAQ,KAAK;AAClB,eAAO,MAAM,UAAU,MAAM,KAAK,KAAK,CAAC;AAAA,MAC5C;AASA,eAAS,YAAY,KAAK,KAAK,MAAM;AACjC,YAAI,UAAU,CAAC;AACf,YAAI,SAAS,CAAC;AACd,YAAI,IAAI;AAER,eAAO,IAAI,IAAI,QAAQ;AACnB,cAAI,MAAM,MAAM,IAAI,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC;AACnC,cAAI,QAAQ,QAAQ,GAAG,IAAI,GAAG;AAC1B,oBAAQ,KAAK,IAAI,CAAC,CAAC;AAAA,UACvB;AACA,iBAAO,CAAC,IAAI;AACZ;AAAA,QACJ;AAEA,YAAI,MAAM;AACN,cAAI,CAAC,KAAK;AACN,sBAAU,QAAQ,KAAK;AAAA,UAC3B,OAAO;AACH,sBAAU,QAAQ,KAAK,SAAS,gBAAgB,GAAG,GAAG;AAClD,qBAAO,EAAE,GAAG,IAAI,EAAE,GAAG;AAAA,YACzB,CAAC;AAAA,UACL;AAAA,QACJ;AAEA,eAAO;AAAA,MACX;AAQA,eAAS,SAAS,KAAK,UAAU;AAC7B,YAAI,QAAQ;AACZ,YAAI,YAAY,SAAS,CAAC,EAAE,YAAY,IAAI,SAAS,MAAM,CAAC;AAE5D,YAAI,IAAI;AACR,eAAO,IAAI,gBAAgB,QAAQ;AAC/B,mBAAS,gBAAgB,CAAC;AAC1B,iBAAQ,SAAU,SAAS,YAAY;AAEvC,cAAI,QAAQ,KAAK;AACb,mBAAO;AAAA,UACX;AACA;AAAA,QACJ;AACA,eAAOD;AAAA,MACX;AAMA,UAAI,YAAY;AAChB,eAAS,WAAW;AAChB,eAAO;AAAA,MACX;AAOA,eAAS,oBAAoB,SAAS;AAClC,YAAI,MAAM,QAAQ,iBAAiB;AACnC,eAAQ,IAAI,eAAe,IAAI,gBAAgBF;AAAA,MACnD;AAEA,UAAI,eAAe;AAEnB,UAAI,gBAAiB,kBAAkBA;AACvC,UAAI,yBAAyB,SAASA,SAAQ,cAAc,MAAME;AAClE,UAAI,qBAAqB,iBAAiB,aAAa,KAAK,UAAU,SAAS;AAE/E,UAAI,mBAAmB;AACvB,UAAI,iBAAiB;AACrB,UAAI,mBAAmB;AACvB,UAAI,oBAAoB;AAExB,UAAI,mBAAmB;AAEvB,UAAI,cAAc;AAClB,UAAI,aAAa;AACjB,UAAI,YAAY;AAChB,UAAI,eAAe;AAEnB,UAAI,iBAAiB;AACrB,UAAI,iBAAiB;AACrB,UAAI,kBAAkB;AACtB,UAAI,eAAe;AACnB,UAAI,iBAAiB;AAErB,UAAI,uBAAuB,iBAAiB;AAC5C,UAAI,qBAAqB,eAAe;AACxC,UAAI,gBAAgB,uBAAuB;AAE3C,UAAI,WAAW,CAAC,KAAK,GAAG;AACxB,UAAI,kBAAkB,CAAC,WAAW,SAAS;AAS3C,eAAS,MAAM,SAASK,WAAU;AAC9B,YAAIC,QAAO;AACX,aAAK,UAAU;AACf,aAAK,WAAWD;AAChB,aAAK,UAAU,QAAQ;AACvB,aAAK,SAAS,QAAQ,QAAQ;AAI9B,aAAK,aAAa,SAAS,IAAI;AAC3B,cAAI,SAAS,QAAQ,QAAQ,QAAQ,CAAC,OAAO,CAAC,GAAG;AAC7C,YAAAC,MAAK,QAAQ,EAAE;AAAA,UACnB;AAAA,QACJ;AAEA,aAAK,KAAK;AAAA,MAEd;AAEA,YAAM,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,QAKd,SAAS,WAAW;AAAA,QAAE;AAAA;AAAA;AAAA;AAAA,QAKtB,MAAM,WAAW;AACb,eAAK,QAAQ,kBAAkB,KAAK,SAAS,KAAK,MAAM,KAAK,UAAU;AACvE,eAAK,YAAY,kBAAkB,KAAK,QAAQ,KAAK,UAAU,KAAK,UAAU;AAC9E,eAAK,SAAS,kBAAkB,oBAAoB,KAAK,OAAO,GAAG,KAAK,OAAO,KAAK,UAAU;AAAA,QAClG;AAAA;AAAA;AAAA;AAAA,QAKA,SAAS,WAAW;AAChB,eAAK,QAAQ,qBAAqB,KAAK,SAAS,KAAK,MAAM,KAAK,UAAU;AAC1E,eAAK,YAAY,qBAAqB,KAAK,QAAQ,KAAK,UAAU,KAAK,UAAU;AACjF,eAAK,SAAS,qBAAqB,oBAAoB,KAAK,OAAO,GAAG,KAAK,OAAO,KAAK,UAAU;AAAA,QACrG;AAAA,MACJ;AAQA,eAAS,oBAAoB,SAAS;AAClC,YAAI;AACJ,YAAI,aAAa,QAAQ,QAAQ;AAEjC,YAAI,YAAY;AACZ,iBAAO;AAAA,QACX,WAAW,wBAAwB;AAC/B,iBAAO;AAAA,QACX,WAAW,oBAAoB;AAC3B,iBAAO;AAAA,QACX,WAAW,CAAC,eAAe;AACvB,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO;AAAA,QACX;AACA,eAAO,IAAK,KAAM,SAAS,YAAY;AAAA,MAC3C;AAQA,eAAS,aAAa,SAAS,WAAW,OAAO;AAC7C,YAAI,cAAc,MAAM,SAAS;AACjC,YAAI,qBAAqB,MAAM,gBAAgB;AAC/C,YAAI,UAAW,YAAY,eAAgB,cAAc,uBAAuB;AAChF,YAAI,UAAW,aAAa,YAAY,iBAAkB,cAAc,uBAAuB;AAE/F,cAAM,UAAU,CAAC,CAAC;AAClB,cAAM,UAAU,CAAC,CAAC;AAElB,YAAI,SAAS;AACT,kBAAQ,UAAU,CAAC;AAAA,QACvB;AAIA,cAAM,YAAY;AAGlB,yBAAiB,SAAS,KAAK;AAG/B,gBAAQ,KAAK,gBAAgB,KAAK;AAElC,gBAAQ,UAAU,KAAK;AACvB,gBAAQ,QAAQ,YAAY;AAAA,MAChC;AAOA,eAAS,iBAAiB,SAAS,OAAO;AACtC,YAAI,UAAU,QAAQ;AACtB,YAAI,WAAW,MAAM;AACrB,YAAI,iBAAiB,SAAS;AAG9B,YAAI,CAAC,QAAQ,YAAY;AACrB,kBAAQ,aAAa,qBAAqB,KAAK;AAAA,QACnD;AAGA,YAAI,iBAAiB,KAAK,CAAC,QAAQ,eAAe;AAC9C,kBAAQ,gBAAgB,qBAAqB,KAAK;AAAA,QACtD,WAAW,mBAAmB,GAAG;AAC7B,kBAAQ,gBAAgB;AAAA,QAC5B;AAEA,YAAI,aAAa,QAAQ;AACzB,YAAI,gBAAgB,QAAQ;AAC5B,YAAI,eAAe,gBAAgB,cAAc,SAAS,WAAW;AAErE,YAAI,SAAS,MAAM,SAASC,WAAU,QAAQ;AAC9C,cAAM,YAAY,IAAI;AACtB,cAAM,YAAY,MAAM,YAAY,WAAW;AAE/C,cAAM,QAAQ,SAAS,cAAc,MAAM;AAC3C,cAAM,WAAW,YAAY,cAAc,MAAM;AAEjD,uBAAe,SAAS,KAAK;AAC7B,cAAM,kBAAkB,aAAa,MAAM,QAAQ,MAAM,MAAM;AAE/D,YAAI,kBAAkB,YAAY,MAAM,WAAW,MAAM,QAAQ,MAAM,MAAM;AAC7E,cAAM,mBAAmB,gBAAgB;AACzC,cAAM,mBAAmB,gBAAgB;AACzC,cAAM,kBAAmB,IAAI,gBAAgB,CAAC,IAAI,IAAI,gBAAgB,CAAC,IAAK,gBAAgB,IAAI,gBAAgB;AAEhH,cAAM,QAAQ,gBAAgB,SAAS,cAAc,UAAU,QAAQ,IAAI;AAC3E,cAAM,WAAW,gBAAgB,YAAY,cAAc,UAAU,QAAQ,IAAI;AAEjF,cAAM,cAAc,CAAC,QAAQ,YAAY,MAAM,SAAS,SAAW,MAAM,SAAS,SAC9E,QAAQ,UAAU,cAAe,MAAM,SAAS,SAAS,QAAQ,UAAU;AAE/E,iCAAyB,SAAS,KAAK;AAGvC,YAAI,SAAS,QAAQ;AACrB,YAAI,UAAU,MAAM,SAAS,QAAQ,MAAM,GAAG;AAC1C,mBAAS,MAAM,SAAS;AAAA,QAC5B;AACA,cAAM,SAAS;AAAA,MACnB;AAEA,eAAS,eAAe,SAAS,OAAO;AACpC,YAAI,SAAS,MAAM;AACnB,YAAI,SAAS,QAAQ,eAAe,CAAC;AACrC,YAAI,YAAY,QAAQ,aAAa,CAAC;AACtC,YAAI,YAAY,QAAQ,aAAa,CAAC;AAEtC,YAAI,MAAM,cAAc,eAAe,UAAU,cAAc,WAAW;AACtE,sBAAY,QAAQ,YAAY;AAAA,YAC5B,GAAG,UAAU,UAAU;AAAA,YACvB,GAAG,UAAU,UAAU;AAAA,UAC3B;AAEA,mBAAS,QAAQ,cAAc;AAAA,YAC3B,GAAG,OAAO;AAAA,YACV,GAAG,OAAO;AAAA,UACd;AAAA,QACJ;AAEA,cAAM,SAAS,UAAU,KAAK,OAAO,IAAI,OAAO;AAChD,cAAM,SAAS,UAAU,KAAK,OAAO,IAAI,OAAO;AAAA,MACpD;AAOA,eAAS,yBAAyB,SAAS,OAAO;AAC9C,YAAI,OAAO,QAAQ,gBAAgB,OAC/B,YAAY,MAAM,YAAY,KAAK,WACnC,UAAU,WAAW,WAAW;AAEpC,YAAI,MAAM,aAAa,iBAAiB,YAAY,oBAAoB,KAAK,aAAaP,aAAY;AAClG,cAAI,SAAS,MAAM,SAAS,KAAK;AACjC,cAAI,SAAS,MAAM,SAAS,KAAK;AAEjC,cAAI,IAAI,YAAY,WAAW,QAAQ,MAAM;AAC7C,sBAAY,EAAE;AACd,sBAAY,EAAE;AACd,qBAAY,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAK,EAAE,IAAI,EAAE;AAC3C,sBAAY,aAAa,QAAQ,MAAM;AAEvC,kBAAQ,eAAe;AAAA,QAC3B,OAAO;AAEH,qBAAW,KAAK;AAChB,sBAAY,KAAK;AACjB,sBAAY,KAAK;AACjB,sBAAY,KAAK;AAAA,QACrB;AAEA,cAAM,WAAW;AACjB,cAAM,YAAY;AAClB,cAAM,YAAY;AAClB,cAAM,YAAY;AAAA,MACtB;AAOA,eAAS,qBAAqB,OAAO;AAGjC,YAAI,WAAW,CAAC;AAChB,YAAI,IAAI;AACR,eAAO,IAAI,MAAM,SAAS,QAAQ;AAC9B,mBAAS,CAAC,IAAI;AAAA,YACV,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,OAAO;AAAA,YACxC,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,OAAO;AAAA,UAC5C;AACA;AAAA,QACJ;AAEA,eAAO;AAAA,UACH,WAAW,IAAI;AAAA,UACf;AAAA,UACA,QAAQO,WAAU,QAAQ;AAAA,UAC1B,QAAQ,MAAM;AAAA,UACd,QAAQ,MAAM;AAAA,QAClB;AAAA,MACJ;AAOA,eAASA,WAAU,UAAU;AACzB,YAAI,iBAAiB,SAAS;AAG9B,YAAI,mBAAmB,GAAG;AACtB,iBAAO;AAAA,YACH,GAAG,MAAM,SAAS,CAAC,EAAE,OAAO;AAAA,YAC5B,GAAG,MAAM,SAAS,CAAC,EAAE,OAAO;AAAA,UAChC;AAAA,QACJ;AAEA,YAAI,IAAI,GAAG,IAAI,GAAG,IAAI;AACtB,eAAO,IAAI,gBAAgB;AACvB,eAAK,SAAS,CAAC,EAAE;AACjB,eAAK,SAAS,CAAC,EAAE;AACjB;AAAA,QACJ;AAEA,eAAO;AAAA,UACH,GAAG,MAAM,IAAI,cAAc;AAAA,UAC3B,GAAG,MAAM,IAAI,cAAc;AAAA,QAC/B;AAAA,MACJ;AASA,eAAS,YAAY,WAAW,GAAG,GAAG;AAClC,eAAO;AAAA,UACH,GAAG,IAAI,aAAa;AAAA,UACpB,GAAG,IAAI,aAAa;AAAA,QACxB;AAAA,MACJ;AAQA,eAAS,aAAa,GAAG,GAAG;AACxB,YAAI,MAAM,GAAG;AACT,iBAAO;AAAA,QACX;AAEA,YAAI,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAClB,iBAAO,IAAI,IAAI,iBAAiB;AAAA,QACpC;AACA,eAAO,IAAI,IAAI,eAAe;AAAA,MAClC;AASA,eAAS,YAAY,IAAI,IAAI,OAAO;AAChC,YAAI,CAAC,OAAO;AACR,kBAAQ;AAAA,QACZ;AACA,YAAI,IAAI,GAAG,MAAM,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,GAC9B,IAAI,GAAG,MAAM,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;AAElC,eAAO,KAAK,KAAM,IAAI,IAAM,IAAI,CAAE;AAAA,MACtC;AASA,eAAS,SAAS,IAAI,IAAI,OAAO;AAC7B,YAAI,CAAC,OAAO;AACR,kBAAQ;AAAA,QACZ;AACA,YAAI,IAAI,GAAG,MAAM,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,GAC9B,IAAI,GAAG,MAAM,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;AAClC,eAAO,KAAK,MAAM,GAAG,CAAC,IAAI,MAAM,KAAK;AAAA,MACzC;AAQA,eAAS,YAAY,OAAO,KAAK;AAC7B,eAAO,SAAS,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,eAAe,IAAI,SAAS,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,eAAe;AAAA,MACnG;AASA,eAAS,SAAS,OAAO,KAAK;AAC1B,eAAO,YAAY,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,eAAe,IAAI,YAAY,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,eAAe;AAAA,MACzG;AAEA,UAAI,kBAAkB;AAAA,QAClB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,SAAS;AAAA,MACb;AAEA,UAAI,uBAAuB;AAC3B,UAAI,sBAAsB;AAO1B,eAAS,aAAa;AAClB,aAAK,OAAO;AACZ,aAAK,QAAQ;AAEb,aAAK,UAAU;AAEf,cAAM,MAAM,MAAM,SAAS;AAAA,MAC/B;AAEA,cAAQ,YAAY,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,QAKvB,SAAS,SAAS,UAAU,IAAI;AAC5B,cAAI,YAAY,gBAAgB,GAAG,IAAI;AAGvC,cAAI,YAAY,eAAe,GAAG,WAAW,GAAG;AAC5C,iBAAK,UAAU;AAAA,UACnB;AAEA,cAAI,YAAY,cAAc,GAAG,UAAU,GAAG;AAC1C,wBAAY;AAAA,UAChB;AAGA,cAAI,CAAC,KAAK,SAAS;AACf;AAAA,UACJ;AAEA,cAAI,YAAY,WAAW;AACvB,iBAAK,UAAU;AAAA,UACnB;AAEA,eAAK,SAAS,KAAK,SAAS,WAAW;AAAA,YACnC,UAAU,CAAC,EAAE;AAAA,YACb,iBAAiB,CAAC,EAAE;AAAA,YACpB,aAAa;AAAA,YACb,UAAU;AAAA,UACd,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAED,UAAI,oBAAoB;AAAA,QACpB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,QACX,eAAe;AAAA,QACf,YAAY;AAAA,MAChB;AAGA,UAAI,yBAAyB;AAAA,QACzB,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA;AAAA,MACP;AAEA,UAAI,yBAAyB;AAC7B,UAAI,wBAAwB;AAG5B,UAAIT,QAAO,kBAAkB,CAACA,QAAO,cAAc;AAC/C,iCAAyB;AACzB,gCAAwB;AAAA,MAC5B;AAOA,eAAS,oBAAoB;AACzB,aAAK,OAAO;AACZ,aAAK,QAAQ;AAEb,cAAM,MAAM,MAAM,SAAS;AAE3B,aAAK,QAAS,KAAK,QAAQ,QAAQ,gBAAgB,CAAC;AAAA,MACxD;AAEA,cAAQ,mBAAmB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,QAK9B,SAAS,SAAS,UAAU,IAAI;AAC5B,cAAI,QAAQ,KAAK;AACjB,cAAI,gBAAgB;AAEpB,cAAI,sBAAsB,GAAG,KAAK,YAAY,EAAE,QAAQ,MAAM,EAAE;AAChE,cAAI,YAAY,kBAAkB,mBAAmB;AACrD,cAAI,cAAc,uBAAuB,GAAG,WAAW,KAAK,GAAG;AAE/D,cAAI,UAAW,eAAe;AAG9B,cAAI,aAAa,QAAQ,OAAO,GAAG,WAAW,WAAW;AAGzD,cAAI,YAAY,gBAAgB,GAAG,WAAW,KAAK,UAAU;AACzD,gBAAI,aAAa,GAAG;AAChB,oBAAM,KAAK,EAAE;AACb,2BAAa,MAAM,SAAS;AAAA,YAChC;AAAA,UACJ,WAAW,aAAa,YAAY,eAAe;AAC/C,4BAAgB;AAAA,UACpB;AAGA,cAAI,aAAa,GAAG;AAChB;AAAA,UACJ;AAGA,gBAAM,UAAU,IAAI;AAEpB,eAAK,SAAS,KAAK,SAAS,WAAW;AAAA,YACnC,UAAU;AAAA,YACV,iBAAiB,CAAC,EAAE;AAAA,YACpB;AAAA,YACA,UAAU;AAAA,UACd,CAAC;AAED,cAAI,eAAe;AAEf,kBAAM,OAAO,YAAY,CAAC;AAAA,UAC9B;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,UAAI,yBAAyB;AAAA,QACzB,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,aAAa;AAAA,MACjB;AAEA,UAAI,6BAA6B;AACjC,UAAI,6BAA6B;AAOjC,eAAS,mBAAmB;AACxB,aAAK,WAAW;AAChB,aAAK,QAAQ;AACb,aAAK,UAAU;AAEf,cAAM,MAAM,MAAM,SAAS;AAAA,MAC/B;AAEA,cAAQ,kBAAkB,OAAO;AAAA,QAC7B,SAAS,SAAS,UAAU,IAAI;AAC5B,cAAI,OAAO,uBAAuB,GAAG,IAAI;AAGzC,cAAI,SAAS,aAAa;AACtB,iBAAK,UAAU;AAAA,UACnB;AAEA,cAAI,CAAC,KAAK,SAAS;AACf;AAAA,UACJ;AAEA,cAAI,UAAU,uBAAuB,KAAK,MAAM,IAAI,IAAI;AAGxD,cAAI,QAAQ,YAAY,iBAAiB,QAAQ,CAAC,EAAE,SAAS,QAAQ,CAAC,EAAE,WAAW,GAAG;AAClF,iBAAK,UAAU;AAAA,UACnB;AAEA,eAAK,SAAS,KAAK,SAAS,MAAM;AAAA,YAC9B,UAAU,QAAQ,CAAC;AAAA,YACnB,iBAAiB,QAAQ,CAAC;AAAA,YAC1B,aAAa;AAAA,YACb,UAAU;AAAA,UACd,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAQD,eAAS,uBAAuB,IAAI,MAAM;AACtC,YAAI,MAAM,QAAQ,GAAG,OAAO;AAC5B,YAAI,UAAU,QAAQ,GAAG,cAAc;AAEvC,YAAI,QAAQ,YAAY,eAAe;AACnC,gBAAM,YAAY,IAAI,OAAO,OAAO,GAAG,cAAc,IAAI;AAAA,QAC7D;AAEA,eAAO,CAAC,KAAK,OAAO;AAAA,MACxB;AAEA,UAAI,kBAAkB;AAAA,QAClB,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,aAAa;AAAA,MACjB;AAEA,UAAI,sBAAsB;AAO1B,eAAS,aAAa;AAClB,aAAK,WAAW;AAChB,aAAK,YAAY,CAAC;AAElB,cAAM,MAAM,MAAM,SAAS;AAAA,MAC/B;AAEA,cAAQ,YAAY,OAAO;AAAA,QACvB,SAAS,SAAS,WAAW,IAAI;AAC7B,cAAI,OAAO,gBAAgB,GAAG,IAAI;AAClC,cAAI,UAAU,WAAW,KAAK,MAAM,IAAI,IAAI;AAC5C,cAAI,CAAC,SAAS;AACV;AAAA,UACJ;AAEA,eAAK,SAAS,KAAK,SAAS,MAAM;AAAA,YAC9B,UAAU,QAAQ,CAAC;AAAA,YACnB,iBAAiB,QAAQ,CAAC;AAAA,YAC1B,aAAa;AAAA,YACb,UAAU;AAAA,UACd,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAQD,eAAS,WAAW,IAAI,MAAM;AAC1B,YAAI,aAAa,QAAQ,GAAG,OAAO;AACnC,YAAI,YAAY,KAAK;AAGrB,YAAI,QAAQ,cAAc,eAAe,WAAW,WAAW,GAAG;AAC9D,oBAAU,WAAW,CAAC,EAAE,UAAU,IAAI;AACtC,iBAAO,CAAC,YAAY,UAAU;AAAA,QAClC;AAEA,YAAI,GACA,eACA,iBAAiB,QAAQ,GAAG,cAAc,GAC1C,uBAAuB,CAAC,GACxB,SAAS,KAAK;AAGlB,wBAAgB,WAAW,OAAO,SAAS,OAAO;AAC9C,iBAAO,UAAU,MAAM,QAAQ,MAAM;AAAA,QACzC,CAAC;AAGD,YAAI,SAAS,aAAa;AACtB,cAAI;AACJ,iBAAO,IAAI,cAAc,QAAQ;AAC7B,sBAAU,cAAc,CAAC,EAAE,UAAU,IAAI;AACzC;AAAA,UACJ;AAAA,QACJ;AAGA,YAAI;AACJ,eAAO,IAAI,eAAe,QAAQ;AAC9B,cAAI,UAAU,eAAe,CAAC,EAAE,UAAU,GAAG;AACzC,iCAAqB,KAAK,eAAe,CAAC,CAAC;AAAA,UAC/C;AAGA,cAAI,QAAQ,YAAY,eAAe;AACnC,mBAAO,UAAU,eAAe,CAAC,EAAE,UAAU;AAAA,UACjD;AACA;AAAA,QACJ;AAEA,YAAI,CAAC,qBAAqB,QAAQ;AAC9B;AAAA,QACJ;AAEA,eAAO;AAAA;AAAA,UAEH,YAAY,cAAc,OAAO,oBAAoB,GAAG,cAAc,IAAI;AAAA,UAC1E;AAAA,QACJ;AAAA,MACJ;AAYA,UAAI,gBAAgB;AACpB,UAAI,iBAAiB;AAErB,eAAS,kBAAkB;AACvB,cAAM,MAAM,MAAM,SAAS;AAE3B,YAAI,UAAU,OAAO,KAAK,SAAS,IAAI;AACvC,aAAK,QAAQ,IAAI,WAAW,KAAK,SAAS,OAAO;AACjD,aAAK,QAAQ,IAAI,WAAW,KAAK,SAAS,OAAO;AAEjD,aAAK,eAAe;AACpB,aAAK,cAAc,CAAC;AAAA,MACxB;AAEA,cAAQ,iBAAiB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAO5B,SAAS,SAAS,WAAW,SAAS,YAAY,WAAW;AACzD,cAAI,UAAW,UAAU,eAAe,kBACpC,UAAW,UAAU,eAAe;AAExC,cAAI,WAAW,UAAU,sBAAsB,UAAU,mBAAmB,kBAAkB;AAC1F;AAAA,UACJ;AAGA,cAAI,SAAS;AACT,0BAAc,KAAK,MAAM,YAAY,SAAS;AAAA,UAClD,WAAW,WAAW,iBAAiB,KAAK,MAAM,SAAS,GAAG;AAC1D;AAAA,UACJ;AAEA,eAAK,SAAS,SAAS,YAAY,SAAS;AAAA,QAChD;AAAA;AAAA;AAAA;AAAA,QAKA,SAAS,SAAS,UAAU;AACxB,eAAK,MAAM,QAAQ;AACnB,eAAK,MAAM,QAAQ;AAAA,QACvB;AAAA,MACJ,CAAC;AAED,eAAS,cAAc,WAAW,WAAW;AACzC,YAAI,YAAY,aAAa;AACzB,eAAK,eAAe,UAAU,gBAAgB,CAAC,EAAE;AACjD,uBAAa,KAAK,MAAM,SAAS;AAAA,QACrC,WAAW,aAAa,YAAY,eAAe;AAC/C,uBAAa,KAAK,MAAM,SAAS;AAAA,QACrC;AAAA,MACJ;AAEA,eAAS,aAAa,WAAW;AAC7B,YAAI,QAAQ,UAAU,gBAAgB,CAAC;AAEvC,YAAI,MAAM,eAAe,KAAK,cAAc;AACxC,cAAI,YAAY,EAAC,GAAG,MAAM,SAAS,GAAG,MAAM,QAAO;AACnD,eAAK,YAAY,KAAK,SAAS;AAC/B,cAAI,MAAM,KAAK;AACf,cAAI,kBAAkB,WAAW;AAC7B,gBAAI,IAAI,IAAI,QAAQ,SAAS;AAC7B,gBAAI,IAAI,IAAI;AACR,kBAAI,OAAO,GAAG,CAAC;AAAA,YACnB;AAAA,UACJ;AACA,qBAAW,iBAAiB,aAAa;AAAA,QAC7C;AAAA,MACJ;AAEA,eAAS,iBAAiB,WAAW;AACjC,YAAI,IAAI,UAAU,SAAS,SAAS,IAAI,UAAU,SAAS;AAC3D,iBAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAC9C,cAAI,IAAI,KAAK,YAAY,CAAC;AAC1B,cAAI,KAAK,KAAK,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,KAAK,IAAI,IAAI,EAAE,CAAC;AACjD,cAAI,MAAM,kBAAkB,MAAM,gBAAgB;AAC9C,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAEA,UAAI,wBAAwB,SAAS,aAAa,OAAO,aAAa;AACtE,UAAI,sBAAsB,0BAA0BE;AAGpD,UAAI,uBAAuB;AAC3B,UAAI,oBAAoB;AACxB,UAAI,4BAA4B;AAChC,UAAI,oBAAoB;AACxB,UAAI,qBAAqB;AACzB,UAAI,qBAAqB;AACzB,UAAI,mBAAmB,oBAAoB;AAS3C,eAAS,YAAY,SAAS,OAAO;AACjC,aAAK,UAAU;AACf,aAAK,IAAI,KAAK;AAAA,MAClB;AAEA,kBAAY,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,QAKpB,KAAK,SAAS,OAAO;AAEjB,cAAI,SAAS,sBAAsB;AAC/B,oBAAQ,KAAK,QAAQ;AAAA,UACzB;AAEA,cAAI,uBAAuB,KAAK,QAAQ,QAAQ,SAAS,iBAAiB,KAAK,GAAG;AAC9E,iBAAK,QAAQ,QAAQ,MAAM,qBAAqB,IAAI;AAAA,UACxD;AACA,eAAK,UAAU,MAAM,YAAY,EAAE,KAAK;AAAA,QAC5C;AAAA;AAAA;AAAA;AAAA,QAKA,QAAQ,WAAW;AACf,eAAK,IAAI,KAAK,QAAQ,QAAQ,WAAW;AAAA,QAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,SAAS,WAAW;AAChB,cAAI,UAAU,CAAC;AACf,UAAAC,MAAK,KAAK,QAAQ,aAAa,SAAS,YAAY;AAChD,gBAAI,SAAS,WAAW,QAAQ,QAAQ,CAAC,UAAU,CAAC,GAAG;AACnD,wBAAU,QAAQ,OAAO,WAAW,eAAe,CAAC;AAAA,YACxD;AAAA,UACJ,CAAC;AACD,iBAAO,kBAAkB,QAAQ,KAAK,GAAG,CAAC;AAAA,QAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,iBAAiB,SAAS,OAAO;AAC7B,cAAI,WAAW,MAAM;AACrB,cAAI,YAAY,MAAM;AAGtB,cAAI,KAAK,QAAQ,QAAQ,WAAW;AAChC,qBAAS,eAAe;AACxB;AAAA,UACJ;AAEA,cAAI,UAAU,KAAK;AACnB,cAAI,UAAU,MAAM,SAAS,iBAAiB,KAAK,CAAC,iBAAiB,iBAAiB;AACtF,cAAI,UAAU,MAAM,SAAS,kBAAkB,KAAK,CAAC,iBAAiB,kBAAkB;AACxF,cAAI,UAAU,MAAM,SAAS,kBAAkB,KAAK,CAAC,iBAAiB,kBAAkB;AAExF,cAAI,SAAS;AAGT,gBAAI,eAAe,MAAM,SAAS,WAAW;AAC7C,gBAAI,gBAAgB,MAAM,WAAW;AACrC,gBAAI,iBAAiB,MAAM,YAAY;AAEvC,gBAAI,gBAAgB,iBAAiB,gBAAgB;AACjD;AAAA,YACJ;AAAA,UACJ;AAEA,cAAI,WAAW,SAAS;AAEpB;AAAA,UACJ;AAEA,cAAI,WACC,WAAW,YAAY,wBACvB,WAAW,YAAY,oBAAqB;AAC7C,mBAAO,KAAK,WAAW,QAAQ;AAAA,UACnC;AAAA,QACJ;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,YAAY,SAAS,UAAU;AAC3B,eAAK,QAAQ,QAAQ,YAAY;AACjC,mBAAS,eAAe;AAAA,QAC5B;AAAA,MACJ;AAOA,eAAS,kBAAkB,SAAS;AAEhC,YAAI,MAAM,SAAS,iBAAiB,GAAG;AACnC,iBAAO;AAAA,QACX;AAEA,YAAI,UAAU,MAAM,SAAS,kBAAkB;AAC/C,YAAI,UAAU,MAAM,SAAS,kBAAkB;AAM/C,YAAI,WAAW,SAAS;AACpB,iBAAO;AAAA,QACX;AAGA,YAAI,WAAW,SAAS;AACpB,iBAAO,UAAU,qBAAqB;AAAA,QAC1C;AAGA,YAAI,MAAM,SAAS,yBAAyB,GAAG;AAC3C,iBAAO;AAAA,QACX;AAEA,eAAO;AAAA,MACX;AAEA,eAAS,sBAAsB;AAC3B,YAAI,CAAC,qBAAqB;AACtB,iBAAO;AAAA,QACX;AACA,YAAI,WAAW,CAAC;AAChB,YAAI,cAAcH,QAAO,OAAOA,QAAO,IAAI;AAC3C,SAAC,QAAQ,gBAAgB,SAAS,SAAS,eAAe,MAAM,EAAE,QAAQ,SAAS,KAAK;AAIpF,mBAAS,GAAG,IAAI,cAAcA,QAAO,IAAI,SAAS,gBAAgB,GAAG,IAAI;AAAA,QAC7E,CAAC;AACD,eAAO;AAAA,MACX;AA6BA,UAAI,iBAAiB;AACrB,UAAI,cAAc;AAClB,UAAI,gBAAgB;AACpB,UAAI,cAAc;AAClB,UAAI,mBAAmB;AACvB,UAAI,kBAAkB;AACtB,UAAI,eAAe;AAQnB,eAAS,WAAW,SAAS;AACzB,aAAK,UAAU,OAAO,CAAC,GAAG,KAAK,UAAU,WAAW,CAAC,CAAC;AAEtD,aAAK,KAAK,SAAS;AAEnB,aAAK,UAAU;AAGf,aAAK,QAAQ,SAAS,YAAY,KAAK,QAAQ,QAAQ,IAAI;AAE3D,aAAK,QAAQ;AAEb,aAAK,eAAe,CAAC;AACrB,aAAK,cAAc,CAAC;AAAA,MACxB;AAEA,iBAAW,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,QAKnB,UAAU,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOX,KAAK,SAAS,SAAS;AACnB,iBAAO,KAAK,SAAS,OAAO;AAG5B,eAAK,WAAW,KAAK,QAAQ,YAAY,OAAO;AAChD,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,eAAe,SAAS,iBAAiB;AACrC,cAAI,eAAe,iBAAiB,iBAAiB,IAAI,GAAG;AACxD,mBAAO;AAAA,UACX;AAEA,cAAI,eAAe,KAAK;AACxB,4BAAkB,6BAA6B,iBAAiB,IAAI;AACpE,cAAI,CAAC,aAAa,gBAAgB,EAAE,GAAG;AACnC,yBAAa,gBAAgB,EAAE,IAAI;AACnC,4BAAgB,cAAc,IAAI;AAAA,UACtC;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,mBAAmB,SAAS,iBAAiB;AACzC,cAAI,eAAe,iBAAiB,qBAAqB,IAAI,GAAG;AAC5D,mBAAO;AAAA,UACX;AAEA,4BAAkB,6BAA6B,iBAAiB,IAAI;AACpE,iBAAO,KAAK,aAAa,gBAAgB,EAAE;AAC3C,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,gBAAgB,SAAS,iBAAiB;AACtC,cAAI,eAAe,iBAAiB,kBAAkB,IAAI,GAAG;AACzD,mBAAO;AAAA,UACX;AAEA,cAAI,cAAc,KAAK;AACvB,4BAAkB,6BAA6B,iBAAiB,IAAI;AACpE,cAAI,QAAQ,aAAa,eAAe,MAAM,IAAI;AAC9C,wBAAY,KAAK,eAAe;AAChC,4BAAgB,eAAe,IAAI;AAAA,UACvC;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,oBAAoB,SAAS,iBAAiB;AAC1C,cAAI,eAAe,iBAAiB,sBAAsB,IAAI,GAAG;AAC7D,mBAAO;AAAA,UACX;AAEA,4BAAkB,6BAA6B,iBAAiB,IAAI;AACpE,cAAI,QAAQ,QAAQ,KAAK,aAAa,eAAe;AACrD,cAAI,QAAQ,IAAI;AACZ,iBAAK,YAAY,OAAO,OAAO,CAAC;AAAA,UACpC;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,oBAAoB,WAAW;AAC3B,iBAAO,KAAK,YAAY,SAAS;AAAA,QACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,kBAAkB,SAAS,iBAAiB;AACxC,iBAAO,CAAC,CAAC,KAAK,aAAa,gBAAgB,EAAE;AAAA,QACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,MAAM,SAAS,OAAO;AAClB,cAAIQ,QAAO;AACX,cAAI,QAAQ,KAAK;AAEjB,mBAAS,KAAK,OAAO;AACjB,YAAAA,MAAK,QAAQ,KAAK,OAAO,KAAK;AAAA,UAClC;AAGA,cAAI,QAAQ,aAAa;AACrB,iBAAKA,MAAK,QAAQ,QAAQ,SAAS,KAAK,CAAC;AAAA,UAC7C;AAEA,eAAKA,MAAK,QAAQ,KAAK;AAEvB,cAAI,MAAM,iBAAiB;AACvB,iBAAK,MAAM,eAAe;AAAA,UAC9B;AAGA,cAAI,SAAS,aAAa;AACtB,iBAAKA,MAAK,QAAQ,QAAQ,SAAS,KAAK,CAAC;AAAA,UAC7C;AAAA,QACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,SAAS,SAAS,OAAO;AACrB,cAAI,KAAK,QAAQ,GAAG;AAChB,mBAAO,KAAK,KAAK,KAAK;AAAA,UAC1B;AAEA,eAAK,QAAQ;AAAA,QACjB;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,SAAS,WAAW;AAChB,cAAI,IAAI;AACR,iBAAO,IAAI,KAAK,YAAY,QAAQ;AAChC,gBAAI,EAAE,KAAK,YAAY,CAAC,EAAE,SAAS,eAAe,kBAAkB;AAChE,qBAAO;AAAA,YACX;AACA;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,WAAW,SAAS,WAAW;AAG3B,cAAI,iBAAiB,OAAO,CAAC,GAAG,SAAS;AAGzC,cAAI,CAAC,SAAS,KAAK,QAAQ,QAAQ,CAAC,MAAM,cAAc,CAAC,GAAG;AACxD,iBAAK,MAAM;AACX,iBAAK,QAAQ;AACb;AAAA,UACJ;AAGA,cAAI,KAAK,SAAS,mBAAmB,kBAAkB,eAAe;AAClE,iBAAK,QAAQ;AAAA,UACjB;AAEA,eAAK,QAAQ,KAAK,QAAQ,cAAc;AAIxC,cAAI,KAAK,SAAS,cAAc,gBAAgB,cAAc,kBAAkB;AAC5E,iBAAK,QAAQ,cAAc;AAAA,UAC/B;AAAA,QACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASA,SAAS,SAAS,WAAW;AAAA,QAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAO/B,gBAAgB,WAAW;AAAA,QAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAO7B,OAAO,WAAW;AAAA,QAAE;AAAA,MACxB;AAOA,eAAS,SAAS,OAAO;AACrB,YAAI,QAAQ,iBAAiB;AACzB,iBAAO;AAAA,QACX,WAAW,QAAQ,aAAa;AAC5B,iBAAO;AAAA,QACX,WAAW,QAAQ,eAAe;AAC9B,iBAAO;AAAA,QACX,WAAW,QAAQ,aAAa;AAC5B,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAOA,eAAS,aAAa,WAAW;AAC7B,YAAI,aAAa,gBAAgB;AAC7B,iBAAO;AAAA,QACX,WAAW,aAAa,cAAc;AAClC,iBAAO;AAAA,QACX,WAAW,aAAa,gBAAgB;AACpC,iBAAO;AAAA,QACX,WAAW,aAAa,iBAAiB;AACrC,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAQA,eAAS,6BAA6B,iBAAiB,YAAY;AAC/D,YAAI,UAAU,WAAW;AACzB,YAAI,SAAS;AACT,iBAAO,QAAQ,IAAI,eAAe;AAAA,QACtC;AACA,eAAO;AAAA,MACX;AAOA,eAAS,iBAAiB;AACtB,mBAAW,MAAM,MAAM,SAAS;AAAA,MACpC;AAEA,cAAQ,gBAAgB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,QAKhC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,UAKN,UAAU;AAAA,QACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,UAAU,SAAS,OAAO;AACtB,cAAI,iBAAiB,KAAK,QAAQ;AAClC,iBAAO,mBAAmB,KAAK,MAAM,SAAS,WAAW;AAAA,QAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,SAAS,SAAS,OAAO;AACrB,cAAI,QAAQ,KAAK;AACjB,cAAI,YAAY,MAAM;AAEtB,cAAI,eAAe,SAAS,cAAc;AAC1C,cAAI,UAAU,KAAK,SAAS,KAAK;AAGjC,cAAI,iBAAiB,YAAY,gBAAgB,CAAC,UAAU;AACxD,mBAAO,QAAQ;AAAA,UACnB,WAAW,gBAAgB,SAAS;AAChC,gBAAI,YAAY,WAAW;AACvB,qBAAO,QAAQ;AAAA,YACnB,WAAW,EAAE,QAAQ,cAAc;AAC/B,qBAAO;AAAA,YACX;AACA,mBAAO,QAAQ;AAAA,UACnB;AACA,iBAAO;AAAA,QACX;AAAA,MACJ,CAAC;AAQD,eAAS,gBAAgB;AACrB,uBAAe,MAAM,MAAM,SAAS;AAEpC,aAAK,KAAK;AACV,aAAK,KAAK;AAAA,MACd;AAEA,cAAQ,eAAe,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,QAKnC,UAAU;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,UAAU;AAAA,UACV,WAAW;AAAA,QACf;AAAA,QAEA,gBAAgB,WAAW;AACvB,cAAI,YAAY,KAAK,QAAQ;AAC7B,cAAI,UAAU,CAAC;AACf,cAAI,YAAY,sBAAsB;AAClC,oBAAQ,KAAK,kBAAkB;AAAA,UACnC;AACA,cAAI,YAAY,oBAAoB;AAChC,oBAAQ,KAAK,kBAAkB;AAAA,UACnC;AACA,iBAAO;AAAA,QACX;AAAA,QAEA,eAAe,SAAS,OAAO;AAC3B,cAAI,UAAU,KAAK;AACnB,cAAI,WAAW;AACf,cAAI,WAAW,MAAM;AACrB,cAAI,YAAY,MAAM;AACtB,cAAI,IAAI,MAAM;AACd,cAAI,IAAI,MAAM;AAGd,cAAI,EAAE,YAAY,QAAQ,YAAY;AAClC,gBAAI,QAAQ,YAAY,sBAAsB;AAC1C,0BAAa,MAAM,IAAK,iBAAkB,IAAI,IAAK,iBAAiB;AACpE,yBAAW,KAAK,KAAK;AACrB,yBAAW,KAAK,IAAI,MAAM,MAAM;AAAA,YACpC,OAAO;AACH,0BAAa,MAAM,IAAK,iBAAkB,IAAI,IAAK,eAAe;AAClE,yBAAW,KAAK,KAAK;AACrB,yBAAW,KAAK,IAAI,MAAM,MAAM;AAAA,YACpC;AAAA,UACJ;AACA,gBAAM,YAAY;AAClB,iBAAO,YAAY,WAAW,QAAQ,aAAa,YAAY,QAAQ;AAAA,QAC3E;AAAA,QAEA,UAAU,SAAS,OAAO;AACtB,iBAAO,eAAe,UAAU,SAAS,KAAK,MAAM,KAAK,MACpD,KAAK,QAAQ,eAAgB,EAAE,KAAK,QAAQ,gBAAgB,KAAK,cAAc,KAAK;AAAA,QAC7F;AAAA,QAEA,MAAM,SAAS,OAAO;AAElB,eAAK,KAAK,MAAM;AAChB,eAAK,KAAK,MAAM;AAEhB,cAAI,YAAY,aAAa,MAAM,SAAS;AAE5C,cAAI,WAAW;AACX,kBAAM,kBAAkB,KAAK,QAAQ,QAAQ;AAAA,UACjD;AACA,eAAK,OAAO,KAAK,KAAK,MAAM,KAAK;AAAA,QACrC;AAAA,MACJ,CAAC;AAQD,eAAS,kBAAkB;AACvB,uBAAe,MAAM,MAAM,SAAS;AAAA,MACxC;AAEA,cAAQ,iBAAiB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,QAKrC,UAAU;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,UAAU;AAAA,QACd;AAAA,QAEA,gBAAgB,WAAW;AACvB,iBAAO,CAAC,iBAAiB;AAAA,QAC7B;AAAA,QAEA,UAAU,SAAS,OAAO;AACtB,iBAAO,KAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MACvC,KAAK,IAAI,MAAM,QAAQ,CAAC,IAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ;AAAA,QAC5E;AAAA,QAEA,MAAM,SAAS,OAAO;AAClB,cAAI,MAAM,UAAU,GAAG;AACnB,gBAAI,QAAQ,MAAM,QAAQ,IAAI,OAAO;AACrC,kBAAM,kBAAkB,KAAK,QAAQ,QAAQ;AAAA,UACjD;AACA,eAAK,OAAO,KAAK,KAAK,MAAM,KAAK;AAAA,QACrC;AAAA,MACJ,CAAC;AAQD,eAAS,kBAAkB;AACvB,mBAAW,MAAM,MAAM,SAAS;AAEhC,aAAK,SAAS;AACd,aAAK,SAAS;AAAA,MAClB;AAEA,cAAQ,iBAAiB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,QAKjC,UAAU;AAAA,UACN,OAAO;AAAA,UACP,UAAU;AAAA,UACV,MAAM;AAAA;AAAA,UACN,WAAW;AAAA;AAAA,QACf;AAAA,QAEA,gBAAgB,WAAW;AACvB,iBAAO,CAAC,iBAAiB;AAAA,QAC7B;AAAA,QAEA,SAAS,SAAS,OAAO;AACrB,cAAI,UAAU,KAAK;AACnB,cAAI,gBAAgB,MAAM,SAAS,WAAW,QAAQ;AACtD,cAAI,gBAAgB,MAAM,WAAW,QAAQ;AAC7C,cAAI,YAAY,MAAM,YAAY,QAAQ;AAE1C,eAAK,SAAS;AAId,cAAI,CAAC,iBAAiB,CAAC,iBAAkB,MAAM,aAAa,YAAY,iBAAiB,CAAC,WAAY;AAClG,iBAAK,MAAM;AAAA,UACf,WAAW,MAAM,YAAY,aAAa;AACtC,iBAAK,MAAM;AACX,iBAAK,SAAS,kBAAkB,WAAW;AACvC,mBAAK,QAAQ;AACb,mBAAK,QAAQ;AAAA,YACjB,GAAG,QAAQ,MAAM,IAAI;AAAA,UACzB,WAAW,MAAM,YAAY,WAAW;AACpC,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX;AAAA,QAEA,OAAO,WAAW;AACd,uBAAa,KAAK,MAAM;AAAA,QAC5B;AAAA,QAEA,MAAM,SAAS,OAAO;AAClB,cAAI,KAAK,UAAU,kBAAkB;AACjC;AAAA,UACJ;AAEA,cAAI,SAAU,MAAM,YAAY,WAAY;AACxC,iBAAK,QAAQ,KAAK,KAAK,QAAQ,QAAQ,MAAM,KAAK;AAAA,UACtD,OAAO;AACH,iBAAK,OAAO,YAAY,IAAI;AAC5B,iBAAK,QAAQ,KAAK,KAAK,QAAQ,OAAO,KAAK,MAAM;AAAA,UACrD;AAAA,QACJ;AAAA,MACJ,CAAC;AAQD,eAAS,mBAAmB;AACxB,uBAAe,MAAM,MAAM,SAAS;AAAA,MACxC;AAEA,cAAQ,kBAAkB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,QAKtC,UAAU;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,UAAU;AAAA,QACd;AAAA,QAEA,gBAAgB,WAAW;AACvB,iBAAO,CAAC,iBAAiB;AAAA,QAC7B;AAAA,QAEA,UAAU,SAAS,OAAO;AACtB,iBAAO,KAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MACvC,KAAK,IAAI,MAAM,QAAQ,IAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ;AAAA,QAC3E;AAAA,MACJ,CAAC;AAQD,eAAS,kBAAkB;AACvB,uBAAe,MAAM,MAAM,SAAS;AAAA,MACxC;AAEA,cAAQ,iBAAiB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,QAKrC,UAAU;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,UAAU;AAAA,UACV,WAAW,uBAAuB;AAAA,UAClC,UAAU;AAAA,QACd;AAAA,QAEA,gBAAgB,WAAW;AACvB,iBAAO,cAAc,UAAU,eAAe,KAAK,IAAI;AAAA,QAC3D;AAAA,QAEA,UAAU,SAAS,OAAO;AACtB,cAAI,YAAY,KAAK,QAAQ;AAC7B,cAAI;AAEJ,cAAI,aAAa,uBAAuB,qBAAqB;AACzD,uBAAW,MAAM;AAAA,UACrB,WAAW,YAAY,sBAAsB;AACzC,uBAAW,MAAM;AAAA,UACrB,WAAW,YAAY,oBAAoB;AACvC,uBAAW,MAAM;AAAA,UACrB;AAEA,iBAAO,KAAK,OAAO,SAAS,KAAK,MAAM,KAAK,KACxC,YAAY,MAAM,mBAClB,MAAM,WAAW,KAAK,QAAQ,aAC9B,MAAM,eAAe,KAAK,QAAQ,YAClC,IAAI,QAAQ,IAAI,KAAK,QAAQ,YAAY,MAAM,YAAY;AAAA,QACnE;AAAA,QAEA,MAAM,SAAS,OAAO;AAClB,cAAI,YAAY,aAAa,MAAM,eAAe;AAClD,cAAI,WAAW;AACX,iBAAK,QAAQ,KAAK,KAAK,QAAQ,QAAQ,WAAW,KAAK;AAAA,UAC3D;AAEA,eAAK,QAAQ,KAAK,KAAK,QAAQ,OAAO,KAAK;AAAA,QAC/C;AAAA,MACJ,CAAC;AAYD,eAAS,gBAAgB;AACrB,mBAAW,MAAM,MAAM,SAAS;AAIhC,aAAK,QAAQ;AACb,aAAK,UAAU;AAEf,aAAK,SAAS;AACd,aAAK,SAAS;AACd,aAAK,QAAQ;AAAA,MACjB;AAEA,cAAQ,eAAe,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,QAK/B,UAAU;AAAA,UACN,OAAO;AAAA,UACP,UAAU;AAAA,UACV,MAAM;AAAA,UACN,UAAU;AAAA;AAAA,UACV,MAAM;AAAA;AAAA,UACN,WAAW;AAAA;AAAA,UACX,cAAc;AAAA;AAAA,QAClB;AAAA,QAEA,gBAAgB,WAAW;AACvB,iBAAO,CAAC,yBAAyB;AAAA,QACrC;AAAA,QAEA,SAAS,SAAS,OAAO;AACrB,cAAI,UAAU,KAAK;AAEnB,cAAI,gBAAgB,MAAM,SAAS,WAAW,QAAQ;AACtD,cAAI,gBAAgB,MAAM,WAAW,QAAQ;AAC7C,cAAI,iBAAiB,MAAM,YAAY,QAAQ;AAE/C,eAAK,MAAM;AAEX,cAAK,MAAM,YAAY,eAAiB,KAAK,UAAU,GAAI;AACvD,mBAAO,KAAK,YAAY;AAAA,UAC5B;AAIA,cAAI,iBAAiB,kBAAkB,eAAe;AAClD,gBAAI,MAAM,aAAa,WAAW;AAC9B,qBAAO,KAAK,YAAY;AAAA,YAC5B;AAEA,gBAAI,gBAAgB,KAAK,QAAS,MAAM,YAAY,KAAK,QAAQ,QAAQ,WAAY;AACrF,gBAAI,gBAAgB,CAAC,KAAK,WAAW,YAAY,KAAK,SAAS,MAAM,MAAM,IAAI,QAAQ;AAEvF,iBAAK,QAAQ,MAAM;AACnB,iBAAK,UAAU,MAAM;AAErB,gBAAI,CAAC,iBAAiB,CAAC,eAAe;AAClC,mBAAK,QAAQ;AAAA,YACjB,OAAO;AACH,mBAAK,SAAS;AAAA,YAClB;AAEA,iBAAK,SAAS;AAId,gBAAI,WAAW,KAAK,QAAQ,QAAQ;AACpC,gBAAI,aAAa,GAAG;AAGhB,kBAAI,CAAC,KAAK,mBAAmB,GAAG;AAC5B,uBAAO;AAAA,cACX,OAAO;AACH,qBAAK,SAAS,kBAAkB,WAAW;AACvC,uBAAK,QAAQ;AACb,uBAAK,QAAQ;AAAA,gBACjB,GAAG,QAAQ,UAAU,IAAI;AACzB,uBAAO;AAAA,cACX;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,QAEA,aAAa,WAAW;AACpB,eAAK,SAAS,kBAAkB,WAAW;AACvC,iBAAK,QAAQ;AAAA,UACjB,GAAG,KAAK,QAAQ,UAAU,IAAI;AAC9B,iBAAO;AAAA,QACX;AAAA,QAEA,OAAO,WAAW;AACd,uBAAa,KAAK,MAAM;AAAA,QAC5B;AAAA,QAEA,MAAM,WAAW;AACb,cAAI,KAAK,SAAS,kBAAkB;AAChC,iBAAK,OAAO,WAAW,KAAK;AAC5B,iBAAK,QAAQ,KAAK,KAAK,QAAQ,OAAO,KAAK,MAAM;AAAA,UACrD;AAAA,QACJ;AAAA,MACJ,CAAC;AAQD,eAASE,QAAO,SAAS,SAAS;AAC9B,kBAAU,WAAW,CAAC;AACtB,gBAAQ,cAAc,YAAY,QAAQ,aAAaA,QAAO,SAAS,MAAM;AAC7E,eAAO,IAAI,QAAQ,SAAS,OAAO;AAAA,MACvC;AAKA,MAAAA,QAAO,UAAU;AAMjB,MAAAA,QAAO,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOd,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQX,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,QAMb,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASR,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOb,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOZ,QAAQ;AAAA;AAAA,UAEJ,CAAC,kBAAkB,EAAC,QAAQ,MAAK,CAAC;AAAA,UAClC,CAAC,iBAAiB,EAAC,QAAQ,MAAK,GAAG,CAAC,QAAQ,CAAC;AAAA,UAC7C,CAAC,iBAAiB,EAAC,WAAW,qBAAoB,CAAC;AAAA,UACnD,CAAC,eAAe,EAAC,WAAW,qBAAoB,GAAG,CAAC,OAAO,CAAC;AAAA,UAC5D,CAAC,aAAa;AAAA,UACd,CAAC,eAAe,EAAC,OAAO,aAAa,MAAM,EAAC,GAAG,CAAC,KAAK,CAAC;AAAA,UACtD,CAAC,eAAe;AAAA,QACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMN,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOZ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASb,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOd,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOhB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQV,mBAAmB;AAAA,QACvB;AAAA,MACJ;AAEA,UAAI,OAAO;AACX,UAAI,cAAc;AAQlB,eAAS,QAAQ,SAAS,SAAS;AAC/B,aAAK,UAAU,OAAO,CAAC,GAAGA,QAAO,UAAU,WAAW,CAAC,CAAC;AAExD,aAAK,QAAQ,cAAc,KAAK,QAAQ,eAAe;AAEvD,aAAK,WAAW,CAAC;AACjB,aAAK,UAAU,CAAC;AAChB,aAAK,cAAc,CAAC;AACpB,aAAK,cAAc,CAAC;AAEpB,aAAK,UAAU;AACf,aAAK,QAAQ,oBAAoB,IAAI;AACrC,aAAK,cAAc,IAAI,YAAY,MAAM,KAAK,QAAQ,WAAW;AAEjE,uBAAe,MAAM,IAAI;AAEzB,QAAAP,MAAK,KAAK,QAAQ,aAAa,SAAS,MAAM;AAC1C,cAAI,aAAa,KAAK,IAAI,IAAK,KAAK,CAAC,EAAG,KAAK,CAAC,CAAC,CAAC;AAChD,eAAK,CAAC,KAAK,WAAW,cAAc,KAAK,CAAC,CAAC;AAC3C,eAAK,CAAC,KAAK,WAAW,eAAe,KAAK,CAAC,CAAC;AAAA,QAChD,GAAG,IAAI;AAAA,MACX;AAEA,cAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMhB,KAAK,SAAS,SAAS;AACnB,iBAAO,KAAK,SAAS,OAAO;AAG5B,cAAI,QAAQ,aAAa;AACrB,iBAAK,YAAY,OAAO;AAAA,UAC5B;AACA,cAAI,QAAQ,aAAa;AAErB,iBAAK,MAAM,QAAQ;AACnB,iBAAK,MAAM,SAAS,QAAQ;AAC5B,iBAAK,MAAM,KAAK;AAAA,UACpB;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,MAAM,SAAS,OAAO;AAClB,eAAK,QAAQ,UAAU,QAAQ,cAAc;AAAA,QACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,WAAW,SAAS,WAAW;AAC3B,cAAI,UAAU,KAAK;AACnB,cAAI,QAAQ,SAAS;AACjB;AAAA,UACJ;AAGA,eAAK,YAAY,gBAAgB,SAAS;AAE1C,cAAI;AACJ,cAAI,cAAc,KAAK;AAKvB,cAAI,gBAAgB,QAAQ;AAI5B,cAAI,CAAC,iBAAkB,iBAAiB,cAAc,QAAQ,kBAAmB;AAC7E,4BAAgB,QAAQ,gBAAgB;AAAA,UAC5C;AAEA,cAAI,IAAI;AACR,iBAAO,IAAI,YAAY,QAAQ;AAC3B,yBAAa,YAAY,CAAC;AAQ1B,gBAAI,QAAQ,YAAY;AAAA,aAChB,CAAC,iBAAiB,cAAc;AAAA,YAChC,WAAW,iBAAiB,aAAa,IAAI;AACjD,yBAAW,UAAU,SAAS;AAAA,YAClC,OAAO;AACH,yBAAW,MAAM;AAAA,YACrB;AAIA,gBAAI,CAAC,iBAAiB,WAAW,SAAS,cAAc,gBAAgB,cAAc;AAClF,8BAAgB,QAAQ,gBAAgB;AAAA,YAC5C;AACA;AAAA,UACJ;AAAA,QACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,KAAK,SAAS,YAAY;AACtB,cAAI,sBAAsB,YAAY;AAClC,mBAAO;AAAA,UACX;AAEA,cAAI,cAAc,KAAK;AACvB,mBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACzC,gBAAI,YAAY,CAAC,EAAE,QAAQ,SAAS,YAAY;AAC5C,qBAAO,YAAY,CAAC;AAAA,YACxB;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,KAAK,SAAS,YAAY;AACtB,cAAI,eAAe,YAAY,OAAO,IAAI,GAAG;AACzC,mBAAO;AAAA,UACX;AAGA,cAAI,WAAW,KAAK,IAAI,WAAW,QAAQ,KAAK;AAChD,cAAI,UAAU;AACV,iBAAK,OAAO,QAAQ;AAAA,UACxB;AAEA,eAAK,YAAY,KAAK,UAAU;AAChC,qBAAW,UAAU;AAErB,eAAK,YAAY,OAAO;AACxB,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,QAAQ,SAAS,YAAY;AACzB,cAAI,eAAe,YAAY,UAAU,IAAI,GAAG;AAC5C,mBAAO;AAAA,UACX;AAEA,uBAAa,KAAK,IAAI,UAAU;AAGhC,cAAI,YAAY;AACZ,gBAAI,cAAc,KAAK;AACvB,gBAAI,QAAQ,QAAQ,aAAa,UAAU;AAE3C,gBAAI,UAAU,IAAI;AACd,0BAAY,OAAO,OAAO,CAAC;AAC3B,mBAAK,YAAY,OAAO;AAAA,YAC5B;AAAA,UACJ;AAEA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,IAAI,SAAS,QAAQ,SAAS;AAC1B,cAAI,WAAWD,YAAW;AACtB;AAAA,UACJ;AACA,cAAI,YAAYA,YAAW;AACvB;AAAA,UACJ;AAEA,cAAI,WAAW,KAAK;AACpB,UAAAC,MAAK,SAAS,MAAM,GAAG,SAAS,OAAO;AACnC,qBAAS,KAAK,IAAI,SAAS,KAAK,KAAK,CAAC;AACtC,qBAAS,KAAK,EAAE,KAAK,OAAO;AAAA,UAChC,CAAC;AACD,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,KAAK,SAAS,QAAQ,SAAS;AAC3B,cAAI,WAAWD,YAAW;AACtB;AAAA,UACJ;AAEA,cAAI,WAAW,KAAK;AACpB,UAAAC,MAAK,SAAS,MAAM,GAAG,SAAS,OAAO;AACnC,gBAAI,CAAC,SAAS;AACV,qBAAO,SAAS,KAAK;AAAA,YACzB,OAAO;AACH,uBAAS,KAAK,KAAK,SAAS,KAAK,EAAE,OAAO,QAAQ,SAAS,KAAK,GAAG,OAAO,GAAG,CAAC;AAAA,YAClF;AAAA,UACJ,CAAC;AACD,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,MAAM,SAAS,OAAO,MAAM;AAExB,cAAI,KAAK,QAAQ,WAAW;AACxB,4BAAgB,OAAO,IAAI;AAAA,UAC/B;AAGA,cAAI,WAAW,KAAK,SAAS,KAAK,KAAK,KAAK,SAAS,KAAK,EAAE,MAAM;AAClE,cAAI,CAAC,YAAY,CAAC,SAAS,QAAQ;AAC/B;AAAA,UACJ;AAEA,eAAK,OAAO;AACZ,eAAK,iBAAiB,WAAW;AAC7B,iBAAK,SAAS,eAAe;AAAA,UACjC;AAEA,cAAI,IAAI;AACR,iBAAO,IAAI,SAAS,QAAQ;AACxB,qBAAS,CAAC,EAAE,IAAI;AAChB;AAAA,UACJ;AAAA,QACJ;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,SAAS,WAAW;AAChB,eAAK,WAAW,eAAe,MAAM,KAAK;AAE1C,eAAK,WAAW,CAAC;AACjB,eAAK,UAAU,CAAC;AAChB,eAAK,MAAM,QAAQ;AACnB,eAAK,UAAU;AAAA,QACnB;AAAA,MACJ;AAOA,eAAS,eAAe,SAAS,KAAK;AAClC,YAAI,UAAU,QAAQ;AACtB,YAAI,CAAC,QAAQ,OAAO;AAChB;AAAA,QACJ;AACA,YAAI;AACJ,QAAAA,MAAK,QAAQ,QAAQ,UAAU,SAAS,OAAO,MAAM;AACjD,iBAAO,SAAS,QAAQ,OAAO,IAAI;AACnC,cAAI,KAAK;AACL,oBAAQ,YAAY,IAAI,IAAI,QAAQ,MAAM,IAAI;AAC9C,oBAAQ,MAAM,IAAI,IAAI;AAAA,UAC1B,OAAO;AACH,oBAAQ,MAAM,IAAI,IAAI,QAAQ,YAAY,IAAI,KAAK;AAAA,UACvD;AAAA,QACJ,CAAC;AACD,YAAI,CAAC,KAAK;AACN,kBAAQ,cAAc,CAAC;AAAA,QAC3B;AAAA,MACJ;AAOA,eAAS,gBAAgB,OAAO,MAAM;AAClC,YAAI,eAAeF,UAAS,YAAY,OAAO;AAC/C,qBAAa,UAAU,OAAO,MAAM,IAAI;AACxC,qBAAa,UAAU;AACvB,aAAK,OAAO,cAAc,YAAY;AAAA,MAC1C;AAEA,aAAOS,SAAQ;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QAEP,IAAI;AAAA,QACJ,KAAK;AAAA,QACL,MAAMP;AAAA,QACN,OAAOG;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAID,UAAI,aAAc,OAAON,YAAW,cAAcA,UAAU,OAAO,SAAS,cAAc,OAAO,CAAC;AAClG,iBAAW,SAASU;AAEpB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC5C,eAAO,WAAW;AACd,iBAAOA;AAAA,QACX,CAAC;AAAA,MACL,WAAW,OAAO,UAAU,eAAe,OAAO,SAAS;AACvD,eAAO,UAAUA;AAAA,MACrB,OAAO;AACH,QAAAV,QAAO,UAAU,IAAIU;AAAA,MACzB;AAAA,IAEA,GAAG,QAAQ,UAAU,QAAQ;AAAA;AAAA;;;AC5kF7B,sBAAmB;AAGnB,IAAM,iBAAiB,UAAQ,QAAQ,KAAK,WAAW,KAAK;AAC5D,IAAM,aAAa,CAAC,KAAK,UAAU,OAAO,MAAM,MAAM,KAAK;AAC3D,IAAM,gBAAgB,CAAC,KAAK,UAAU,OAAO,CAAC,MAAM,MAAM,KAAK;AAC/D,SAAS,iBAAiB,MAAM,KAAK,OAAO;AAC1C,MAAI,SAAS,QAAW;AACtB,WAAO;AAAA,EACT,WAAW,OAAO,SAAS,UAAU;AACnC,WAAO,KAAK,QAAQ,GAAG,MAAM;AAAA,EAC/B,WAAW,OAAO,SAAS,YAAY;AACrC,WAAO,KAAK,EAAC,MAAK,CAAC,EAAE,QAAQ,GAAG,MAAM;AAAA,EACxC;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,MAAM,OAAO;AACtC,MAAI,OAAO,SAAS,YAAY;AAC9B,WAAO,KAAK,EAAC,MAAK,CAAC;AAAA,EACrB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,EAAC,GAAG,KAAK,QAAQ,GAAG,MAAM,IAAI,GAAG,KAAK,QAAQ,GAAG,MAAM,GAAE;AAAA,EAClE;AACA,SAAO,EAAC,GAAG,OAAO,GAAG,MAAK;AAC5B;AACA,SAASC,UAAS,IAAI,OAAO;AAC3B,MAAI;AACJ,SAAO,WAAW;AAChB,iBAAa,OAAO;AACpB,cAAU,WAAW,IAAI,KAAK;AAC9B,WAAO;AAAA,EACT;AACF;AACA,SAAS,mBAAmB,EAAC,GAAG,EAAC,GAAG,OAAO;AACzC,QAAM,SAAS,MAAM;AACrB,QAAM,WAAW,OAAO,KAAK,MAAM;AACnC,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAM,QAAQ,OAAO,SAAS,CAAC,CAAC;AAChC,QAAI,KAAK,MAAM,OAAO,KAAK,MAAM,UAAU,KAAK,MAAM,QAAQ,KAAK,MAAM,OAAO;AAC9E,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,wBAAwB,SAAS,OAAO,OAAO;AACtD,QAAM,EAAC,OAAO,MAAM,WAAW,cAAa,IAAI,WAAW,CAAC;AAC5D,QAAM,QAAQ,mBAAmB,OAAO,KAAK;AAC7C,QAAM,UAAU,kBAAkB,MAAM,KAAK;AAC7C,QAAM,eAAe,kBAAkB,WAAW,KAAK;AACvD,MAAI,eAAe;AACjB,UAAM,mBAAmB,kBAAkB,eAAe,KAAK;AAC/D,eAAW,QAAQ,CAAC,KAAK,GAAG,GAAG;AAC7B,UAAI,iBAAiB,IAAI,GAAG;AAC1B,qBAAa,IAAI,IAAI,QAAQ,IAAI;AACjC,gBAAQ,IAAI,IAAI;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS,aAAa,MAAM,IAAI,GAAG;AACrC,WAAO,CAAC,KAAK;AAAA,EACf;AACA,QAAM,gBAAgB,CAAC;AACvB,OAAK,MAAM,QAAQ,SAAS,WAAW;AACrC,QAAI,QAAQ,UAAU,IAAI,GAAG;AAC3B,oBAAc,KAAK,SAAS;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,IAAM,cAAc,oBAAI,QAAQ;AAChC,SAAS,SAAS,OAAO;AACvB,MAAI,QAAQ,YAAY,IAAI,KAAK;AACjC,MAAI,CAAC,OAAO;AACV,YAAQ;AAAA,MACN,qBAAqB,CAAC;AAAA,MACtB,oBAAoB,CAAC;AAAA,MACrB,UAAU,CAAC;AAAA,MACX,UAAU,CAAC;AAAA,MACX,UAAU;AAAA,MACV,SAAS;AAAA,IACX;AACA,gBAAY,IAAI,OAAO,KAAK;AAAA,EAC9B;AACA,SAAO;AACT;AACA,SAAS,YAAY,OAAO;AAC1B,cAAY,OAAO,KAAK;AAC1B;AAEA,SAAS,UAAU,KAAK,KAAK,OAAO,UAAU;AAC5C,QAAM,aAAa,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,MAAM,OAAO,SAAS,CAAC,CAAC;AACpE,QAAM,aAAa,IAAI;AACvB,SAAO;AAAA,IACL,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,EAClB;AACF;AACA,SAAS,gBAAgB,OAAO,OAAO;AACrC,QAAM,QAAQ,MAAM,aAAa,IAAI,MAAM,IAAI,MAAM;AACrD,SAAO,MAAM,iBAAiB,KAAK;AACrC;AACA,SAAS,gBAAgB,OAAOC,OAAM,QAAQ;AAC5C,QAAM,QAAQ,MAAM,MAAM,MAAM;AAChC,QAAM,WAAW,SAASA,QAAO;AACjC,QAAM,cAAc,gBAAgB,OAAO,MAAM;AACjD,SAAO,UAAU,aAAa,MAAM,KAAK,OAAO,QAAQ;AAC1D;AACA,SAAS,qBAAqB,OAAOA,OAAM,QAAQ;AACjD,QAAM,cAAc,gBAAgB,OAAO,MAAM;AACjD,MAAI,gBAAgB,QAAW;AAC7B,WAAO,EAAC,KAAK,MAAM,KAAK,KAAK,MAAM,IAAG;AAAA,EACxC;AACA,QAAM,SAAS,KAAK,MAAM,MAAM,GAAG;AACnC,QAAM,SAAS,KAAK,MAAM,MAAM,GAAG;AACnC,QAAM,YAAY,KAAK,MAAM,WAAW;AACxC,QAAM,WAAW,SAAS;AAC1B,QAAM,cAAc,YAAYA,QAAO;AACvC,QAAM,QAAQ,UAAU,WAAW,QAAQ,UAAU,WAAW;AAChE,SAAO;AAAA,IACL,KAAK,KAAK,IAAI,IAAI,SAAS,MAAM,GAAG;AAAA,IACpC,KAAK,KAAK,IAAI,IAAI,SAAS,MAAM,GAAG;AAAA,EACtC;AACF;AACA,SAAS,eAAe,OAAO,QAAQ;AACrC,SAAO,WAAW,OAAO,MAAM,EAAE,KAAK,OAAO,MAAM,IAAI,MAAM,CAAC;AAChE;AACA,SAAS,SAAS,OAAO,OAAO,aAAa,MAAM,UAAU;AAC3D,MAAI,QAAQ,YAAY,IAAI;AAC5B,MAAI,UAAU,YAAY;AACxB,UAAM,WAAW,MAAM,oBAAoB,MAAM,EAAE,EAAE,IAAI;AACzD,YAAQ,eAAe,SAAS,SAAS,SAAS,KAAK;AAAA,EACzD;AACA,SAAO,eAAe,OAAO,QAAQ;AACvC;AACA,SAAS,YAAY,OAAO,QAAQ,QAAQ;AAC1C,QAAM,KAAK,MAAM,iBAAiB,MAAM;AACxC,QAAM,KAAK,MAAM,iBAAiB,MAAM;AACxC,SAAO;AAAA,IACL,KAAK,KAAK,IAAI,IAAI,EAAE;AAAA,IACpB,KAAK,KAAK,IAAI,IAAI,EAAE;AAAA,EACtB;AACF;AACA,SAAS,SAAS,OAAO,EAAC,KAAK,KAAK,UAAU,SAAQ,GAAG,gBAAgB;AACvE,QAAM,UAAU,QAAQ,MAAM,OAAO;AACrC,SAAO;AACP,SAAO;AACP,QAAM,UAAU,eAAe,IAAI,WAAW,eAAe,IAAI;AACjE,QAAM,UAAU,eAAe,IAAI,WAAW,eAAe,IAAI;AACjE,QAAM,UAAU,QAAQ;AACxB,MAAI,aAAa,KAAK,SAAS,OAAO,GAAG;AACvC,UAAM;AAAA,EACR;AACA,MAAI,aAAa,KAAK,SAAS,OAAO,GAAG;AACvC,UAAM;AAAA,EACR;AACA,MAAI,MAAM,UAAU;AAClB,UAAM;AACN,UAAM,KAAK,IAAI,WAAW,OAAO,QAAQ;AAAA,EAC3C,WAAW,MAAM,UAAU;AACzB,UAAM;AACN,UAAM,KAAK,IAAI,WAAW,OAAO,QAAQ;AAAA,EAC3C;AACA,SAAO,EAAC,KAAK,IAAG;AAClB;AACA,SAAS,YAAY,OAAO,EAAC,KAAK,IAAG,GAAG,QAAQA,QAAO,OAAO;AAC5D,QAAM,QAAQ,SAAS,MAAM,KAAK;AAClC,QAAM,EAAC,SAAS,UAAS,IAAI;AAC7B,QAAM,cAAc,eAAe,OAAO,MAAM;AAChD,QAAM,EAAC,WAAW,EAAC,IAAI;AACvB,QAAM,WAAW,SAAS,OAAO,OAAO,aAAa,OAAO,SAAS;AACrE,QAAM,WAAW,SAAS,OAAO,OAAO,aAAa,OAAO,QAAQ;AACpE,MAAIA,UAAS,UAAU,MAAM,YAAY,MAAM,WAAW;AACxD,WAAO;AAAA,EACT;AACA,QAAM,aAAa,MAAM,MAAM,MAAM;AACrC,QAAM,QAAQA,QAAO,KAAK,IAAI,MAAM,KAAK,QAAQ,IAAI;AACrD,MAAIA,SAAQ,UAAU,YAAY,cAAc,UAAU;AACxD,WAAO;AAAA,EACT;AACA,QAAM,WAAW,SAAS,OAAO,EAAC,KAAK,KAAK,UAAU,SAAQ,GAAG,MAAM,oBAAoB,MAAM,EAAE,CAAC;AACpG,YAAU,MAAM,SAAS;AACzB,YAAU,MAAM,SAAS;AACzB,QAAM,mBAAmB,MAAM,EAAE,IAAI;AACrC,SAAO,MAAM,MAAM,SAAS,GAAG,MAAM,MAAM,OAAO,MAAM,MAAM,SAAS,GAAG,MAAM,MAAM;AACxF;AACA,SAAS,mBAAmB,OAAOA,OAAM,QAAQ,QAAQ;AACvD,QAAM,QAAQ,gBAAgB,OAAOA,OAAM,MAAM;AACjD,QAAM,WAAW,EAAC,KAAK,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,MAAM,IAAG;AACxE,SAAO,YAAY,OAAO,UAAU,QAAQ,IAAI;AAClD;AACA,SAAS,qBAAqB,OAAOA,OAAM,QAAQ,QAAQ;AACzD,QAAM,WAAW,qBAAqB,OAAOA,OAAM,MAAM;AACzD,SAAO,YAAY,OAAO,UAAU,QAAQ,IAAI;AAClD;AACA,SAAS,uBAAuB,OAAO,MAAM,IAAI,QAAQ;AACvD,cAAY,OAAO,YAAY,OAAO,MAAM,EAAE,GAAG,QAAQ,IAAI;AAC/D;AACA,IAAM,gBAAgB,CAAC,MAAM,MAAM,KAAK,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC;AACtH,SAAS,yBAAyB,OAAO;AACvC,QAAM,SAAS,MAAM,UAAU;AAC/B,QAAM,WAAW,OAAO,SAAS;AACjC,MAAI,MAAM,MAAM,GAAG;AACjB,UAAM,OAAO;AAAA,EACf;AACA,MAAI,MAAM,MAAM,UAAU;AACxB,UAAM,OAAO;AAAA,EACf;AACF;AACA,SAAS,kBAAkB,OAAOA,OAAM,QAAQ,QAAQ;AACtD,QAAM,QAAQ,gBAAgB,OAAOA,OAAM,MAAM;AACjD,MAAI,MAAM,QAAQ,MAAM,OAAOA,QAAO,GAAG;AACvC,6BAAyB,KAAK;AAAA,EAChC;AACA,QAAM,WAAW,EAAC,KAAK,MAAM,MAAM,cAAc,MAAM,GAAG,GAAG,KAAK,MAAM,MAAM,cAAc,MAAM,GAAG,EAAC;AACtG,SAAO,YAAY,OAAO,UAAU,QAAQ,IAAI;AAClD;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,MAAM,aAAa,IAAI,MAAM,QAAQ,MAAM;AACpD;AACA,SAAS,iBAAiB,OAAO,OAAO,QAAQ;AAC9C,QAAM,SAAS,MAAM,UAAU;AAC/B,QAAM,iBAAiB,OAAO,SAAS;AACvC,MAAI,EAAC,KAAK,IAAG,IAAI;AACjB,QAAM,QAAQ,KAAK,IAAI,MAAM,KAAK,CAAC;AACnC,QAAM,YAAY,KAAK,MAAM,YAAY,KAAK,IAAI,KAAK,IAAI,OAAO,EAAE,CAAC;AACrE,QAAM,WAAW,KAAK,MAAM,KAAK,IAAI,QAAQ,SAAS,CAAC;AACvD,MAAI;AACJ,MAAI,QAAQ,CAAC,WAAW;AACtB,UAAM,KAAK,IAAI,MAAM,UAAU,cAAc;AAC7C,UAAM,UAAU,IAAI,MAAM,MAAM;AAChC,cAAU,QAAQ;AAAA,EACpB,WAAW,QAAQ,WAAW;AAC5B,UAAM,KAAK,IAAI,GAAG,MAAM,QAAQ;AAChC,UAAM,UAAU,IAAI,MAAM,MAAM;AAChC,cAAU,QAAQ;AAAA,EACpB;AACA,SAAO,YAAY,OAAO,EAAC,KAAK,IAAG,GAAG,MAAM,KAAK;AACnD;AACA,IAAM,UAAU;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ,KAAK;AAAA,EACb,MAAM,KAAK,KAAK;AAAA,EAChB,KAAK,KAAK,KAAK,KAAK;AAAA,EACpB,MAAM,MAAM,KAAK,KAAK,KAAK;AAAA,EAC3B,OAAO,KAAK,KAAK,KAAK,KAAK;AAAA,EAC3B,SAAS,KAAK,KAAK,KAAK,KAAK;AAAA,EAC7B,MAAM,MAAM,KAAK,KAAK,KAAK;AAC7B;AACA,SAAS,kBAAkB,OAAO,OAAO,QAAQC,OAAM,OAAO;AAC5D,QAAM,EAAC,KAAK,WAAW,KAAK,SAAS,QAAO,IAAI;AAChD,QAAM,QAAQ,QAAQ,QAAQ,QAAQ,KAAK;AAC3C,QAAM,SAAS,QAAQ,KAAK,KAAK;AACjC,QAAM,SAAS,MAAM,iBAAiB,MAAM,iBAAiB,YAAY,MAAM,IAAI,KAAK;AACxF,QAAM,SAAS,MAAM,iBAAiB,MAAM,iBAAiB,UAAU,MAAM,IAAI,KAAK;AACtF,MAAI,MAAM,MAAM,KAAK,MAAM,MAAM,GAAG;AAClC,WAAO;AAAA,EACT;AACA,SAAO,YAAY,OAAO,EAAC,KAAK,QAAQ,KAAK,OAAM,GAAG,QAAQA,OAAM,QAAQ,KAAK;AACnF;AACA,SAAS,kBAAkB,OAAO,OAAO,QAAQ;AAC/C,SAAO,kBAAkB,OAAO,OAAO,QAAQ,IAAI;AACrD;AACA,IAAM,gBAAgB;AAAA,EACpB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,aAAa;AACf;AACA,IAAM,oBAAoB;AAAA,EACxB,SAAS;AACX;AACA,IAAM,eAAe;AAAA,EACnB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,aAAa;AAAA,EACb,YAAY;AACd;AAEA,SAAS,wBAAwB,OAAO,qBAAqB,oBAAoB;AAC/E,QAAM,EAAC,IAAI,SAAS,EAAC,KAAK,IAAG,EAAC,IAAI;AAClC,MAAI,CAAC,oBAAoB,EAAE,KAAK,CAAC,mBAAmB,EAAE,GAAG;AACvD,WAAO;AAAA,EACT;AACA,QAAM,WAAW,mBAAmB,EAAE;AACtC,SAAO,SAAS,QAAQ,OAAO,SAAS,QAAQ;AAClD;AACA,SAAS,oBAAoB,QAAQ,QAAQ;AAC3C,OAAK,QAAQ,CAAC,KAAK,QAAQ;AACzB,QAAI,CAAC,OAAO,GAAG,GAAG;AAChB,aAAO,OAAO,GAAG;AAAA,IACnB;AAAA,EACF,CAAC;AACH;AACA,SAAS,yBAAyB,OAAO,OAAO;AAC9C,QAAM,EAAC,OAAM,IAAI;AACjB,QAAM,EAAC,qBAAqB,mBAAkB,IAAI;AAClD,OAAK,QAAQ,SAAS,OAAO;AAC3B,QAAI,wBAAwB,OAAO,qBAAqB,kBAAkB,GAAG;AAC3E,0BAAoB,MAAM,EAAE,IAAI;AAAA,QAC9B,KAAK,EAAC,OAAO,MAAM,KAAK,SAAS,MAAM,QAAQ,IAAG;AAAA,QAClD,KAAK,EAAC,OAAO,MAAM,KAAK,SAAS,MAAM,QAAQ,IAAG;AAAA,MACpD;AAAA,IACF;AAAA,EACF,CAAC;AACD,sBAAoB,qBAAqB,MAAM;AAC/C,sBAAoB,oBAAoB,MAAM;AAC9C,SAAO;AACT;AACA,SAAS,OAAO,OAAO,QAAQ,QAAQ,QAAQ;AAC7C,QAAM,KAAK,cAAc,MAAM,IAAI,KAAK,cAAc;AACtD,WAAS,IAAI,CAAC,OAAO,QAAQ,QAAQ,MAAM,CAAC;AAC9C;AACA,SAAS,WAAW,OAAO,MAAM,IAAI,QAAQ;AAC3C,QAAM,KAAK,kBAAkB,MAAM,IAAI,KAAK,kBAAkB;AAC9D,WAAS,IAAI,CAAC,OAAO,MAAM,IAAI,MAAM,CAAC;AACxC;AACA,SAAS,UAAU,OAAO;AACxB,QAAM,KAAK,MAAM;AACjB,SAAO;AAAA,IACL,IAAI,GAAG,OAAO,GAAG,SAAS;AAAA,IAC1B,IAAI,GAAG,MAAM,GAAG,UAAU;AAAA,EAC5B;AACF;AACA,SAAS,KAAK,OAAO,QAAQ,aAAa,QAAQ,UAAU,OAAO;AACjE,QAAM,EAAC,IAAI,GAAG,IAAI,GAAG,aAAa,UAAU,KAAK,EAAC,IAAI,OAAO,WAAW,WAAW,EAAC,GAAG,QAAQ,GAAG,OAAM,IAAI;AAC5G,QAAM,QAAQ,SAAS,KAAK;AAC5B,QAAM,EAAC,SAAS,EAAC,QAAQ,MAAM,YAAW,EAAC,IAAI;AAC/C,2BAAyB,OAAO,KAAK;AACrC,QAAM,WAAW,MAAM;AACvB,QAAM,WAAW,MAAM;AACvB,QAAM,gBAAgB,wBAAwB,aAAa,YAAY,KAAK;AAC5E,OAAK,iBAAiB,MAAM,QAAQ,SAAS,OAAO;AAClD,QAAI,MAAM,aAAa,KAAK,UAAU;AACpC,aAAO,OAAO,GAAG,YAAY,MAAM;AAAA,IACrC,WAAW,CAAC,MAAM,aAAa,KAAK,UAAU;AAC5C,aAAO,OAAO,GAAG,YAAY,MAAM;AAAA,IACrC;AAAA,EACF,CAAC;AACD,QAAM,OAAO,UAAU;AACvB,WAAS,YAAY,QAAQ,CAAC,EAAC,OAAO,QAAO,CAAC,CAAC;AACjD;AACA,SAAS,SAAS,OAAO,IAAI,IAAI,aAAa,QAAQ,UAAU,OAAO;AACrE,QAAM,QAAQ,SAAS,KAAK;AAC5B,QAAM,EAAC,SAAS,EAAC,QAAQ,MAAM,YAAW,EAAC,IAAI;AAC/C,QAAM,EAAC,OAAO,KAAI,IAAI;AACtB,2BAAyB,OAAO,KAAK;AACrC,QAAM,WAAW,iBAAiB,MAAM,KAAK,KAAK;AAClD,QAAM,WAAW,iBAAiB,MAAM,KAAK,KAAK;AAClD,OAAK,MAAM,QAAQ,SAAS,OAAO;AACjC,QAAI,MAAM,aAAa,KAAK,UAAU;AACpC,iBAAW,OAAO,GAAG,GAAG,GAAG,GAAG,MAAM;AAAA,IACtC,WAAW,CAAC,MAAM,aAAa,KAAK,UAAU;AAC5C,iBAAW,OAAO,GAAG,GAAG,GAAG,GAAG,MAAM;AAAA,IACtC;AAAA,EACF,CAAC;AACD,QAAM,OAAO,UAAU;AACvB,WAAS,YAAY,QAAQ,CAAC,EAAC,OAAO,QAAO,CAAC,CAAC;AACjD;AACA,SAAS,UAAU,OAAO,SAAS,OAAO,aAAa,QAAQ,UAAU,OAAO;AAC9E,QAAM,QAAQ,SAAS,KAAK;AAC5B,2BAAyB,OAAO,KAAK;AACrC,QAAM,QAAQ,MAAM,OAAO,OAAO;AAClC,cAAY,OAAO,OAAO,QAAW,IAAI;AACzC,QAAM,OAAO,UAAU;AACvB,WAAS,MAAM,QAAQ,MAAM,QAAQ,CAAC,EAAC,OAAO,QAAO,CAAC,CAAC;AACzD;AACA,SAAS,UAAU,OAAO,aAAa,WAAW;AAChD,QAAM,QAAQ,SAAS,KAAK;AAC5B,QAAM,sBAAsB,yBAAyB,OAAO,KAAK;AACjE,OAAK,MAAM,QAAQ,SAAS,OAAO;AACjC,UAAM,eAAe,MAAM;AAC3B,QAAI,oBAAoB,MAAM,EAAE,GAAG;AACjC,mBAAa,MAAM,oBAAoB,MAAM,EAAE,EAAE,IAAI;AACrD,mBAAa,MAAM,oBAAoB,MAAM,EAAE,EAAE,IAAI;AAAA,IACvD,OAAO;AACL,aAAO,aAAa;AACpB,aAAO,aAAa;AAAA,IACtB;AACA,WAAO,MAAM,mBAAmB,MAAM,EAAE;AAAA,EAC1C,CAAC;AACD,QAAM,OAAO,UAAU;AACvB,WAAS,MAAM,QAAQ,KAAK,gBAAgB,CAAC,EAAC,MAAK,CAAC,CAAC;AACvD;AACA,SAAS,iBAAiB,OAAO,SAAS;AACxC,QAAM,WAAW,MAAM,oBAAoB,OAAO;AAClD,MAAI,CAAC,UAAU;AACb;AAAA,EACF;AACA,QAAM,EAAC,KAAK,IAAG,IAAI;AACnB,SAAO,eAAe,IAAI,SAAS,IAAI,KAAK,IAAI,eAAe,IAAI,SAAS,IAAI,KAAK;AACvF;AACA,SAAS,aAAa,OAAO;AAC3B,QAAM,QAAQ,SAAS,KAAK;AAC5B,MAAI,MAAM;AACV,MAAI,MAAM;AACV,OAAK,MAAM,QAAQ,SAAS,OAAO;AACjC,UAAM,YAAY,iBAAiB,OAAO,MAAM,EAAE;AAClD,QAAI,WAAW;AACb,YAAM,QAAQ,KAAK,MAAM,aAAa,MAAM,MAAM,MAAM,OAAO,GAAG,IAAI;AACtE,YAAM,KAAK,IAAI,KAAK,KAAK;AACzB,YAAM,KAAK,IAAI,KAAK,KAAK;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,SAAO,MAAM,IAAI,MAAM;AACzB;AACA,SAAS,SAAS,OAAO,OAAO,QAAQ,OAAO;AAC7C,QAAM,EAAC,SAAQ,IAAI;AACnB,QAAM,cAAc,SAAS,MAAM,EAAE,KAAK;AAC1C,MAAI,KAAK,WAAW,MAAM,KAAK,KAAK,GAAG;AACrC,aAAS;AAAA,EACX;AACA,QAAM,KAAK,aAAa,MAAM,IAAI,KAAK,aAAa;AACpD,MAAI,SAAS,IAAI,CAAC,OAAO,OAAO,MAAM,CAAC,GAAG;AACxC,aAAS,MAAM,EAAE,IAAI;AAAA,EACvB,OAAO;AACL,aAAS,MAAM,EAAE,IAAI;AAAA,EACvB;AACF;AACA,SAAS,IAAI,OAAO,OAAO,eAAe,aAAa,QAAQ;AAC7D,QAAM,EAAC,IAAI,GAAG,IAAI,EAAC,IAAI,OAAO,UAAU,WAAW,EAAC,GAAG,OAAO,GAAG,MAAK,IAAI;AAC1E,QAAM,QAAQ,SAAS,KAAK;AAC5B,QAAM,EAAC,SAAS,EAAC,KAAK,YAAY,OAAM,EAAC,IAAI;AAC7C,QAAM,EAAC,MAAK,IAAI,cAAc,CAAC;AAC/B,2BAAyB,OAAO,KAAK;AACrC,QAAM,WAAW,MAAM;AACvB,QAAM,WAAW,MAAM;AACvB,OAAK,iBAAiB,MAAM,QAAQ,SAAS,OAAO;AAClD,QAAI,MAAM,aAAa,KAAK,UAAU;AACpC,eAAS,OAAO,GAAG,QAAQ,KAAK;AAAA,IAClC,WAAW,CAAC,MAAM,aAAa,KAAK,UAAU;AAC5C,eAAS,OAAO,GAAG,QAAQ,KAAK;AAAA,IAClC;AAAA,EACF,CAAC;AACD,QAAM,OAAO,UAAU;AACvB,WAAS,OAAO,CAAC,EAAC,MAAK,CAAC,CAAC;AAC3B;AACA,SAAS,sBAAsB,OAAO;AACpC,QAAM,QAAQ,SAAS,KAAK;AAC5B,2BAAyB,OAAO,KAAK;AACrC,QAAM,cAAc,CAAC;AACrB,aAAW,WAAW,OAAO,KAAK,MAAM,MAAM,GAAG;AAC/C,UAAM,EAAC,KAAK,IAAG,IAAI,MAAM,oBAAoB,OAAO,KAAK,EAAC,KAAK,CAAC,GAAG,KAAK,CAAC,EAAC;AAC1E,gBAAY,OAAO,IAAI,EAAC,KAAK,IAAI,OAAO,KAAK,IAAI,MAAK;AAAA,EACxD;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,OAAO;AACnC,QAAM,QAAQ,SAAS,KAAK;AAC5B,QAAM,cAAc,CAAC;AACrB,aAAW,WAAW,OAAO,KAAK,MAAM,MAAM,GAAG;AAC/C,gBAAY,OAAO,IAAI,MAAM,mBAAmB,OAAO;AAAA,EACzD;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,OAAO;AAC/B,QAAM,cAAc,sBAAsB,KAAK;AAC/C,aAAW,WAAW,OAAO,KAAK,MAAM,MAAM,GAAG;AAC/C,UAAM,EAAC,KAAK,aAAa,KAAK,YAAW,IAAI,YAAY,OAAO;AAChE,QAAI,gBAAgB,UAAa,MAAM,OAAO,OAAO,EAAE,QAAQ,aAAa;AAC1E,aAAO;AAAA,IACT;AACA,QAAI,gBAAgB,UAAa,MAAM,OAAO,OAAO,EAAE,QAAQ,aAAa;AAC1E,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,OAAO;AACjC,QAAM,QAAQ,SAAS,KAAK;AAC5B,SAAO,MAAM,WAAW,MAAM;AAChC;AAEA,IAAM,QAAQ,CAAC,GAAG,MAAM,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,MAAM,CAAC,CAAC;AAC7D,SAAS,cAAc,OAAO,MAAM;AAClC,QAAM,EAAC,SAAQ,IAAI,SAAS,KAAK;AACjC,QAAM,UAAU,SAAS,IAAI;AAC7B,MAAI,WAAW,QAAQ,QAAQ;AAC7B,YAAQ,OAAO,oBAAoB,MAAM,OAAO;AAChD,WAAO,SAAS,IAAI;AAAA,EACtB;AACF;AACA,SAAS,WAAW,OAAO,QAAQ,MAAM,SAAS;AAChD,QAAM,EAAC,UAAU,QAAO,IAAI,SAAS,KAAK;AAC1C,QAAM,aAAa,SAAS,IAAI;AAChC,MAAI,cAAc,WAAW,WAAW,QAAQ;AAC9C;AAAA,EACF;AACA,gBAAc,OAAO,IAAI;AACzB,WAAS,IAAI,IAAI,CAAC,UAAU,QAAQ,OAAO,OAAO,OAAO;AACzD,WAAS,IAAI,EAAE,SAAS;AACxB,QAAM,UAAU,SAAS,UAAU,QAAQ;AAC3C,SAAO,iBAAiB,MAAM,SAAS,IAAI,GAAG,EAAC,QAAO,CAAC;AACzD;AACA,SAAS,UAAU,OAAO,OAAO;AAC/B,QAAM,QAAQ,SAAS,KAAK;AAC5B,MAAI,MAAM,WAAW;AACnB,UAAM,WAAW;AACjB,UAAM,UAAU;AAChB,UAAM,OAAO,MAAM;AAAA,EACrB;AACF;AACA,SAAS,QAAQ,OAAO,OAAO;AAC7B,QAAM,QAAQ,SAAS,KAAK;AAC5B,MAAI,CAAC,MAAM,aAAa,MAAM,QAAQ,UAAU;AAC9C;AAAA,EACF;AACA,gBAAc,OAAO,SAAS;AAC9B,QAAM,WAAW;AACjB,QAAM,YAAY,MAAM,UAAU;AAClC,QAAM,OAAO,MAAM;AACrB;AACA,SAAS,iBAAiB,OAAO,OAAO;AACtC,MAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,UAAM,aAAa,MAAM,OAAO,sBAAsB;AACtD,WAAO;AAAA,MACL,GAAG,MAAM,UAAU,WAAW;AAAA,MAC9B,GAAG,MAAM,UAAU,WAAW;AAAA,IAChC;AAAA,EACF;AACA,SAAO,oBAAoB,OAAO,KAAK;AACzC;AACA,SAAS,UAAU,OAAO,OAAO,aAAa;AAC5C,QAAM,EAAC,aAAa,eAAc,IAAI;AACtC,MAAI,aAAa;AACf,UAAM,QAAQ,iBAAiB,OAAO,KAAK;AAC3C,QAAI,SAAS,aAAa,CAAC,EAAC,OAAO,OAAO,MAAK,CAAC,CAAC,MAAM,OAAO;AAC5D,eAAS,gBAAgB,CAAC,EAAC,OAAO,MAAK,CAAC,CAAC;AACzC,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,UAAU,OAAO,OAAO;AAC/B,MAAI,MAAM,QAAQ;AAChB,UAAM,QAAQ,oBAAoB,OAAO,KAAK;AAC9C,QAAI,eAAe,OAAO,MAAM,MAAM,GAAG;AACvC;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAQ,SAAS,KAAK;AAC5B,QAAM,EAAC,KAAK,YAAY,MAAM,cAAc,CAAC,EAAC,IAAI,MAAM;AACxD,MACE,MAAM,WAAW,KACjB,WAAW,eAAe,UAAU,GAAG,KAAK,KAC5C,cAAc,eAAe,YAAY,IAAI,GAAG,KAAK,GACrD;AACA,WAAO,SAAS,YAAY,gBAAgB,CAAC,EAAC,OAAO,MAAK,CAAC,CAAC;AAAA,EAC9D;AACA,MAAI,UAAU,OAAO,OAAO,WAAW,MAAM,OAAO;AAClD;AAAA,EACF;AACA,QAAM,YAAY;AAClB,aAAW,OAAO,MAAM,OAAO,eAAe,aAAa,SAAS;AACpE,aAAW,OAAO,OAAO,UAAU,WAAW,OAAO;AACvD;AACA,SAAS,iBAAiB,EAAC,OAAO,IAAG,GAAG,aAAa;AACnD,MAAI,QAAQ,IAAI,IAAI,MAAM;AAC1B,MAAI,SAAS,IAAI,IAAI,MAAM;AAC3B,QAAM,QAAQ,KAAK,IAAI,QAAQ,MAAM;AACrC,MAAI,QAAQ,aAAa;AACvB,YAAQ,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,SAAS,WAAW;AAAA,EAC1D,WAAW,QAAQ,aAAa;AAC9B,aAAS,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,QAAQ,WAAW;AAAA,EAC3D;AACA,MAAI,IAAI,MAAM,IAAI;AAClB,MAAI,IAAI,MAAM,IAAI;AACpB;AACA,SAAS,iBAAiB,MAAM,WAAW,QAAQ,EAAC,KAAK,KAAK,KAAI,GAAG;AACnE,OAAK,GAAG,IAAI,MAAM,KAAK,IAAI,OAAO,MAAM,IAAI,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,UAAU,GAAG,GAAG,UAAU,GAAG,CAAC;AAChG,OAAK,GAAG,IAAI,MAAM,KAAK,IAAI,OAAO,MAAM,IAAI,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,UAAU,GAAG,GAAG,UAAU,GAAG,CAAC;AAClG;AACA,SAAS,kBAAkB,OAAO,aAAa,qBAAqB;AAClE,QAAM,SAAS;AAAA,IACb,OAAO,iBAAiB,YAAY,WAAW,KAAK;AAAA,IACpD,KAAK,iBAAiB,YAAY,SAAS,KAAK;AAAA,EAClD;AACA,MAAI,qBAAqB;AACvB,UAAM,cAAc,MAAM,UAAU,QAAQ,MAAM,UAAU;AAC5D,qBAAiB,QAAQ,WAAW;AAAA,EACtC;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,OAAO,MAAM,aAAa,qBAAqB;AACtE,QAAM,WAAW,iBAAiB,MAAM,KAAK,KAAK;AAClD,QAAM,WAAW,iBAAiB,MAAM,KAAK,KAAK;AAClD,QAAM,EAAC,KAAK,MAAM,OAAO,QAAQ,OAAO,YAAY,QAAQ,YAAW,IAAI,MAAM;AACjF,QAAM,OAAO,EAAC,KAAK,MAAM,OAAO,OAAM;AACtC,QAAM,SAAS,kBAAkB,OAAO,aAAa,uBAAuB,YAAY,QAAQ;AAChG,MAAI,UAAU;AACZ,qBAAiB,MAAM,MAAM,WAAW,QAAQ,EAAC,KAAK,QAAQ,KAAK,SAAS,MAAM,IAAG,CAAC;AAAA,EACxF;AACA,MAAI,UAAU;AACZ,qBAAiB,MAAM,MAAM,WAAW,QAAQ,EAAC,KAAK,OAAO,KAAK,UAAU,MAAM,IAAG,CAAC;AAAA,EACxF;AACA,QAAM,QAAQ,KAAK,QAAQ,KAAK;AAChC,QAAM,SAAS,KAAK,SAAS,KAAK;AAClC,SAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA,OAAO,YAAY,QAAQ,KAAM,aAAa,SAAS,aAAc;AAAA,IACrE,OAAO,YAAY,SAAS,KAAM,cAAc,UAAU,cAAe;AAAA,EAC3E;AACF;AACA,SAAS,QAAQ,OAAO,OAAO;AAC7B,QAAM,QAAQ,SAAS,KAAK;AAC5B,MAAI,CAAC,MAAM,WAAW;AACpB;AAAA,EACF;AACA,gBAAc,OAAO,WAAW;AAChC,QAAM,EAAC,MAAM,gBAAgB,MAAM,EAAC,YAAY,GAAG,oBAAmB,EAAC,IAAI,MAAM,QAAQ;AACzF,QAAM,OAAO,gBAAgB,OAAO,MAAM,EAAC,WAAW,MAAM,WAAW,SAAS,MAAK,GAAG,mBAAmB;AAC3G,QAAM,YAAY,iBAAiB,MAAM,KAAK,KAAK,IAAI,KAAK,QAAQ;AACpE,QAAM,YAAY,iBAAiB,MAAM,KAAK,KAAK,IAAI,KAAK,SAAS;AACrE,QAAM,WAAW,KAAK,KAAK,YAAY,YAAY,YAAY,SAAS;AACxE,QAAM,YAAY,MAAM,UAAU;AAClC,MAAI,YAAY,WAAW;AACzB,UAAM,WAAW;AACjB,UAAM,OAAO,MAAM;AACnB;AAAA,EACF;AACA,WAAS,OAAO,EAAC,GAAG,KAAK,MAAM,GAAG,KAAK,IAAG,GAAG,EAAC,GAAG,KAAK,OAAO,GAAG,KAAK,OAAM,GAAG,QAAQ,MAAM;AAC5F,QAAM,WAAW;AACjB,QAAM,kBAAkB;AACxB,WAAS,gBAAgB,CAAC,EAAC,MAAK,CAAC,CAAC;AACpC;AACA,SAAS,mBAAmB,OAAO,OAAO,aAAa;AACrD,MAAI,cAAc,eAAe,YAAY,KAAK,GAAG,KAAK,GAAG;AAC3D,aAAS,YAAY,gBAAgB,CAAC,EAAC,OAAO,MAAK,CAAC,CAAC;AACrD;AAAA,EACF;AACA,MAAI,UAAU,OAAO,OAAO,WAAW,MAAM,OAAO;AAClD;AAAA,EACF;AACA,MAAI,MAAM,YAAY;AACpB,UAAM,eAAe;AAAA,EACvB;AACA,MAAI,MAAM,WAAW,QAAW;AAC9B;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,MAAM,OAAO,OAAO;AAC3B,QAAM,EAAC,UAAU,EAAC,eAAc,GAAG,SAAS,EAAC,MAAM,YAAW,EAAC,IAAI,SAAS,KAAK;AACjF,MAAI,CAAC,mBAAmB,OAAO,OAAO,WAAW,GAAG;AAClD;AAAA,EACF;AACA,QAAM,OAAO,MAAM,OAAO,sBAAsB;AAChD,QAAM,QAAQ,YAAY,MAAM;AAChC,QAAM,aAAa,MAAM,UAAU,IAAI,IAAI,KAAK,IAAI,SAAS,IAAI;AACjE,QAAM,SAAS;AAAA,IACb,GAAG;AAAA,IACH,GAAG;AAAA,IACH,YAAY;AAAA,MACV,GAAG,MAAM,UAAU,KAAK;AAAA,MACxB,GAAG,MAAM,UAAU,KAAK;AAAA,IAC1B;AAAA,EACF;AACA,OAAK,OAAO,QAAQ,QAAQ,OAAO;AACnC,WAAS,gBAAgB,CAAC,EAAC,MAAK,CAAC,CAAC;AACpC;AACA,SAAS,oBAAoB,OAAO,MAAM,SAAS,OAAO;AACxD,MAAI,SAAS;AACX,aAAS,KAAK,EAAE,SAAS,IAAI,IAAIF,UAAS,MAAM,SAAS,SAAS,CAAC,EAAC,MAAK,CAAC,CAAC,GAAG,KAAK;AAAA,EACrF;AACF;AACA,SAAS,aAAa,OAAO,SAAS;AACpC,QAAM,SAAS,MAAM;AACrB,QAAM,EAAC,OAAO,cAAc,MAAM,aAAa,eAAc,IAAI,QAAQ;AACzE,MAAI,aAAa,SAAS;AACxB,eAAW,OAAO,QAAQ,SAAS,KAAK;AACxC,wBAAoB,OAAO,kBAAkB,gBAAgB,GAAG;AAAA,EAClE,OAAO;AACL,kBAAc,OAAO,OAAO;AAAA,EAC9B;AACA,MAAI,YAAY,SAAS;AACvB,eAAW,OAAO,QAAQ,aAAa,SAAS;AAChD,eAAW,OAAO,OAAO,eAAe,WAAW,OAAO;AAAA,EAC5D,OAAO;AACL,kBAAc,OAAO,WAAW;AAChC,kBAAc,OAAO,WAAW;AAChC,kBAAc,OAAO,SAAS;AAC9B,kBAAc,OAAO,SAAS;AAAA,EAChC;AACF;AACA,SAAS,gBAAgB,OAAO;AAC9B,gBAAc,OAAO,WAAW;AAChC,gBAAc,OAAO,WAAW;AAChC,gBAAc,OAAO,SAAS;AAC9B,gBAAc,OAAO,OAAO;AAC5B,gBAAc,OAAO,OAAO;AAC5B,gBAAc,OAAO,SAAS;AAChC;AAEA,SAAS,cAAc,OAAO,OAAO;AACnC,SAAO,SAAS,YAAY,OAAO;AACjC,UAAM,EAAC,KAAK,YAAY,MAAM,cAAc,CAAC,EAAC,IAAI,MAAM;AACxD,QAAI,CAAC,cAAc,CAAC,WAAW,SAAS;AACtC,aAAO;AAAA,IACT;AACA,UAAM,WAAW,SAAS,MAAM;AAChC,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,QAAI,CAAC,MAAM,WAAW,MAAM,gBAAgB,YAC1C,cAAc,eAAe,UAAU,GAAG,QAAQ,KAAK,WAAW,eAAe,YAAY,IAAI,GAAG,QAAQ,IAC5G;AACA,eAAS,WAAW,eAAe,CAAC,EAAC,OAAO,MAAK,CAAC,CAAC;AACnD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,UAAU,IAAI,IAAI;AACzB,QAAM,SAAS,KAAK,IAAI,GAAG,UAAU,GAAG,OAAO;AAC/C,QAAM,SAAS,KAAK,IAAI,GAAG,UAAU,GAAG,OAAO;AAC/C,QAAM,IAAI,SAAS;AACnB,MAAI,GAAG;AACP,MAAI,IAAI,OAAO,IAAI,KAAK;AACtB,QAAI,IAAI;AAAA,EACV,WAAW,SAAS,QAAQ;AAC1B,QAAI;AAAA,EACN,OAAO;AACL,QAAI;AAAA,EACN;AACA,SAAO,EAAC,GAAG,EAAC;AACd;AACA,SAAS,YAAY,OAAO,OAAO,GAAG;AACpC,MAAI,MAAM,OAAO;AACf,UAAM,EAAC,QAAQ,SAAQ,IAAI;AAC3B,UAAM,cAAc,IAAI,MAAM,QAAQ,EAAE;AACxC,UAAM,OAAO,EAAE,OAAO,sBAAsB;AAC5C,UAAM,QAAQ,UAAU,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAChD,UAAM,OAAO,MAAM,QAAQ,KAAK;AAChC,UAAM,SAAS;AAAA,MACb,GAAG,MAAM,KAAK,iBAAiB,MAAM,KAAK,KAAK,IAAI,cAAc;AAAA,MACjE,GAAG,MAAM,KAAK,iBAAiB,MAAM,KAAK,KAAK,IAAI,cAAc;AAAA,MACjE,YAAY;AAAA,QACV,GAAG,OAAO,IAAI,KAAK;AAAA,QACnB,GAAG,OAAO,IAAI,KAAK;AAAA,MACrB;AAAA,IACF;AACA,SAAK,OAAO,QAAQ,QAAQ,OAAO;AACnC,UAAM,QAAQ,EAAE;AAAA,EAClB;AACF;AACA,SAAS,WAAW,OAAO,OAAO,OAAO;AACvC,MAAI,MAAM,QAAQ,KAAK,MAAM,SAAS;AACpC,UAAM,QAAQ,oBAAoB,OAAO,KAAK;AAC9C,QAAI,SAAS,MAAM,QAAQ,KAAK,aAAa,CAAC,EAAC,OAAO,OAAO,MAAK,CAAC,CAAC,MAAM,OAAO;AAC/E,YAAM,QAAQ;AACd,eAAS,MAAM,QAAQ,KAAK,gBAAgB,CAAC,EAAC,OAAO,MAAK,CAAC,CAAC;AAAA,IAC9D,OAAO;AACL,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF;AACF;AACA,SAAS,SAAS,OAAO,OAAO,GAAG;AACjC,MAAI,MAAM,OAAO;AACf,gBAAY,OAAO,OAAO,CAAC;AAC3B,UAAM,QAAQ;AACd,aAAS,MAAM,QAAQ,KAAK,gBAAgB,CAAC,EAAC,MAAK,CAAC,CAAC;AAAA,EACvD;AACF;AACA,SAAS,UAAU,OAAO,OAAO,GAAG;AAClC,QAAM,QAAQ,MAAM;AACpB,MAAI,OAAO;AACT,UAAM,UAAU;AAChB,QAAI,OAAO,EAAC,GAAG,EAAE,SAAS,MAAM,GAAG,GAAG,EAAE,SAAS,MAAM,EAAC,GAAG,MAAM,SAAS;AAC1E,UAAM,QAAQ,EAAC,GAAG,EAAE,QAAQ,GAAG,EAAE,OAAM;AAAA,EACzC;AACF;AACA,SAAS,SAAS,OAAO,OAAO,OAAO;AACrC,QAAM,EAAC,SAAS,YAAY,cAAa,IAAI,MAAM,QAAQ;AAC3D,MAAI,CAAC,SAAS;AACZ;AAAA,EACF;AACA,QAAM,OAAO,MAAM,OAAO,sBAAsB;AAChD,QAAM,QAAQ;AAAA,IACZ,GAAG,MAAM,OAAO,IAAI,KAAK;AAAA,IACzB,GAAG,MAAM,OAAO,IAAI,KAAK;AAAA,EAC3B;AACA,MAAI,SAAS,YAAY,CAAC,EAAC,OAAO,OAAO,MAAK,CAAC,CAAC,MAAM,OAAO;AAC3D,WAAO,SAAS,eAAe,CAAC,EAAC,OAAO,MAAK,CAAC,CAAC;AAAA,EACjD;AACA,QAAM,YAAY,wBAAwB,MAAM,QAAQ,KAAK,OAAO,KAAK;AACzE,QAAM,QAAQ,EAAC,GAAG,GAAG,GAAG,EAAC;AACzB,YAAU,OAAO,OAAO,KAAK;AAC/B;AACA,SAAS,OAAO,OAAO,OAAO;AAC5B,QAAM,QAAQ;AACd,MAAI,MAAM,SAAS;AACjB,UAAM,UAAU;AAChB,UAAM,kBAAkB;AACxB,aAAS,MAAM,QAAQ,IAAI,eAAe,CAAC,EAAC,MAAK,CAAC,CAAC;AAAA,EACrD;AACF;AACA,IAAM,UAAU,oBAAI,QAAQ;AAC5B,SAAS,YAAY,OAAO,SAAS;AACnC,QAAM,QAAQ,SAAS,KAAK;AAC5B,QAAM,SAAS,MAAM;AACrB,QAAM,EAAC,KAAK,YAAY,MAAM,YAAW,IAAI;AAC7C,QAAM,KAAK,IAAI,gBAAAG,QAAO,QAAQ,MAAM;AACpC,MAAI,eAAe,YAAY,MAAM,SAAS;AAC5C,OAAG,IAAI,IAAI,gBAAAA,QAAO,MAAM,CAAC;AACzB,OAAG,GAAG,cAAc,CAAC,MAAM,WAAW,OAAO,OAAO,CAAC,CAAC;AACtD,OAAG,GAAG,SAAS,CAAC,MAAM,YAAY,OAAO,OAAO,CAAC,CAAC;AAClD,OAAG,GAAG,YAAY,CAAC,MAAM,SAAS,OAAO,OAAO,CAAC,CAAC;AAAA,EACpD;AACA,MAAI,cAAc,WAAW,SAAS;AACpC,OAAG,IAAI,IAAI,gBAAAA,QAAO,IAAI;AAAA,MACpB,WAAW,WAAW;AAAA,MACtB,QAAQ,cAAc,OAAO,KAAK;AAAA,IACpC,CAAC,CAAC;AACF,OAAG,GAAG,YAAY,CAAC,MAAM,SAAS,OAAO,OAAO,CAAC,CAAC;AAClD,OAAG,GAAG,WAAW,CAAC,MAAM,UAAU,OAAO,OAAO,CAAC,CAAC;AAClD,OAAG,GAAG,UAAU,MAAM,OAAO,OAAO,KAAK,CAAC;AAAA,EAC5C;AACA,UAAQ,IAAI,OAAO,EAAE;AACvB;AACA,SAAS,WAAW,OAAO;AACzB,QAAM,KAAK,QAAQ,IAAI,KAAK;AAC5B,MAAI,IAAI;AACN,OAAG,OAAO,YAAY;AACtB,OAAG,OAAO,OAAO;AACjB,OAAG,OAAO,UAAU;AACpB,OAAG,OAAO,UAAU;AACpB,OAAG,OAAO,KAAK;AACf,OAAG,OAAO,QAAQ;AAClB,OAAG,QAAQ;AACX,YAAQ,OAAO,KAAK;AAAA,EACtB;AACF;AACA,SAAS,qBAAqB,YAAY,YAAY;AACpD,QAAM,EAAC,KAAK,QAAQ,MAAM,QAAO,IAAI;AACrC,QAAM,EAAC,KAAK,QAAQ,MAAM,QAAO,IAAI;AACrC,MAAI,SAAS,MAAM,OAAO,YAAY,SAAS,MAAM,OAAO,SAAS;AACnE,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,YAAY,QAAQ,SAAS;AACvC,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,cAAc,QAAQ,WAAW;AAC3C,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAI,UAAU;AAEd,SAAS,KAAK,OAAO,QAAQ,SAAS;AACpC,QAAM,cAAc,QAAQ,KAAK;AACjC,QAAM,EAAC,WAAW,QAAO,IAAI,SAAS,KAAK;AAC3C,MAAI,YAAY,aAAa,UAAU,CAAC,SAAS;AAC/C;AAAA,EACF;AACA,QAAM,EAAC,MAAM,KAAK,OAAO,OAAM,IAAI,gBAAgB,OAAO,QAAQ,KAAK,MAAM,EAAC,WAAW,QAAO,GAAG,YAAY,mBAAmB;AAClI,QAAM,MAAM,MAAM;AAClB,MAAI,KAAK;AACT,MAAI,UAAU;AACd,MAAI,YAAY,YAAY,mBAAmB;AAC/C,MAAI,SAAS,MAAM,KAAK,OAAO,MAAM;AACrC,MAAI,YAAY,cAAc,GAAG;AAC/B,QAAI,YAAY,YAAY;AAC5B,QAAI,cAAc,YAAY,eAAe;AAC7C,QAAI,WAAW,MAAM,KAAK,OAAO,MAAM;AAAA,EACzC;AACA,MAAI,QAAQ;AACd;AACA,IAAI,SAAS;AAAA,EACX,IAAI;AAAA,EACJ;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,WAAW;AAAA,MACX,aAAa;AAAA,IACf;AAAA,IACA,MAAM;AAAA,MACJ,OAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,MACf;AAAA,MACA,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,MACA,OAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,MACA,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,OAAO,SAAS,OAAO,OAAO,SAAS;AACrC,UAAM,QAAQ,SAAS,KAAK;AAC5B,UAAM,UAAU;AAChB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,MAAM,SAAS,GAAG;AACjE,cAAQ,KAAK,kIAAkI;AAAA,IACjJ;AACA,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,MAAM,eAAe,KACjE,OAAO,UAAU,eAAe,KAAK,QAAQ,KAAK,eAAe,GAAG;AACvE,cAAQ,KAAK,0GAA0G;AAAA,IACzH;AACA,QAAI,gBAAAA,SAAQ;AACV,kBAAY,OAAO,OAAO;AAAA,IAC5B;AACA,UAAM,MAAM,CAAC,OAAO,WAAW,eAAe,IAAI,OAAO,OAAO,WAAW,UAAU;AACrF,UAAM,OAAO,CAAC,MAAM,eAAe,KAAK,OAAO,MAAM,UAAU;AAC/D,UAAM,WAAW,CAAC,IAAI,IAAI,eAAe,SAAS,OAAO,IAAI,IAAI,UAAU;AAC3E,UAAM,YAAY,CAAC,IAAI,OAAO,eAAe,UAAU,OAAO,IAAI,OAAO,UAAU;AACnF,UAAM,YAAY,CAAC,eAAe,UAAU,OAAO,UAAU;AAC7D,UAAM,eAAe,MAAM,aAAa,KAAK;AAC7C,UAAM,wBAAwB,MAAM,sBAAsB,KAAK;AAC/D,UAAM,uBAAuB,MAAM,qBAAqB,KAAK;AAC7D,UAAM,mBAAmB,MAAM,iBAAiB,KAAK;AACrD,UAAM,qBAAqB,MAAM,mBAAmB,KAAK;AAAA,EAC3D;AAAA,EACA,YAAY,OAAO,EAAC,MAAK,GAAG;AAC1B,QAAI,mBAAmB,KAAK,GAAG;AAC7B,aAAO;AAAA,IACT;AACA,QAAI,MAAM,SAAS,WAAW,MAAM,SAAS,WAAW;AACtD,YAAM,QAAQ,SAAS,KAAK;AAC5B,UAAI,MAAM,iBAAiB;AACzB,cAAM,kBAAkB;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,SAAS,OAAO,MAAM,SAAS;AAC3C,UAAM,QAAQ,SAAS,KAAK;AAC5B,UAAM,kBAAkB,MAAM;AAC9B,UAAM,UAAU;AAChB,QAAI,qBAAqB,iBAAiB,OAAO,GAAG;AAClD,iBAAW,KAAK;AAChB,kBAAY,OAAO,OAAO;AAAA,IAC5B;AACA,iBAAa,OAAO,OAAO;AAAA,EAC7B;AAAA,EACA,mBAAmB,OAAO,OAAO,SAAS;AACxC,SAAK,OAAO,sBAAsB,OAAO;AAAA,EAC3C;AAAA,EACA,kBAAkB,OAAO,OAAO,SAAS;AACvC,SAAK,OAAO,qBAAqB,OAAO;AAAA,EAC1C;AAAA,EACA,WAAW,OAAO,OAAO,SAAS;AAChC,SAAK,OAAO,cAAc,OAAO;AAAA,EACnC;AAAA,EACA,UAAU,OAAO,OAAO,SAAS;AAC/B,SAAK,OAAO,aAAa,OAAO;AAAA,EAClC;AAAA,EACA,MAAM,SAAS,OAAO;AACpB,oBAAgB,KAAK;AACrB,QAAI,gBAAAA,SAAQ;AACV,iBAAW,KAAK;AAAA,IAClB;AACA,gBAAY,KAAK;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": ["window", "document", "undefined", "each", "assign", "extend", "merge", "callback", "self", "getCenter", "Hammer", "debounce", "zoom", "pan", "Hammer"]}