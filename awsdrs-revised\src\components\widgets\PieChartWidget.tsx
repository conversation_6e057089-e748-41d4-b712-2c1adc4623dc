import { Card, Text, Group, ActionIcon, Tooltip } from '@mantine/core';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend } from 'recharts';
import { IconRefresh, IconDownload } from '@tabler/icons-react';

interface PieChartWidgetProps {
  title: string;
  data: any[];
  colors: string[];
  height?: number;
  onRefresh?: () => void;
  onExport?: () => void;
  withBorder?: boolean;
  withShadow?: boolean;
}

const PieChartWidget = ({
  title,
  data,
  colors,
  height = 300,
  onRefresh,
  onExport,
  withBorder = true,
  withShadow = true
}: PieChartWidgetProps) => {
  // Consistent tooltip styling to match header row tooltips
  const tooltipStyles = {
    tooltip: {
      fontSize: '11px',
      padding: '4px 8px',
      fontWeight: 400
    }
  };

  return (
    <Card shadow={withShadow ? "sm" : "none"} padding="lg" radius="md" withBorder={withBorder}>
      <Group justify="space-between" mb="md">
        <Text size="lg" fw={600}>
          {title}
        </Text>
        <Group gap="xs">
          {onRefresh && (
            <Tooltip label="Refresh chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onRefresh}>
                <IconRefresh size={16} />
              </ActionIcon>
            </Tooltip>
          )}
          {onExport && (
            <Tooltip label="Export chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onExport}>
                <IconDownload size={16} />
              </ActionIcon>
            </Tooltip>
          )}
        </Group>
      </Group>

      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
            ))}
          </Pie>
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </Card>
  );
};

export default PieChartWidget;
