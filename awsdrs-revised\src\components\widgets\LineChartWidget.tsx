import { Card, Text, Group, ActionIcon, Tooltip } from '@mantine/core';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Legend } from 'recharts';
import { IconRefresh, IconDownload } from '@tabler/icons-react';

interface LineChartWidgetProps {
  title: string;
  data: any[];
  lines: {
    dataKey: string;
    stroke: string;
    name: string;
  }[];
  height?: number;
  onRefresh?: () => void;
  onExport?: () => void;
  withBorder?: boolean;
  withShadow?: boolean;
}

const LineChartWidget = ({
  title,
  data,
  lines,
  height = 300,
  onRefresh,
  onExport,
  withBorder = true,
  withShadow = true
}: LineChartWidgetProps) => {
  // Consistent tooltip styling to match header row tooltips
  const tooltipStyles = {
    tooltip: {
      fontSize: '11px',
      padding: '4px 8px',
      fontWeight: 400
    }
  };

  return (
    <Card shadow={withShadow ? "sm" : "none"} padding="lg" radius="md" withBorder={withBorder}>
      <Group justify="space-between" mb="md">
        <Text size="lg" fw={600}>
          {title}
        </Text>
        <Group gap="xs">
          {onRefresh && (
            <Tooltip label="Refresh chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onRefresh}>
                <IconRefresh size={16} />
              </ActionIcon>
            </Tooltip>
          )}
          {onExport && (
            <Tooltip label="Export chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onExport}>
                <IconDownload size={16} />
              </ActionIcon>
            </Tooltip>
          )}
        </Group>
      </Group>

      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
          <XAxis 
            dataKey="name" 
            tick={{ fontSize: 12 }}
            axisLine={{ stroke: 'var(--mantine-color-gray-4)' }}
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            axisLine={{ stroke: 'var(--mantine-color-gray-4)' }}
          />
          <Legend />
          {lines.map((line, index) => (
            <Line
              key={index}
              type="monotone"
              dataKey={line.dataKey}
              stroke={line.stroke}
              strokeWidth={2}
              name={line.name}
              dot={{ r: 4 }}
              activeDot={{ r: 6 }}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </Card>
  );
};

export default LineChartWidget;
