import { useState, useEffect, useCallback } from "react";
import { Text, Card, Group, Grid, Badge, Button, Progress, Stack } from "@mantine/core";
import { IconServer, IconClock, IconCheck } from "@tabler/icons-react";
import DashboardLayout from "../layouts/DashboardLayout";
import WidgetMenu from "../components/WidgetMenu";
import { useLayoutManager } from "../hooks/useLayoutManager";
import { Responsive, WidthProvider } from 'react-grid-layout';

const ResponsiveGridLayout = WidthProvider(Responsive);

// Define Recovery widgets
interface RecoveryWidget {
  id: string;
  title: string;
  type: 'rto-rpo' | 'active-operations' | 'failover-status' | 'backup-health' | 'test-results' | 'recovery-sites' | 'alerts' | 'timeline';
}

// Default widgets configuration
const defaultRecoveryWidgets: RecoveryWidget[] = [
  { id: 'rto-rpo-metrics', title: 'RTO/RPO Metrics', type: 'rto-rpo' },
  { id: 'active-operations', title: 'Active Operations', type: 'active-operations' },
  { id: 'failover-status', title: 'Failover Status', type: 'failover-status' },
  { id: 'backup-health', title: 'Backup Health', type: 'backup-health' },
  { id: 'test-results', title: 'DR Test Results', type: 'test-results' },
  { id: 'recovery-sites', title: 'Recovery Sites', type: 'recovery-sites' },
  { id: 'recovery-alerts', title: 'Recovery Alerts', type: 'alerts' },
  { id: 'recovery-timeline', title: 'Recovery Timeline', type: 'timeline' },
];

// Default layouts configuration - smaller, more focused widgets
const defaultRecoveryLayouts = {
  lg: [
    { i: 'rto-rpo-metrics', x: 0, y: 0, w: 8, h: 4, minW: 6, minH: 3 }, // One larger widget for key metrics
    { i: 'active-operations', x: 8, y: 0, w: 4, h: 4, minW: 3, minH: 3 },
    { i: 'failover-status', x: 0, y: 4, w: 3, h: 3, minW: 3, minH: 3 },
    { i: 'backup-health', x: 3, y: 4, w: 3, h: 3, minW: 3, minH: 3 },
    { i: 'test-results', x: 6, y: 4, w: 3, h: 3, minW: 3, minH: 3 },
    { i: 'recovery-sites', x: 9, y: 4, w: 3, h: 3, minW: 3, minH: 3 },
    { i: 'recovery-alerts', x: 0, y: 7, w: 6, h: 3, minW: 4, minH: 3 },
    { i: 'recovery-timeline', x: 6, y: 7, w: 6, h: 3, minW: 4, minH: 3 },
  ],
  md: [
    { i: 'rto-rpo-metrics', x: 0, y: 0, w: 6, h: 4, minW: 4, minH: 3 },
    { i: 'active-operations', x: 0, y: 4, w: 3, h: 3, minW: 3, minH: 3 },
    { i: 'failover-status', x: 3, y: 4, w: 3, h: 3, minW: 3, minH: 3 },
    { i: 'backup-health', x: 0, y: 7, w: 3, h: 3, minW: 3, minH: 3 },
    { i: 'test-results', x: 3, y: 7, w: 3, h: 3, minW: 3, minH: 3 },
    { i: 'recovery-sites', x: 0, y: 10, w: 3, h: 3, minW: 3, minH: 3 },
    { i: 'recovery-alerts', x: 3, y: 10, w: 3, h: 3, minW: 3, minH: 3 },
    { i: 'recovery-timeline', x: 0, y: 13, w: 6, h: 3, minW: 4, minH: 3 },
  ],
  sm: [
    { i: 'rto-rpo-metrics', x: 0, y: 0, w: 4, h: 4, minW: 4, minH: 3 },
    { i: 'active-operations', x: 0, y: 4, w: 2, h: 3, minW: 2, minH: 3 },
    { i: 'failover-status', x: 2, y: 4, w: 2, h: 3, minW: 2, minH: 3 },
    { i: 'backup-health', x: 0, y: 7, w: 2, h: 3, minW: 2, minH: 3 },
    { i: 'test-results', x: 2, y: 7, w: 2, h: 3, minW: 2, minH: 3 },
    { i: 'recovery-sites', x: 0, y: 10, w: 2, h: 3, minW: 2, minH: 3 },
    { i: 'recovery-alerts', x: 2, y: 10, w: 2, h: 3, minW: 2, minH: 3 },
    { i: 'recovery-timeline', x: 0, y: 13, w: 4, h: 3, minW: 4, minH: 3 },
  ],
  xs: [
    { i: 'rto-rpo-metrics', x: 0, y: 0, w: 2, h: 4, minW: 2, minH: 3 },
    { i: 'active-operations', x: 0, y: 4, w: 2, h: 3, minW: 2, minH: 3 },
    { i: 'failover-status', x: 0, y: 7, w: 2, h: 3, minW: 2, minH: 3 },
    { i: 'backup-health', x: 0, y: 10, w: 2, h: 3, minW: 2, minH: 3 },
    { i: 'test-results', x: 0, y: 13, w: 2, h: 3, minW: 2, minH: 3 },
    { i: 'recovery-sites', x: 0, y: 16, w: 2, h: 3, minW: 2, minH: 3 },
    { i: 'recovery-alerts', x: 0, y: 19, w: 2, h: 3, minW: 2, minH: 3 },
    { i: 'recovery-timeline', x: 0, y: 22, w: 2, h: 3, minW: 2, minH: 3 },
  ]
};

// Helper function for initial menu position
const getInitialMenuPosition = () => ({
  x: window.innerWidth - 320,
  y: 100
});

// Widget factory function
const renderRecoveryWidget = (widget: RecoveryWidget) => {
  switch (widget.id) {
    case 'rto-rpo-metrics':
      return (
        <Card shadow="sm" p="md" h="100%">
          <Group justify="space-between" mb="md">
            <Text fw={600} size="lg">RTO/RPO Metrics</Text>
            <Badge color="blue" variant="light">Compliant</Badge>
          </Group>
          <Grid>
            <Grid.Col span={6}>
              <Stack gap="xs" align="center">
                <Text size="xs" c="dimmed" tt="uppercase">RTO Target</Text>
                <Text size="xl" fw={700} c="blue">4h</Text>
                <Text size="xs" c="green">✓ Within target</Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={6}>
              <Stack gap="xs" align="center">
                <Text size="xs" c="dimmed" tt="uppercase">RPO Target</Text>
                <Text size="xl" fw={700} c="blue">15m</Text>
                <Text size="xs" c="green">✓ Within target</Text>
              </Stack>
            </Grid.Col>
          </Grid>
          <Progress value={92} color="blue" size="sm" mt="md" />
          <Text size="xs" c="dimmed" ta="center" mt="xs">92% SLA Compliance</Text>
        </Card>
      );
    case 'active-operations':
      return (
        <Card shadow="sm" p="md" h="100%">
          <Group justify="space-between" mb="sm">
            <Text fw={600} size="md">Active Operations</Text>
            <Badge color="blue" size="sm">3 Running</Badge>
          </Group>
          <Stack gap="xs">
            <Group justify="space-between">
              <Group gap="xs">
                <IconCheck size={14} color="green" />
                <Text size="sm">DB Backup</Text>
              </Group>
              <Text size="xs" c="green">Complete</Text>
            </Group>
            <Group justify="space-between">
              <Group gap="xs">
                <IconClock size={14} color="orange" />
                <Text size="sm">File Sync</Text>
              </Group>
              <Text size="xs" c="orange">Running</Text>
            </Group>
            <Group justify="space-between">
              <Group gap="xs">
                <IconServer size={14} color="blue" />
                <Text size="sm">VM Replication</Text>
              </Group>
              <Text size="xs" c="blue">Scheduled</Text>
            </Group>
          </Stack>
        </Card>
      );
    case 'failover-status':
      return (
        <Card shadow="sm" p="md" h="100%">
          <Group justify="space-between" mb="sm">
            <Text fw={600} size="md">Failover Status</Text>
            <Badge color="green" size="sm">Ready</Badge>
          </Group>
          <Stack gap="xs">
            <Group justify="space-between">
              <Text size="sm">Primary Site</Text>
              <Badge color="green" size="xs">Online</Badge>
            </Group>
            <Group justify="space-between">
              <Text size="sm">DR Site</Text>
              <Badge color="orange" size="xs">Standby</Badge>
            </Group>
            <Group justify="space-between">
              <Text size="sm">Last Test</Text>
              <Text size="xs" c="dimmed">3 days ago</Text>
            </Group>
            <Button variant="light" color="blue" size="xs" fullWidth mt="xs">
              Test Failover
            </Button>
          </Stack>
        </Card>
      );

    case 'backup-health':
      return (
        <Card shadow="sm" p="md" h="100%">
          <Group justify="space-between" mb="sm">
            <Text fw={600} size="md">Backup Health</Text>
            <Badge color="green" size="sm">Healthy</Badge>
          </Group>
          <Stack gap="xs">
            <Group justify="space-between">
              <Text size="sm">Database</Text>
              <IconCheck size={16} color="green" />
            </Group>
            <Group justify="space-between">
              <Text size="sm">Files</Text>
              <IconCheck size={16} color="green" />
            </Group>
            <Group justify="space-between">
              <Text size="sm">Configs</Text>
              <IconClock size={16} color="orange" />
            </Group>
            <Text size="xs" c="dimmed" mt="xs">Last backup: 2 hours ago</Text>
          </Stack>
        </Card>
      );

    case 'test-results':
      return (
        <Card shadow="sm" p="md" h="100%">
          <Group justify="space-between" mb="sm">
            <Text fw={600} size="md">DR Test Results</Text>
            <Badge color="green" size="sm">Passed</Badge>
          </Group>
          <Stack gap="xs">
            <Group justify="space-between">
              <Text size="sm">Network Test</Text>
              <Text size="xs" c="green">✓ Pass</Text>
            </Group>
            <Group justify="space-between">
              <Text size="sm">Data Integrity</Text>
              <Text size="xs" c="green">✓ Pass</Text>
            </Group>
            <Group justify="space-between">
              <Text size="sm">App Recovery</Text>
              <Text size="xs" c="orange">⚠ Warning</Text>
            </Group>
            <Text size="xs" c="dimmed" mt="xs">Next test: Jan 15, 2025</Text>
          </Stack>
        </Card>
      );

    case 'recovery-sites':
      return (
        <Card shadow="sm" p="md" h="100%">
          <Group justify="space-between" mb="sm">
            <Text fw={600} size="md">Recovery Sites</Text>
            <Text size="xs" c="dimmed">3 Sites</Text>
          </Group>
          <Stack gap="xs">
            <Group justify="space-between">
              <Text size="sm">US East</Text>
              <Badge color="green" size="xs">Primary</Badge>
            </Group>
            <Group justify="space-between">
              <Text size="sm">US West</Text>
              <Badge color="orange" size="xs">DR</Badge>
            </Group>
            <Group justify="space-between">
              <Text size="sm">EU West</Text>
              <Badge color="gray" size="xs">Cold</Badge>
            </Group>
            <Progress value={75} color="blue" size="xs" mt="xs" />
            <Text size="xs" c="dimmed">75% Capacity Available</Text>
          </Stack>
        </Card>
      );
    case 'recovery-alerts':
      return (
        <Card shadow="sm" p="md" h="100%">
          <Group justify="space-between" mb="sm">
            <Text fw={600} size="md">Recovery Alerts</Text>
            <Badge color="orange" size="sm">2 Active</Badge>
          </Group>
          <Stack gap="xs">
            <Group justify="space-between" p="xs" style={{ backgroundColor: 'var(--mantine-color-orange-0)', borderRadius: '4px' }}>
              <Text size="sm">Backup delay detected</Text>
              <Text size="xs" c="orange">Warning</Text>
            </Group>
            <Group justify="space-between" p="xs" style={{ backgroundColor: 'var(--mantine-color-red-0)', borderRadius: '4px' }}>
              <Text size="sm">Replication lag high</Text>
              <Text size="xs" c="red">Critical</Text>
            </Group>
            <Text size="xs" c="dimmed" mt="xs">Last updated: 5 minutes ago</Text>
          </Stack>
        </Card>
      );

    case 'recovery-timeline':
      return (
        <Card shadow="sm" p="md" h="100%">
          <Group justify="space-between" mb="sm">
            <Text fw={600} size="md">Recovery Timeline</Text>
            <Text size="xs" c="dimmed">Last 24h</Text>
          </Group>
          <Stack gap="xs">
            <Group justify="space-between">
              <Text size="sm">14:30 - Backup completed</Text>
              <IconCheck size={14} color="green" />
            </Group>
            <Group justify="space-between">
              <Text size="sm">12:15 - DR test passed</Text>
              <IconCheck size={14} color="green" />
            </Group>
            <Group justify="space-between">
              <Text size="sm">09:45 - Sync started</Text>
              <IconClock size={14} color="blue" />
            </Group>
            <Group justify="space-between">
              <Text size="sm">08:00 - Health check</Text>
              <IconCheck size={14} color="green" />
            </Group>
          </Stack>
        </Card>
      );
    default:
      return (
        <Card h="100%" p="md">
          <Text>Unknown widget: {widget.id}</Text>
        </Card>
      );
  }
};

// Main Recovery Component
const Recovery = () => {
  // Layout management with multiple presets
  const {
    layouts,
    layoutPresets,
    currentLayoutId,
    switchToLayout,
    saveCurrentLayout,
    deleteLayout,
    resetToDefault,
    handleLayoutChange
  } = useLayoutManager({
    pageKey: 'recovery',
    defaultLayouts: defaultRecoveryLayouts,
    defaultLayoutName: 'Default Recovery Layout'
  });

  // State management (standard for all pages)
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [widgets] = useState(defaultRecoveryWidgets);
  const [visibleWidgets, setVisibleWidgets] = useState(new Set(defaultRecoveryWidgets.map(w => w.id)));
  const [showWidgetMenu, setShowWidgetMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState(getInitialMenuPosition());
  const [isDragging, setIsDragging] = useState(false);

  // Auto-refresh data
  useEffect(() => {
    const interval = setInterval(() => {
      console.log('Auto-refreshing recovery data...');
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Load saved widget visibility
  useEffect(() => {
    try {
      const savedVisibility = localStorage.getItem('recovery-visible-widgets');
      if (savedVisibility) {
        setVisibleWidgets(new Set(JSON.parse(savedVisibility)));
      }
    } catch (error) {
      console.error('Error loading widget visibility:', error);
    }
  }, []);

  // Widget visibility toggle
  const toggleWidgetVisibility = (widgetId: string) => {
    setVisibleWidgets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(widgetId)) {
        newSet.delete(widgetId);
      } else {
        newSet.add(widgetId);
      }
      localStorage.setItem('recovery-visible-widgets', JSON.stringify([...newSet]));
      return newSet;
    });
  };

  const closeWidgetMenu = () => {
    setShowWidgetMenu(false);
  };

  const resetToDefaultAndClose = () => {
    resetToDefault();
    setShowWidgetMenu(false);
    setMenuPosition(getInitialMenuPosition());
  };

  // Optimized drag handlers for floating menu
  const handleMouseDown = (e: React.MouseEvent) => {
    // Prevent dragging when clicking on interactive elements
    const target = e.target as HTMLElement;
    if (target.tagName === 'BUTTON' || target.tagName === 'INPUT' || target.tagName === 'SELECT' ||
        target.closest('button') || target.closest('[role="button"]') || target.closest('input') || target.closest('select')) {
      return;
    }

    setIsDragging(true);
    const startX = e.clientX - menuPosition.x;
    const startY = e.clientY - menuPosition.y;

    const handleMouseMove = (e: MouseEvent) => {
      const newX = Math.max(0, Math.min(window.innerWidth - 300, e.clientX - startX));
      const newY = Math.max(0, Math.min(window.innerHeight - 200, e.clientY - startY));

      setMenuPosition({ x: newX, y: newY });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };



  // Handle refresh
  const handleRefresh = useCallback(() => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1000);
  }, []);

  // Filter visible widgets
  const allWidgets = widgets.filter(widget => visibleWidgets.has(widget.id));

  // Dashboard controls
  const dashboardControls = {
    isEditing,
    loading,
    showWidgetMenu,
    onToggleEdit: () => setIsEditing(!isEditing),
    onRefresh: handleRefresh,
    onResetLayout: resetToDefaultAndClose,
    onToggleWidgetMenu: () => {
      if (!showWidgetMenu) {
        setMenuPosition(getInitialMenuPosition());
      }
      setShowWidgetMenu(!showWidgetMenu);
    }
  };

  return (
    <DashboardLayout dashboardControls={dashboardControls}>
      <div style={{ width: '100%', position: 'relative', maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ flex: 1, overflow: 'auto', paddingTop: '40px', padding: '40px 24px 24px 24px' }}>

          {/* Draggable Grid Layout */}
          <ResponsiveGridLayout
            className="layout"
            layouts={layouts}
            onLayoutChange={(layout, layouts) => {
              if (isEditing) {
                handleLayoutChange(layout, layouts);
              }
            }}
            isDraggable={isEditing}
            isResizable={isEditing}
            breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480 }}
            cols={{ lg: 12, md: 6, sm: 4, xs: 2 }}
            rowHeight={60}
            margin={[16, 16]}
            containerPadding={[0, 0]}
            useCSSTransforms={true}
            preventCollision={false}
            compactType="vertical"
          >
            {allWidgets.map((widget) => {
              return (
                <div key={widget.id} style={{ position: 'relative' }}>
                  {renderRecoveryWidget(widget)}
                </div>
              );
            })}
          </ResponsiveGridLayout>
        </div>

        {/* Floating Widget Selection Menu */}
        {showWidgetMenu && (
          <WidgetMenu
            widgets={widgets}
            visibleWidgets={visibleWidgets}
            onToggleWidgetVisibility={toggleWidgetVisibility}
            layoutPresets={layoutPresets}
            currentLayoutId={currentLayoutId}
            onLayoutChange={switchToLayout}
            onSaveLayout={saveCurrentLayout}
            onDeleteLayout={deleteLayout}
            onResetToDefault={resetToDefaultAndClose}
            menuPosition={menuPosition}
            isDragging={isDragging}
            onMouseDown={handleMouseDown}
            onClose={closeWidgetMenu}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default Recovery;
