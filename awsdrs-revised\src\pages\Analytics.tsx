import { useState, useEffect, useCallback } from "react";
import { Text, Card } from "@mantine/core";
import { IconServer, IconClock, IconShield, IconDatabase } from "@tabler/icons-react";
import DashboardLayout from "../layouts/DashboardLayout";
import { Metric<PERSON>ard, LineChartWidget, BarChartWidget, PieChartWidget } from "../components/widgets";
import WidgetMenu from "../components/WidgetMenu";
import { useLayoutManager } from "../hooks/useLayoutManager";
import dataService from "../services/dataService";
import { Responsive, WidthProvider } from 'react-grid-layout';
import type { Layout } from 'react-grid-layout';

const ResponsiveGridLayout = WidthProvider(Responsive);

// Define Analytics widgets (like Dashboard pattern)
interface AnalyticsWidget {
  id: string;
  title: string;
  type: string;
}

const defaultAnalyticsWidgets: AnalyticsWidget[] = [
  { id: 'servers-metric', title: 'Active Servers', type: 'servers-metric' },
  { id: 'lag-metric', title: 'Replication Lag', type: 'lag-metric' },
  { id: 'data-metric', title: 'Data Replicated', type: 'data-metric' },
  { id: 'health-metric', title: 'System Health', type: 'health-metric' },
  { id: 'lag-trend', title: 'Replication Lag Trend', type: 'lag-trend' },
  { id: 'region-distribution', title: 'Regional Distribution', type: 'region-distribution' },
  { id: 'server-status-chart', title: 'Server Status Trends', type: 'server-status-chart' }
];

// Default layout configuration (like Dashboard pattern)
const defaultAnalyticsLayouts = {
  lg: [
    { i: 'servers-metric', x: 0, y: 0, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'lag-metric', x: 3, y: 0, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'data-metric', x: 6, y: 0, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'health-metric', x: 9, y: 0, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'lag-trend', x: 0, y: 3, w: 6, h: 5, minW: 4, minH: 4 },
    { i: 'region-distribution', x: 6, y: 3, w: 6, h: 5, minW: 3, minH: 4 },
    { i: 'server-status-chart', x: 0, y: 8, w: 12, h: 4, minW: 6, minH: 3 }
  ],
  md: [
    { i: 'servers-metric', x: 0, y: 0, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'lag-metric', x: 3, y: 0, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'data-metric', x: 0, y: 3, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'health-metric', x: 3, y: 3, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'lag-trend', x: 0, y: 6, w: 6, h: 5, minW: 4, minH: 4 },
    { i: 'region-distribution', x: 0, y: 11, w: 6, h: 5, minW: 3, minH: 4 },
    { i: 'server-status-chart', x: 0, y: 16, w: 6, h: 4, minW: 6, minH: 3 }
  ],
  sm: [
    { i: 'servers-metric', x: 0, y: 0, w: 4, h: 3, minW: 4, minH: 3 },
    { i: 'lag-metric', x: 0, y: 3, w: 4, h: 3, minW: 4, minH: 3 },
    { i: 'data-metric', x: 0, y: 6, w: 4, h: 3, minW: 4, minH: 3 },
    { i: 'health-metric', x: 0, y: 9, w: 4, h: 3, minW: 4, minH: 3 },
    { i: 'lag-trend', x: 0, y: 12, w: 4, h: 5, minW: 4, minH: 4 },
    { i: 'region-distribution', x: 0, y: 17, w: 4, h: 5, minW: 4, minH: 4 },
    { i: 'server-status-chart', x: 0, y: 22, w: 4, h: 4, minW: 4, minH: 3 }
  ]
};

// Widget factory function (like Dashboard pattern)
const renderAnalyticsWidget = (widget: AnalyticsWidget) => {
  const analyticsData = dataService.getAnalyticsMetrics();
  const systemResourceData = dataService.getSystemResourceData();
  const systemHealthData = dataService.getSystemHealthData();
  const networkTrafficData = dataService.getNetworkTrafficData();

  switch (widget.type) {
    case 'servers-metric':
      return (
        <div style={{ height: '100%', padding: '16px' }}>
          <MetricCard
            title="Active Servers"
            value={24}
            change={2}
            changeType="increase"
            icon={<IconServer size={20} />}
            color="blue"
            subtitle="Currently online"
          />
        </div>
      );
    case 'lag-metric':
      return (
        <div style={{ height: '100%', padding: '16px' }}>
          <MetricCard
            title="Avg Replication Lag"
            value="2.3s"
            change={-0.5}
            changeType="decrease"
            icon={<IconClock size={20} />}
            color="orange"
            subtitle="Last 24 hours"
          />
        </div>
      );
    case 'data-metric':
      return (
        <div style={{ height: '100%', padding: '16px' }}>
          <MetricCard
            title="Data Replicated"
            value="1.2TB"
            change={156}
            changeType="increase"
            icon={<IconDatabase size={20} />}
            color="cyan"
            subtitle="Today"
          />
        </div>
      );
    case 'health-metric':
      return (
        <div style={{ height: '100%', padding: '16px' }}>
          <MetricCard
            title="System Health"
            value="98.5%"
            change={0.2}
            changeType="increase"
            icon={<IconShield size={20} />}
            color="green"
            subtitle="Overall uptime"
          />
        </div>
      );
    case 'lag-trend':
      return (
        <div style={{ height: '100%', padding: '8px' }}>
          <LineChartWidget
            title="Replication Lag Trend (24h)"
            data={systemResourceData}
            lines={[
              { dataKey: 'cpu', stroke: '#3b82f6', name: 'Lag (seconds)' }
            ]}
            height={280}
            onRefresh={() => console.log('Refreshing lag trend...')}
            onExport={() => console.log('Exporting lag trend...')}
            withBorder={false}
            withShadow={false}
          />
        </div>
      );
    case 'region-distribution':
      return (
        <div style={{ height: '100%', padding: '8px' }}>
          <PieChartWidget
            title="Regional Distribution"
            data={systemHealthData}
            colors={['#3b82f6', '#f59e0b', '#10b981', '#ef4444', '#8b5cf6']}
            height={280}
            onRefresh={() => console.log('Refreshing region data...')}
            onExport={() => console.log('Exporting region data...')}
            withBorder={false}
            withShadow={false}
          />
        </div>
      );
    case 'server-status-chart':
      return (
        <div style={{ height: '100%', padding: '8px' }}>
          <BarChartWidget
            title="Server Status Trends (6 Months)"
            data={networkTrafficData}
            bars={[
              { dataKey: 'inbound', fill: '#10b981', name: 'Healthy Servers' },
              { dataKey: 'outbound', fill: '#8b5cf6', name: 'Warning Servers' }
            ]}
            height={200}
            onRefresh={() => console.log('Refreshing server status...')}
            onExport={() => console.log('Exporting server status...')}
            withBorder={false}
            withShadow={false}
          />
        </div>
      );
    default:
      return (
        <Card h="100%" p="md">
          <Text c="dimmed" ta="center">
            Widget: {widget.type}
          </Text>
        </Card>
      );
  }
};

const getInitialMenuPosition = () => ({
  x: Math.max(50, window.innerWidth - 370),
  y: 100
});

const Analytics = () => {
  // Layout management with multiple presets
  const {
    layouts,
    layoutPresets,
    currentLayoutId,
    switchToLayout,
    saveCurrentLayout,
    deleteLayout,
    resetToDefault,
    handleLayoutChange
  } = useLayoutManager({
    pageKey: 'analytics',
    defaultLayouts: defaultAnalyticsLayouts,
    defaultLayoutName: 'Default Analytics Layout'
  });

  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [widgets, setWidgets] = useState(defaultAnalyticsWidgets);
  const [visibleWidgets, setVisibleWidgets] = useState(new Set(defaultAnalyticsWidgets.map(w => w.id)));
  const [showWidgetMenu, setShowWidgetMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState(getInitialMenuPosition());
  const [isDragging, setIsDragging] = useState(false);

  // Auto-refresh data
  useEffect(() => {
    const interval = setInterval(() => {
      // Trigger re-render for fresh data
      setLoading(false);
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Load saved widget visibility
  useEffect(() => {
    try {
      const savedVisibility = localStorage.getItem('analytics-visible-widgets');
      if (savedVisibility) {
        setVisibleWidgets(new Set(JSON.parse(savedVisibility)));
      }
    } catch (error) {
      console.error('Error loading widget visibility:', error);
    }
  }, []);

  const toggleWidgetVisibility = (widgetId: string) => {
    setVisibleWidgets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(widgetId)) {
        newSet.delete(widgetId);
      } else {
        newSet.add(widgetId);
      }
      localStorage.setItem('analytics-visible-widgets', JSON.stringify([...newSet]));
      return newSet;
    });
  };

  const closeWidgetMenu = () => {
    setShowWidgetMenu(false);
  };

  const resetToDefaultAndClose = () => {
    resetToDefault();
    setShowWidgetMenu(false);
    setMenuPosition(getInitialMenuPosition());
  };

  // Optimized drag handlers for floating menu
  const handleMouseDown = (e: React.MouseEvent) => {
    // Prevent dragging when clicking on interactive elements
    const target = e.target as HTMLElement;
    if (target.tagName === 'BUTTON' || target.tagName === 'INPUT' || target.tagName === 'SELECT' ||
        target.closest('button') || target.closest('[role="button"]') || target.closest('input') || target.closest('select')) {
      return;
    }

    setIsDragging(true);
    const startX = e.clientX - menuPosition.x;
    const startY = e.clientY - menuPosition.y;

    const handleMouseMove = (e: MouseEvent) => {
      const newX = Math.max(0, Math.min(window.innerWidth - 300, e.clientX - startX));
      const newY = Math.max(0, Math.min(window.innerHeight - 200, e.clientY - startY));

      setMenuPosition({ x: newX, y: newY });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };



  // Handle refresh
  const handleRefresh = useCallback(() => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1000);
  }, []);

  // Create allWidgets array (like Dashboard)
  const allWidgets = widgets.filter(widget => visibleWidgets.has(widget.id));

  const dashboardControls = {
    isEditing,
    loading,
    showWidgetMenu,
    onToggleEdit: () => setIsEditing(!isEditing),
    onRefresh: handleRefresh,
    onResetLayout: resetToDefaultAndClose,
    onToggleWidgetMenu: () => {
      if (!showWidgetMenu) {
        setMenuPosition(getInitialMenuPosition());
      }
      setShowWidgetMenu(!showWidgetMenu);
    }
  };

  return (
    <DashboardLayout dashboardControls={dashboardControls}>
      <div style={{ width: '100%', position: 'relative', maxWidth: '1400px', margin: '0 auto' }}>

        {/* Dashboard Grid */}
        <div style={{ flex: 1, overflow: 'auto', paddingTop: '40px' }}>
          <ResponsiveGridLayout
            className={`layout ${isEditing ? 'editing' : ''}`}
            layouts={layouts}
            onLayoutChange={(layout, layouts) => {
              if (isEditing) {
                handleLayoutChange(layout, layouts);
              }
            }}
            breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
            cols={{ lg: 9, md: 10, sm: 6, xs: 4, xxs: 2 }}
            rowHeight={60}
            isDraggable={isEditing}
            isResizable={isEditing}
            margin={[16, 16]}
            containerPadding={[16, 16]}
            useCSSTransforms={true}
            compactType="vertical"
            preventCollision={false}
            allowOverlap={false}
          >
            {allWidgets.map((widget) => {
              const isVisible = visibleWidgets.has(widget.id);
              return (
                <div
                  key={widget.id}
                  className="widget-container"
                  style={{
                    display: isVisible ? 'block' : 'none'
                  }}
                >
                  <Card
                    h="100%"
                    p={0}
                    withBorder
                    shadow="sm"
                    radius="md"
                    style={{
                      position: 'relative',
                      overflow: 'visible',
                      border: isEditing ? '2px solid var(--mantine-color-blue-4)' : undefined
                    }}
                  >
                    {renderAnalyticsWidget(widget)}
                  </Card>
                </div>
              );
            })}
          </ResponsiveGridLayout>
        </div>

        {/* Floating Widget Selection Menu */}
        {showWidgetMenu && (
          <WidgetMenu
            widgets={widgets}
            visibleWidgets={visibleWidgets}
            onToggleWidgetVisibility={toggleWidgetVisibility}
            layoutPresets={layoutPresets}
            currentLayoutId={currentLayoutId}
            onLayoutChange={switchToLayout}
            onSaveLayout={saveCurrentLayout}
            onDeleteLayout={deleteLayout}
            onResetToDefault={resetToDefaultAndClose}
            menuPosition={menuPosition}
            isDragging={isDragging}
            onMouseDown={handleMouseDown}
            onClose={closeWidgetMenu}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default Analytics;
