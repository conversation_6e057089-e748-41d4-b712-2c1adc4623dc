import React, { useEffect, useRef, useState } from 'react';
import { Card, Group, Text, ActionIcon, Tooltip, Switch, Badge } from '@mantine/core';
import { IconRefresh, IconDownload, IconZoomIn, IconZoomOut, IconTarget } from '@tabler/icons-react';
import * as d3 from 'd3';

interface DataPoint {
  time: string;
  value: number;
  cpu?: number;
  memory?: number;
  disk?: number;
  series?: string;
}

interface D3ChartAdvancedProps {
  title: string;
  data: DataPoint[];
  height?: number;
  onRefresh?: () => void;
  onExport?: () => void;
  minimal?: boolean;
  withBorder?: boolean;
  withShadow?: boolean;
}

const tooltipStyles = {
  tooltip: {
    fontSize: '12px',
  },
};

const D3ChartAdvanced: React.FC<D3ChartAdvancedProps> = ({
  title,
  data,
  height = 400,
  onRefresh,
  onExport,
  minimal = false,
  withBorder = true,
  withShadow = true,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [showMultipleSeries, setShowMultipleSeries] = useState(true);
  const [enableCrosshair, setEnableCrosshair] = useState(true);
  const [showTooltips, setShowTooltips] = useState(true);
  const [enableAnimation, setEnableAnimation] = useState(true);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [showThresholdLines, setShowThresholdLines] = useState(true);
  const [enableParticleEffect, setEnableParticleEffect] = useState(false);
  const [showDataFlow, setShowDataFlow] = useState(false);
  const [enableHeatmapOverlay, setEnableHeatmapOverlay] = useState(false);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 80, bottom: 40, left: 60 };
    const width = (containerRef.current?.clientWidth || 800) - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom - 100; // Account for controls

    // Set up scales - use point scale for time labels
    const processedData = data.map((d, i) => ({
      ...d,
      index: i,
      timeLabel: d.time,
    }));

    const xScale = d3.scaleLinear()
      .domain([0, processedData.length - 1])
      .range([0, width]);

    const maxValue = showMultipleSeries 
      ? d3.max(processedData, d => Math.max(d.value, d.cpu || 0, d.memory || 0, d.disk || 0)) || 100
      : d3.max(processedData, d => d.value) || 100;

    const yScale = d3.scaleLinear()
      .domain([0, maxValue])
      .range([chartHeight, 0]);

    const g = svg.append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Add gradient definitions
    const defs = svg.append('defs');
    
    const gradient = defs.append('linearGradient')
      .attr('id', 'area-gradient')
      .attr('gradientUnits', 'userSpaceOnUse')
      .attr('x1', 0).attr('y1', chartHeight)
      .attr('x2', 0).attr('y2', 0);
    
    gradient.append('stop')
      .attr('offset', '0%')
      .attr('stop-color', '#3b82f6')
      .attr('stop-opacity', 0.1);
    
    gradient.append('stop')
      .attr('offset', '100%')
      .attr('stop-color', '#3b82f6')
      .attr('stop-opacity', 0.6);

    // Add axes
    const xAxis = g.append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale)
        .tickFormat((d, i) => processedData[d as number]?.timeLabel || '')
        .ticks(Math.min(10, processedData.length))
      );

    const yAxis = g.append('g')
      .attr('class', 'y-axis')
      .call(d3.axisLeft(yScale));

    // Add grid lines
    g.append('g')
      .attr('class', 'grid')
      .attr('transform', `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale)
        .tickSize(-chartHeight)
        .tickFormat(() => '')
        .ticks(Math.min(10, processedData.length))
      )
      .style('stroke-dasharray', '3,3')
      .style('opacity', 0.3);

    g.append('g')
      .attr('class', 'grid')
      .call(d3.axisLeft(yScale)
        .tickSize(-width)
        .tickFormat(() => '')
      )
      .style('stroke-dasharray', '3,3')
      .style('opacity', 0.3);

    // Define line generators
    const line = d3.line<any>()
      .x(d => xScale(d.index))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    const area = d3.area<any>()
      .x(d => xScale(d.index))
      .y0(chartHeight)
      .y1(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Add area fill
    const areaPath = g.append('path')
      .datum(processedData)
      .attr('fill', 'url(#area-gradient)')
      .attr('d', area);

    // Add main line
    const mainPath = g.append('path')
      .datum(processedData)
      .attr('fill', 'none')
      .attr('stroke', '#3b82f6')
      .attr('stroke-width', 3)
      .attr('d', line);

    // Add multiple series if enabled
    if (showMultipleSeries) {
      const cpuLine = d3.line<any>()
        .x(d => xScale(d.index))
        .y(d => yScale(d.cpu || 0))
        .curve(d3.curveMonotoneX);

      const memoryLine = d3.line<any>()
        .x(d => xScale(d.index))
        .y(d => yScale(d.memory || 0))
        .curve(d3.curveMonotoneX);

      g.append('path')
        .datum(processedData)
        .attr('fill', 'none')
        .attr('stroke', '#f59e0b')
        .attr('stroke-width', 2)
        .attr('stroke-dasharray', '5,5')
        .attr('d', cpuLine);

      g.append('path')
        .datum(processedData)
        .attr('fill', 'none')
        .attr('stroke', '#10b981')
        .attr('stroke-width', 2)
        .attr('stroke-dasharray', '3,3')
        .attr('d', memoryLine);

      // Add legend
      const legend = g.append('g')
        .attr('class', 'legend')
        .attr('transform', `translate(${width - 70}, 10)`);

      const legendData = [
        { name: 'Network', color: '#3b82f6', dash: 'none' },
        { name: 'CPU', color: '#f59e0b', dash: '5,5' },
        { name: 'Memory', color: '#10b981', dash: '3,3' }
      ];

      legendData.forEach((item, i) => {
        const legendItem = legend.append('g')
          .attr('transform', `translate(0, ${i * 20})`);

        legendItem.append('line')
          .attr('x1', 0)
          .attr('x2', 15)
          .attr('y1', 0)
          .attr('y2', 0)
          .attr('stroke', item.color)
          .attr('stroke-width', 2)
          .attr('stroke-dasharray', item.dash);

        legendItem.append('text')
          .attr('x', 20)
          .attr('y', 0)
          .attr('dy', '0.35em')
          .style('font-size', '12px')
          .style('fill', 'var(--mantine-color-text)')
          .text(item.name);
      });
    }

    // Add dots with animation
    const dots = g.selectAll('.dot')
      .data(processedData)
      .enter().append('circle')
      .attr('class', 'dot')
      .attr('cx', d => xScale(d.index))
      .attr('cy', d => yScale(d.value))
      .attr('r', 0)
      .attr('fill', '#3b82f6')
      .attr('stroke', '#ffffff')
      .attr('stroke-width', 2);

    if (enableAnimation) {
      dots.transition()
        .duration(1000)
        .delay((d, i) => i * 50)
        .attr('r', 4);

      // Animate path drawing
      const totalLength = mainPath.node()?.getTotalLength() || 0;
      mainPath
        .attr('stroke-dasharray', totalLength + ' ' + totalLength)
        .attr('stroke-dashoffset', totalLength)
        .transition()
        .duration(2000)
        .ease(d3.easeLinear)
        .attr('stroke-dashoffset', 0);
    } else {
      dots.attr('r', 4);
    }

    // Add crosshair
    if (enableCrosshair) {
      const crosshair = g.append('g')
        .attr('class', 'crosshair')
        .style('display', 'none');

      crosshair.append('line')
        .attr('class', 'crosshair-x')
        .attr('y1', 0)
        .attr('y2', chartHeight)
        .style('stroke', '#666')
        .style('stroke-dasharray', '3,3');

      crosshair.append('line')
        .attr('class', 'crosshair-y')
        .attr('x1', 0)
        .attr('x2', width)
        .style('stroke', '#666')
        .style('stroke-dasharray', '3,3');
    }

    // Add tooltip
    const tooltip = d3.select('body').append('div')
      .attr('class', 'd3-tooltip')
      .style('position', 'absolute')
      .style('visibility', 'hidden')
      .style('background', 'rgba(0, 0, 0, 0.8)')
      .style('color', 'white')
      .style('padding', '8px')
      .style('border-radius', '4px')
      .style('font-size', '12px')
      .style('pointer-events', 'none')
      .style('z-index', 1000);

    // Add interaction overlay
    const overlay = g.append('rect')
      .attr('class', 'overlay')
      .attr('width', width)
      .attr('height', chartHeight)
      .style('fill', 'none')
      .style('pointer-events', 'all');

    // Mouse events
    overlay.on('mousemove', function(event) {
      const [mouseX, mouseY] = d3.pointer(event);
      const x0 = xScale.invert(mouseX);
      const i = Math.round(x0);
      const d = processedData[Math.max(0, Math.min(i, processedData.length - 1))];

      if (d && enableCrosshair) {
        crosshair.style('display', null);
        crosshair.select('.crosshair-x')
          .attr('x1', xScale(d.index))
          .attr('x2', xScale(d.index));
        crosshair.select('.crosshair-y')
          .attr('y1', yScale(d.value))
          .attr('y2', yScale(d.value));
      }

      if (d && showTooltips) {
        tooltip.style('visibility', 'visible')
          .html(`
            <div><strong>Time:</strong> ${d.timeLabel}</div>
            <div><strong>Network:</strong> ${d.value.toFixed(2)} Mbps</div>
            ${showMultipleSeries ? `
              <div><strong>CPU:</strong> ${(d.cpu || 0).toFixed(2)}%</div>
              <div><strong>Memory:</strong> ${(d.memory || 0).toFixed(2)}%</div>
            ` : ''}
          `)
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 10) + 'px');
      }
    })
    .on('mouseout', function() {
      if (enableCrosshair) {
        crosshair.style('display', 'none');
      }
      if (showTooltips) {
        tooltip.style('visibility', 'hidden');
      }
    });

    // Add brush for zoom functionality
    const brush = d3.brushX()
      .extent([[0, 0], [width, chartHeight]])
      .on('end', (event) => {
        if (!event.selection) return;

        const [x0, x1] = event.selection;
        const startIndex = Math.round(xScale.invert(x0));
        const endIndex = Math.round(xScale.invert(x1));
        const newDomain = [Math.max(0, startIndex), Math.min(processedData.length - 1, endIndex)];

        // Update x scale
        xScale.domain(newDomain);
        setZoomLevel(prev => prev * 1.5);

        // Update axes with transition
        xAxis.transition()
          .duration(750)
          .call(d3.axisBottom(xScale)
            .tickFormat((d, i) => {
              const index = Math.round(d as number);
              return processedData[index]?.timeLabel || '';
            })
            .ticks(Math.min(10, endIndex - startIndex + 1))
          );

        // Update paths with transition
        mainPath.transition()
          .duration(750)
          .attr('d', line);

        areaPath.transition()
          .duration(750)
          .attr('d', area);

        // Update dots
        dots.transition()
          .duration(750)
          .attr('cx', d => xScale(d.index));

        // Remove brush selection
        g.select('.brush').call(brush.move, null);
      });

    g.append('g')
      .attr('class', 'brush')
      .call(brush);

    // === UNIQUE D3 FEATURES THAT CAN'T BE DONE IN CHART.JS ===

    // 1. Dynamic Threshold Lines with Animated Alerts
    if (showThresholdLines) {
      const criticalThreshold = 80;
      const warningThreshold = 60;

      // Critical threshold line with pulsing animation
      const criticalLine = g.append('line')
        .attr('x1', 0)
        .attr('x2', width)
        .attr('y1', yScale(criticalThreshold))
        .attr('y2', yScale(criticalThreshold))
        .attr('stroke', '#ef4444')
        .attr('stroke-width', 2)
        .attr('stroke-dasharray', '5,5');

      // Pulsing animation for critical threshold
      criticalLine
        .transition()
        .duration(1000)
        .attr('stroke-opacity', 0.3)
        .transition()
        .duration(1000)
        .attr('stroke-opacity', 1)
        .on('end', function repeat() {
          d3.select(this)
            .transition()
            .duration(1000)
            .attr('stroke-opacity', 0.3)
            .transition()
            .duration(1000)
            .attr('stroke-opacity', 1)
            .on('end', repeat);
        });

      // Warning threshold with subtle glow
      g.append('line')
        .attr('x1', 0)
        .attr('x2', width)
        .attr('y1', yScale(warningThreshold))
        .attr('y2', yScale(warningThreshold))
        .attr('stroke', '#f59e0b')
        .attr('stroke-width', 1)
        .attr('stroke-dasharray', '3,3')
        .attr('opacity', 0.7);

      // Threshold labels with background
      const thresholdLabels = g.append('g').attr('class', 'threshold-labels');

      thresholdLabels.append('rect')
        .attr('x', width - 80)
        .attr('y', yScale(criticalThreshold) - 12)
        .attr('width', 75)
        .attr('height', 16)
        .attr('fill', '#ef4444')
        .attr('rx', 3);

      thresholdLabels.append('text')
        .attr('x', width - 42)
        .attr('y', yScale(criticalThreshold) - 2)
        .attr('text-anchor', 'middle')
        .attr('fill', 'white')
        .attr('font-size', '10px')
        .attr('font-weight', 'bold')
        .text('CRITICAL');
    }

    // 2. Particle Effect for High Activity
    if (enableParticleEffect) {
      const particles = g.append('g').attr('class', 'particles');

      processedData.forEach((d, i) => {
        if (d.value > 70) { // High activity threshold
          // Create particles at high activity points
          for (let p = 0; p < 3; p++) {
            particles.append('circle')
              .attr('cx', xScale(d.index))
              .attr('cy', yScale(d.value))
              .attr('r', 2)
              .attr('fill', '#3b82f6')
              .attr('opacity', 0.8)
              .transition()
              .duration(2000)
              .delay(p * 200)
              .attr('cy', yScale(d.value) - 30)
              .attr('r', 0)
              .attr('opacity', 0)
              .remove();
          }
        }
      });
    }

    // 3. Data Flow Animation (impossible in Chart.js)
    if (showDataFlow) {
      const flowPath = g.append('path')
        .datum(processedData)
        .attr('fill', 'none')
        .attr('stroke', '#06b6d4')
        .attr('stroke-width', 4)
        .attr('stroke-opacity', 0.6)
        .attr('d', line);

      // Animated flow effect
      const totalLength = flowPath.node()?.getTotalLength() || 0;

      function animateFlow() {
        const flowDot = g.append('circle')
          .attr('r', 6)
          .attr('fill', '#06b6d4')
          .attr('stroke', '#ffffff')
          .attr('stroke-width', 2)
          .attr('opacity', 0);

        flowDot
          .transition()
          .duration(100)
          .attr('opacity', 1)
          .transition()
          .duration(3000)
          .ease(d3.easeLinear)
          .attrTween('transform', () => {
            return (t: number) => {
              const point = flowPath.node()?.getPointAtLength(t * totalLength);
              return point ? `translate(${point.x},${point.y})` : '';
            };
          })
          .transition()
          .duration(100)
          .attr('opacity', 0)
          .remove()
          .on('end', () => {
            setTimeout(animateFlow, 1000); // Repeat every second
          });
      }

      animateFlow();
    }

    // 4. Heatmap Overlay (completely unique to D3)
    if (enableHeatmapOverlay) {
      const heatmapData: any[] = [];
      const cellWidth = width / processedData.length;
      const cellHeight = 20;

      processedData.forEach((d, i) => {
        const intensity = d.value / 100; // Normalize to 0-1
        heatmapData.push({
          x: i * cellWidth,
          y: chartHeight + 10,
          width: cellWidth,
          height: cellHeight,
          intensity: intensity,
          value: d.value
        });
      });

      const heatmapGroup = g.append('g').attr('class', 'heatmap');

      heatmapGroup.selectAll('.heatmap-cell')
        .data(heatmapData)
        .enter()
        .append('rect')
        .attr('class', 'heatmap-cell')
        .attr('x', d => d.x)
        .attr('y', d => d.y)
        .attr('width', d => d.width - 1)
        .attr('height', d => d.height)
        .attr('fill', d => {
          const color = d3.interpolateRdYlBu(1 - d.intensity); // Reverse for hot colors
          return color;
        })
        .attr('opacity', 0.8)
        .on('mouseover', function(event, d) {
          d3.select(this).attr('stroke', '#000').attr('stroke-width', 1);
          tooltip.style('visibility', 'visible')
            .html(`<div><strong>Intensity:</strong> ${(d.intensity * 100).toFixed(1)}%</div>`)
            .style('left', (event.pageX + 10) + 'px')
            .style('top', (event.pageY - 10) + 'px');
        })
        .on('mouseout', function() {
          d3.select(this).attr('stroke', 'none');
          tooltip.style('visibility', 'hidden');
        });

      // Heatmap label
      g.append('text')
        .attr('x', -10)
        .attr('y', chartHeight + 25)
        .attr('text-anchor', 'end')
        .attr('font-size', '10px')
        .attr('fill', 'var(--mantine-color-text)')
        .text('Intensity');
    }

    // 5. Custom Shape Annotations (impossible in Chart.js)
    const anomalies = processedData.filter(d => d.value > 85);
    anomalies.forEach(d => {
      // Custom diamond shape for anomalies
      const diamond = g.append('path')
        .attr('d', `M ${xScale(d.index)} ${yScale(d.value) - 8}
                   L ${xScale(d.index) + 6} ${yScale(d.value)}
                   L ${xScale(d.index)} ${yScale(d.value) + 8}
                   L ${xScale(d.index) - 6} ${yScale(d.value)} Z`)
        .attr('fill', '#ef4444')
        .attr('stroke', '#ffffff')
        .attr('stroke-width', 2)
        .attr('opacity', 0);

      // Pulsing anomaly indicator
      diamond
        .transition()
        .duration(500)
        .attr('opacity', 1)
        .transition()
        .duration(1000)
        .attr('opacity', 0.5)
        .transition()
        .duration(1000)
        .attr('opacity', 1)
        .on('end', function repeat() {
          d3.select(this)
            .transition()
            .duration(1000)
            .attr('opacity', 0.5)
            .transition()
            .duration(1000)
            .attr('opacity', 1)
            .on('end', repeat);
        });
    });

    // Cleanup function
    return () => {
      tooltip.remove();
    };

  }, [data, height, showMultipleSeries, enableCrosshair, showTooltips, enableAnimation]);

  const resetZoom = () => {
    setZoomLevel(1);
    // Trigger re-render by updating a dependency
    setEnableAnimation(prev => prev);
  };

  const content = (
    <div ref={containerRef} style={{ height: '100%', padding: minimal ? '16px' : '0' }}>
      <Group justify="space-between" mb="md">
        <Text size="lg" fw={600}>
          {title}
        </Text>
        <Group gap="xs">
          <Badge variant="light" color="blue">
            Zoom: {zoomLevel.toFixed(1)}x
          </Badge>
          {onRefresh && (
            <Tooltip label="Refresh chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onRefresh}>
                <IconRefresh size={16} />
              </ActionIcon>
            </Tooltip>
          )}
          <Tooltip label="Reset zoom" styles={tooltipStyles}>
            <ActionIcon variant="light" size="sm" onClick={resetZoom}>
              <IconTarget size={16} />
            </ActionIcon>
          </Tooltip>
          {onExport && (
            <Tooltip label="Export chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onExport}>
                <IconDownload size={16} />
              </ActionIcon>
            </Tooltip>
          )}
        </Group>
      </Group>

      {/* Advanced Controls */}
      <Group mb="md" gap="lg">
        <Switch
          label="Multiple Series"
          checked={showMultipleSeries}
          onChange={(event) => setShowMultipleSeries(event.currentTarget.checked)}
          size="sm"
        />
        <Switch
          label="Crosshair"
          checked={enableCrosshair}
          onChange={(event) => setEnableCrosshair(event.currentTarget.checked)}
          size="sm"
        />
        <Switch
          label="Tooltips"
          checked={showTooltips}
          onChange={(event) => setShowTooltips(event.currentTarget.checked)}
          size="sm"
        />
        <Switch
          label="Animations"
          checked={enableAnimation}
          onChange={(event) => setEnableAnimation(event.currentTarget.checked)}
          size="sm"
        />
      </Group>

      {/* Unique D3 Features */}
      <Group mb="md" gap="lg">
        <Switch
          label="Threshold Lines"
          checked={showThresholdLines}
          onChange={(event) => setShowThresholdLines(event.currentTarget.checked)}
          size="sm"
          color="red"
        />
        <Switch
          label="Particle Effects"
          checked={enableParticleEffect}
          onChange={(event) => setEnableParticleEffect(event.currentTarget.checked)}
          size="sm"
          color="blue"
        />
        <Switch
          label="Data Flow"
          checked={showDataFlow}
          onChange={(event) => setShowDataFlow(event.currentTarget.checked)}
          size="sm"
          color="cyan"
        />
        <Switch
          label="Heatmap Overlay"
          checked={enableHeatmapOverlay}
          onChange={(event) => setEnableHeatmapOverlay(event.currentTarget.checked)}
          size="sm"
          color="orange"
        />
      </Group>

      <svg
        ref={svgRef}
        width="100%"
        height={height - 140}
        style={{ overflow: 'visible' }}
      />
      
      <Text size="xs" c="dimmed" mt="xs">
        <strong>D3.js Unique Features (Impossible in Chart.js):</strong> Pulsing threshold alerts • Particle effects for anomalies • Animated data flow • Interactive heatmap overlay • Custom diamond shapes • Real-time animations
      </Text>
    </div>
  );

  if (minimal) {
    return content;
  }

  return (
    <Card shadow={withShadow ? "sm" : "none"} padding="lg" radius="md" withBorder={withBorder}>
      {content}
    </Card>
  );
};

export default D3ChartAdvanced;
