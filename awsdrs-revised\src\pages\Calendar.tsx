import { useState, useEffect, useCallback } from 'react';
import {
  Title,
  Text,
  Card,
  Group,
  Badge,
  Stack,
  Button,
  ActionIcon
} from "@mantine/core";
import { Calendar as MantineCalendar } from '@mantine/dates';
import {
  IconCalendar,
  IconPlus,
  IconChevronLeft,
  IconChevronRight,
  IconBell
} from "@tabler/icons-react";
import DashboardLayout from "../layouts/DashboardLayout";
import WidgetMenu from "../components/WidgetMenu";
import { useLayoutManager } from "../hooks/useLayoutManager";
import { GenericCardWidget } from '../components/widgets/GenericCardWidget';
import { DashboardWidget } from '../types/dashboard';
import { Responsive, WidthProvider } from 'react-grid-layout';

const ResponsiveGridLayout = WidthProvider(Responsive);

interface Event {
  id: string;
  title: string;
  date: Date;
  time: string;
  type: 'meeting' | 'task' | 'reminder' | 'event';
  color: string;
}

// Calendar widgets
const calendarWidgets: DashboardWidget[] = [
  { id: 'calendar-view', title: 'Calendar View', type: 'calendar-view' },
  { id: 'event-list', title: 'Event Details', type: 'event-list' },
  { id: 'upcoming-events', title: 'Upcoming Events', type: 'upcoming-events' },
  { id: 'event-stats', title: 'Event Statistics', type: 'event-stats' }
];

// Calendar layouts
const calendarLayouts = {
  lg: [
    { i: 'calendar-view', x: 0, y: 0, w: 6, h: 8, minW: 4, minH: 6 },
    { i: 'event-list', x: 6, y: 0, w: 3, h: 4, minW: 2, minH: 3 },
    { i: 'upcoming-events', x: 6, y: 4, w: 3, h: 4, minW: 2, minH: 3 },
    { i: 'event-stats', x: 0, y: 8, w: 9, h: 2, minW: 6, minH: 2 }
  ],
  md: [
    { i: 'calendar-view', x: 0, y: 0, w: 6, h: 8, minW: 4, minH: 6 },
    { i: 'event-list', x: 6, y: 0, w: 4, h: 4, minW: 3, minH: 3 },
    { i: 'upcoming-events', x: 6, y: 4, w: 4, h: 4, minW: 3, minH: 3 },
    { i: 'event-stats', x: 0, y: 8, w: 10, h: 2, minW: 8, minH: 2 }
  ],
  sm: [
    { i: 'calendar-view', x: 0, y: 0, w: 6, h: 8, minW: 6, minH: 6 },
    { i: 'event-list', x: 0, y: 8, w: 6, h: 4, minW: 6, minH: 3 },
    { i: 'upcoming-events', x: 0, y: 12, w: 6, h: 4, minW: 6, minH: 3 },
    { i: 'event-stats', x: 0, y: 16, w: 6, h: 2, minW: 6, minH: 2 }
  ]
};

const getInitialMenuPosition = () => ({
  x: Math.max(50, window.innerWidth - 370),
  y: 100
});

const Calendar = () => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());

  // Layout management with multiple presets
  const {
    layouts,
    layoutPresets,
    currentLayoutId,
    switchToLayout,
    saveCurrentLayout,
    deleteLayout,
    resetToDefault,
    handleLayoutChange
  } = useLayoutManager({
    pageKey: 'calendar',
    defaultLayouts: calendarLayouts,
    defaultLayoutName: 'Default Calendar Layout'
  });

  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [widgets] = useState(calendarWidgets);
  const [visibleWidgets, setVisibleWidgets] = useState(new Set(calendarWidgets.map(w => w.id)));
  const [showWidgetMenu, setShowWidgetMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState(getInitialMenuPosition());
  const [isDragging, setIsDragging] = useState(false);

  // Sample events
  const [events] = useState<Event[]>([
    {
      id: '1',
      title: 'Team Meeting',
      date: new Date(),
      time: '10:00 AM',
      type: 'meeting',
      color: 'blue'
    },
    {
      id: '2',
      title: 'Server Maintenance',
      date: new Date(Date.now() + 86400000), // Tomorrow
      time: '2:00 PM',
      type: 'task',
      color: 'orange'
    },
    {
      id: '3',
      title: 'Backup Review',
      date: new Date(Date.now() + 172800000), // Day after tomorrow
      time: '9:00 AM',
      type: 'reminder',
      color: 'green'
    },
    {
      id: '4',
      title: 'Conference Call',
      date: new Date(Date.now() + 259200000), // 3 days from now
      time: '3:00 PM',
      type: 'meeting',
      color: 'blue'
    }
  ]);

  const getEventsForDate = (date: Date) => {
    return events.filter(event =>
      event.date.toDateString() === date.toDateString()
    );
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
  };

  // Widget render function
  const renderWidget = (widget: DashboardWidget) => {
    switch (widget.type) {
      case 'calendar-view':
        return (
          <Card h="100%" p="md">
            <Group justify="space-between" mb="md">
              <Title order={3}>Calendar View</Title>
              <Group gap="xs">
                <ActionIcon
                  variant="subtle"
                  onClick={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1))}
                >
                  <IconChevronLeft size={16} />
                </ActionIcon>
                <Text fw={500}>
                  {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </Text>
                <ActionIcon
                  variant="subtle"
                  onClick={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1))}
                >
                  <IconChevronRight size={16} />
                </ActionIcon>
              </Group>
            </Group>

            <MantineCalendar
              value={selectedDate}
              onChange={handleDateSelect}
              month={currentMonth}
              onMonthChange={setCurrentMonth}
              size="lg"
              styles={{
                calendarHeader: {
                  display: 'none' // Hide default header since we have custom one
                },
                day: {
                  height: '60px',
                  fontSize: '14px',
                  fontWeight: 500,
                  position: 'relative',
                  '&[data-today="true"]': {
                    backgroundColor: 'var(--mantine-color-cyan-6)',
                    color: 'white',
                    fontWeight: 600,
                    '&:hover': {
                      backgroundColor: 'var(--mantine-color-cyan-7)',
                    }
                  },
                  '&[data-selected="true"]': {
                    backgroundColor: 'var(--mantine-color-blue-6)',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: 'var(--mantine-color-blue-7)',
                    }
                  },
                  '&:hover': {
                    backgroundColor: 'var(--mantine-color-gray-1)',
                  }
                }
              }}
              renderDay={(date) => {
                const dateObj = new Date(date);
                const dayEvents = getEventsForDate(dateObj);
                const today = isToday(dateObj);

                return (
                  <div style={{
                    height: '100%',
                    width: '100%',
                    position: 'relative',
                    display: 'flex',
                    flexDirection: 'column',
                    padding: '4px'
                  }}>
                    <div style={{
                      fontSize: '14px',
                      fontWeight: today ? 600 : 500,
                      marginBottom: '2px'
                    }}>
                      {dateObj.getDate()}
                    </div>

                    {dayEvents.slice(0, 2).map((event) => (
                      <div
                        key={event.id}
                        style={{
                          fontSize: '10px',
                          backgroundColor: `var(--mantine-color-${event.color}-6)`,
                          color: 'white',
                          padding: '1px 4px',
                          borderRadius: '2px',
                          marginBottom: '1px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: '100%'
                        }}
                      >
                        {event.title}
                      </div>
                    ))}

                    {dayEvents.length > 2 && (
                      <div style={{
                        fontSize: '9px',
                        color: 'var(--mantine-color-gray-6)',
                        fontWeight: 500
                      }}>
                        +{dayEvents.length - 2} more
                      </div>
                    )}
                  </div>
                );
              }}
            />
          </Card>
        );

      case 'event-list':
        return (
          <GenericCardWidget
            widget={widget}
            title={selectedDate ? selectedDate.toLocaleDateString('en-US', {
              weekday: 'long',
              month: 'long',
              day: 'numeric',
              year: 'numeric'
            }) : 'Select a Date'}
            icon={<IconCalendar size={20} />}
            color="blue"
          >
            <Group justify="space-between" mb="md">
              <Button
                size="xs"
                leftSection={<IconPlus size={14} />}
                disabled={!selectedDate}
              >
                Add Event
              </Button>
            </Group>

            <Stack gap="sm">
              {selectedDate && getEventsForDate(selectedDate).length > 0 ? (
                getEventsForDate(selectedDate).map((event) => (
                  <Card key={event.id} withBorder p="sm">
                    <Group justify="space-between" mb="xs">
                      <Badge color={event.color} size="sm">
                        {event.type}
                      </Badge>
                      <Text size="xs" c="dimmed">{event.time}</Text>
                    </Group>
                    <Text fw={500} size="sm">{event.title}</Text>
                  </Card>
                ))
              ) : selectedDate ? (
                <Text c="dimmed" ta="center" py="xl">
                  No events scheduled for this date
                </Text>
              ) : (
                <Text c="dimmed" ta="center" py="xl">
                  Select a date to view events
                </Text>
              )}
            </Stack>
          </GenericCardWidget>
        );

      case 'upcoming-events': {
        const upcomingEvents = events.filter(event => event.date >= new Date()).slice(0, 5);
        return (
          <GenericCardWidget
            widget={widget}
            title="Upcoming Events"
            value={upcomingEvents.length}
            icon={<IconBell size={20} />}
            color="orange"
            description="Next 5 upcoming events"
          >
            <Stack gap="xs">
              {upcomingEvents.map((event) => (
                <Group key={event.id} justify="space-between">
                  <div>
                    <Text size="sm" fw={500}>{event.title}</Text>
                    <Text size="xs" c="dimmed">{event.date.toLocaleDateString()} at {event.time}</Text>
                  </div>
                  <Badge color={event.color} size="xs">{event.type}</Badge>
                </Group>
              ))}
              {upcomingEvents.length === 0 && (
                <Text c="dimmed" ta="center" py="md">No upcoming events</Text>
              )}
            </Stack>
          </GenericCardWidget>
        );
      }

      case 'event-stats': {
        const totalEvents = events.length;
        const todayEvents = events.filter(event => isToday(event.date)).length;
        const thisWeekEvents = events.filter(event => {
          const weekStart = new Date();
          weekStart.setDate(weekStart.getDate() - weekStart.getDay());
          return event.date >= weekStart;
        }).length;

        return (
          <Card h="100%" p="md">
            <Title order={4} size="sm" c="dimmed" tt="uppercase" fw={700} mb="md">
              Event Statistics
            </Title>
            <Group justify="space-around">
              <div style={{ textAlign: 'center' }}>
                <Text fw={700} size="xl" c="blue">{totalEvents}</Text>
                <Text size="xs" c="dimmed">Total Events</Text>
              </div>
              <div style={{ textAlign: 'center' }}>
                <Text fw={700} size="xl" c="cyan">{todayEvents}</Text>
                <Text size="xs" c="dimmed">Today</Text>
              </div>
              <div style={{ textAlign: 'center' }}>
                <Text fw={700} size="xl" c="green">{thisWeekEvents}</Text>
                <Text size="xs" c="dimmed">This Week</Text>
              </div>
            </Group>
          </Card>
        );
      }

      default:
        return (
          <Card h="100%" p="md">
            <Text c="dimmed" ta="center">
              Widget: {widget.type}
            </Text>
          </Card>
        );
    }
  };

  // Load saved widget visibility
  useEffect(() => {
    try {
      const savedVisibility = localStorage.getItem('calendar-visible-widgets');
      if (savedVisibility) {
        setVisibleWidgets(new Set(JSON.parse(savedVisibility)));
      }
    } catch (error) {
      console.error('Error loading widget visibility:', error);
    }
  }, []);

  const toggleWidgetVisibility = (widgetId: string) => {
    setVisibleWidgets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(widgetId)) {
        newSet.delete(widgetId);
      } else {
        newSet.add(widgetId);
      }
      localStorage.setItem('calendar-visible-widgets', JSON.stringify([...newSet]));
      return newSet;
    });
  };

  const closeWidgetMenu = () => {
    setShowWidgetMenu(false);
  };

  const resetToDefaultAndClose = () => {
    resetToDefault();
    setShowWidgetMenu(false);
    setMenuPosition(getInitialMenuPosition());
  };

  // Optimized drag handlers for floating menu
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target !== e.currentTarget && !(e.target as Element).closest('.widget-menu-header')) return;

    setIsDragging(true);
    const startX = e.clientX - menuPosition.x;
    const startY = e.clientY - menuPosition.y;

    const handleMouseMove = (e: MouseEvent) => {
      const newX = Math.max(0, Math.min(window.innerWidth - 300, e.clientX - startX));
      const newY = Math.max(0, Math.min(window.innerHeight - 200, e.clientY - startY));

      setMenuPosition({ x: newX, y: newY });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1000);
  }, []);

  const allWidgets = widgets.map(widget => ({
    ...widget,
    isVisible: visibleWidgets.has(widget.id)
  }));

  const dashboardControls = {
    isEditing,
    loading,
    showWidgetMenu,
    onToggleEdit: () => setIsEditing(!isEditing),
    onRefresh: handleRefresh,
    onResetLayout: resetToDefaultAndClose,
    onToggleWidgetMenu: () => {
      if (!showWidgetMenu) {
        setMenuPosition(getInitialMenuPosition());
      }
      setShowWidgetMenu(!showWidgetMenu);
    }
  };

  return (
    <DashboardLayout dashboardControls={dashboardControls}>
      <div style={{ width: '100%', position: 'relative', maxWidth: '1400px', margin: '0 auto' }}>

        {/* Dashboard Grid */}
        <div style={{ flex: 1, overflow: 'auto', paddingTop: '40px' }}>
          <ResponsiveGridLayout
            className={`layout ${isEditing ? 'editing' : ''}`}
            layouts={layouts}
            onLayoutChange={(layout, layouts) => {
              if (isEditing) {
                handleLayoutChange(layout, layouts);
              }
            }}
            breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
            cols={{ lg: 9, md: 10, sm: 6, xs: 4, xxs: 2 }}
            rowHeight={60}
            isDraggable={isEditing}
            isResizable={isEditing}
            margin={[16, 16]}
            containerPadding={[16, 16]}
            useCSSTransforms={true}
            compactType="vertical"
            preventCollision={false}
            allowOverlap={false}
          >
            {allWidgets.map((widget) => {
              const isVisible = visibleWidgets.has(widget.id);
              return (
                <div
                  key={widget.id}
                  className="widget-container"
                  style={{
                    display: isVisible ? 'block' : 'none'
                  }}
                >
                  <Card
                    h="100%"
                    p={0}
                    style={{
                      position: 'relative',
                      overflow: 'hidden',
                      border: isEditing ? '2px solid var(--mantine-color-blue-4)' : '1px solid var(--mantine-color-gray-3)',
                      borderRadius: '8px',
                      backgroundColor: 'var(--mantine-color-body)'
                    }}
                  >
                    {renderWidget(widget)}
                  </Card>
                </div>
              );
            })}
          </ResponsiveGridLayout>
        </div>

        {/* Floating Widget Selection Menu */}
        {showWidgetMenu && (
          <WidgetMenu
            widgets={widgets}
            visibleWidgets={visibleWidgets}
            onToggleWidgetVisibility={toggleWidgetVisibility}
            layoutPresets={layoutPresets}
            currentLayoutId={currentLayoutId}
            onLayoutChange={switchToLayout}
            onSaveLayout={saveCurrentLayout}
            onDeleteLayout={deleteLayout}
            onResetToDefault={resetToDefaultAndClose}
            menuPosition={menuPosition}
            isDragging={isDragging}
            onMouseDown={handleMouseDown}
            onClose={closeWidgetMenu}
          />
        )}
      </div>
    </DashboardLayout>
  );
};


export default Calendar;
