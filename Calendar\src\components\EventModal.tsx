import { useState, useEffect } from 'react'
import { Modal, TextInput, Textarea, Select, Switch, Group, Button, Stack, Text } from '@mantine/core'
import { format } from 'date-fns'
import { CalendarEvent, EventCategory } from '../types/calendar'

interface EventModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>) => void
  event?: CalendarEvent | null
  selectedDate?: Date
  categories: EventCategory[]
}

export function EventModal({ isOpen, onClose, onSave, event, selectedDate, categories }: EventModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    start: '',
    end: '',
    allDay: false,
    category: 'work',
    location: '',
    attendees: '',
  })

  useEffect(() => {
    if (event) {
      setFormData({
        title: event.title,
        description: event.description || '',
        start: format(event.start, "yyyy-MM-dd'T'HH:mm"),
        end: format(event.end, "yyyy-MM-dd'T'HH:mm"),
        allDay: event.allDay,
        category: event.category || 'work',
        location: event.location || '',
        attendees: event.attendees?.join(', ') || '',
      })
    } else if (selectedDate) {
      const startTime = format(selectedDate, "yyyy-MM-dd'T'09:00")
      const endTime = format(selectedDate, "yyyy-MM-dd'T'10:00")
      setFormData({
        title: '',
        description: '',
        start: startTime,
        end: endTime,
        allDay: false,
        category: 'work',
        location: '',
        attendees: '',
      })
    }
  }, [event, selectedDate])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const eventData: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'> = {
      title: formData.title,
      description: formData.description,
      start: new Date(formData.start),
      end: new Date(formData.end),
      allDay: formData.allDay,
      category: formData.category,
      location: formData.location,
      attendees: formData.attendees ? formData.attendees.split(',').map(email => email.trim()) : [],
      color: categories.find(cat => cat.id === formData.category)?.color,
    }

    onSave(eventData)
    onClose()
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    
    if (name === 'start') {
      // When start date changes, ensure end date is not before start date
      const newStart = new Date(value)
      const currentEnd = new Date(formData.end)
      
      setFormData(prev => ({
        ...prev,
        start: value,
        end: currentEnd < newStart ? value : prev.end
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
      }))
    }
  }

  if (!isOpen) return null

  return (
    <Modal
      opened={isOpen}
      onClose={onClose}
      title={event ? 'Edit Event' : 'Create Event'}
      size="lg"
    >
      <form onSubmit={handleSubmit}>
        <Stack gap="md">
          <TextInput
            label="Event Title"
            placeholder="Enter event title"
            value={formData.title}
            onChange={(e) => handleInputChange(e)}
            name="title"
            required
          />

          <Group grow>
            <Select
              label="Category"
              value={formData.category}
              onChange={(value) => handleInputChange({ target: { name: 'category', value } } as any)}
              data={categories.map(category => ({ value: category.id, label: category.name }))}
            />
            <Switch
              label="All day"
              checked={formData.allDay}
              onChange={(event) => handleInputChange({ target: { name: 'allDay', type: 'checkbox', checked: event.currentTarget.checked } } as any)}
            />
          </Group>

          <Group grow>
            <TextInput
              label={`Start ${formData.allDay ? 'Date' : 'Time'}`}
              type={formData.allDay ? 'date' : 'datetime-local'}
              value={formData.allDay ? formData.start.split('T')[0] : formData.start}
              onChange={(e) => handleInputChange(e)}
              name="start"
              required
            />
            <TextInput
              label={`End ${formData.allDay ? 'Date' : 'Time'}`}
              type={formData.allDay ? 'date' : 'datetime-local'}
              value={formData.allDay ? formData.end.split('T')[0] : formData.end}
              onChange={(e) => handleInputChange(e)}
              name="end"
              required
            />
          </Group>

          <Group grow>
            <TextInput
              label="Location"
              placeholder="Enter location"
              value={formData.location}
              onChange={(e) => handleInputChange(e)}
              name="location"
            />
            <TextInput
              label="Attendees"
              placeholder="Email addresses"
              value={formData.attendees}
              onChange={(e) => handleInputChange(e)}
              name="attendees"
            />
          </Group>

          <Textarea
            label="Description"
            placeholder="Event description"
            value={formData.description}
            onChange={(e) => handleInputChange(e)}
            name="description"
            rows={3}
          />

          <Group justify="flex-end" mt="md">
            <Button variant="subtle" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              {event ? 'Update Event' : 'Create Event'}
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  )
}