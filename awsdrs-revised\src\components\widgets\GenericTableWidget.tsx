import { Card, Title, Table, Text, ScrollArea, Badge, TextInput, Group } from '@mantine/core';
import { IconSearch } from '@tabler/icons-react';
import { DashboardWidget } from '../../types/dashboard';
import { useState } from 'react';

interface TableColumn {
  key: string;
  label: string;
  render?: (value: any, row: any) => React.ReactNode;
}

interface GenericTableWidgetProps {
  widget: DashboardWidget;
  title: string;
  columns: TableColumn[];
  data: any[];
  maxHeight?: number;
}

export const GenericTableWidget: React.FC<GenericTableWidgetProps> = ({
  widget,
  title,
  columns,
  data,
  maxHeight = 400
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  // Filter data based on search query
  const filteredData = data.filter(row => {
    if (!searchQuery) return true;

    return columns.some(column => {
      const value = row[column.key];
      if (typeof value === 'string') {
        return value.toLowerCase().includes(searchQuery.toLowerCase());
      }
      return false;
    });
  });

  return (
    <Card h="100%" p="md" style={{ display: 'flex', flexDirection: 'column' }}>
      <Title order={4} size="sm" c="dimmed" tt="uppercase" fw={700} mb="md">
        {title}
      </Title>

      {/* Search Bar */}
      <Group mb="md">
        <TextInput
          placeholder="Search servers..."
          value={searchQuery}
          onChange={(event) => setSearchQuery(event.currentTarget.value)}
          leftSection={<IconSearch size={16} />}
          size="xs"
          style={{ minWidth: 120 }}
        />
      </Group>

      {/* Table */}
      <ScrollArea style={{ height: 'calc(100% - 100px)' }}>
        <Table striped highlightOnHover fontSize="xs">
          <Table.Thead>
            <Table.Tr>
              {columns.map((column) => (
                <Table.Th key={column.key}>
                  {column.label}
                </Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {filteredData.length > 0 ? (
              filteredData.map((row, index) => (
                <Table.Tr key={index}>
                  {columns.map((column) => (
                    <Table.Td key={column.key}>
                      {column.render
                        ? column.render(row[column.key], row)
                        : <Text size="xs">{row[column.key] || '-'}</Text>
                      }
                    </Table.Td>
                  ))}
                </Table.Tr>
              ))
            ) : (
              <Table.Tr>
                <Table.Td colSpan={columns.length}>
                  <Text c="dimmed" ta="center" py="xl" size="xs">
                    {searchQuery ? 'No servers match your search' : 'No data available'}
                  </Text>
                </Table.Td>
              </Table.Tr>
            )}
          </Table.Tbody>
        </Table>
      </ScrollArea>
    </Card>
  );
};
