# Theme Menu Component Documentation

This document explains how to implement the draggable theme menu component with color picker functionality in your React projects.

## Table of Contents
1. [Features](#features)
2. [Dependencies](#dependencies)
3. [Required Files](#required-files)
4. [CSS Setup](#css-setup)
5. [Implementation](#implementation)
6. [Component API](#component-api)
7. [Usage Example](#usage-example)

## Features
- Color picker with hex input
- Saturation and brightness controls
- Dark mode toggle
- Draggable menu window
- Theme presets for different UI elements
- Responsive design
- Screen edge detection

## Dependencies
Add these dependencies to your `package.json`:
```json
{
  "dependencies": {
    "react-colorful": "^5.6.1",
    "@heroicons/react": "^2.1.1"
  }
}
```

## Required Files
You need three main files for the theme menu implementation:

1. `ThemeMenu.tsx` - The main component
2. `useTheme.ts` - Theme management hook
3. `useDraggableMenu.ts` - Draggable functionality hook

### File Structure
```
src/
  components/
    ThemeMenu.tsx
  hooks/
    useTheme.ts
    useDraggableMenu.ts
```

## CSS Setup
Add these CSS variables to your root stylesheet:

```css
:root {
  --primary-color: #005499;
  --calendar-bg: #FFFFFF;
  --calendar-dark-bg: #1F2937;
  --header-bg: #FFFFFF;
  --text-color: #111827;
  --border-color: #E5E7EB;
}
```

## Implementation

### 1. Theme Hook (useTheme.ts)
The theme hook manages color state and theme switching:

```typescript
import { useState, useEffect } from 'react'

export interface ThemeColors {
  primary: string
  header: string
  text: string
  border: string
  background: string
}

export function useTheme() {
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [colors, setColors] = useState<ThemeColors>({
    primary: "#005499",
    header: "#FFFFFF",
    text: "#111827",
    border: "#E5E7EB",
    background: "#FFFFFF"
  })
  const [selectedElement, setSelectedElement] = useState<keyof ThemeColors>('primary')

  const toggleTheme = () => setIsDarkMode(!isDarkMode)
  
  const handleColorChange = (color: string) => {
    setColors(prev => ({
      ...prev,
      [selectedElement]: color
    }))
  }

  return {
    isDarkMode,
    colors,
    selectedElement,
    setSelectedElement,
    toggleTheme,
    handleColorChange
  }
}
```

### 2. Draggable Menu Hook (useDraggableMenu.ts)
This hook handles the draggable functionality:

```typescript
import { useState, useRef, useEffect } from 'react'

interface Position {
  x: number
  y: number
}

export function useDraggableMenu() {
  const [showMenu, setShowMenu] = useState(false)
  const [menuPosition, setMenuPosition] = useState<Position>({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  // ... rest of the hook implementation from the source file

  return {
    showMenu,
    setShowMenu,
    menuPosition,
    setMenuPosition,
    isDragging,
    menuRef,
    handleMouseDown
  }
}
```

## Component API

### ThemeMenu Props
```typescript
interface ThemeMenuProps {
  menuRef: React.RefObject<HTMLDivElement>
  position: { x: number; y: number }
  isDragging: boolean
  onMouseDown: (e: React.MouseEvent) => void
  colors: ThemeColors
  selectedElement: keyof ThemeColors
  onElementSelect: (element: keyof ThemeColors) => void
  onColorChange: (color: string) => void
  onReset: () => void
  isDarkMode: boolean
  onThemeToggle: () => void
}
```

## Usage Example

```tsx
import { ThemeMenu } from './components/ThemeMenu'
import { useTheme } from './hooks/useTheme'
import { useDraggableMenu } from './hooks/useDraggableMenu'

function App() {
  const theme = useTheme()
  const themeMenu = useDraggableMenu()

  const handleThemeClick = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const windowWidth = window.innerWidth
    const menuWidth = 500
    const padding = 16
    
    let xPos = rect.left
    if (xPos + menuWidth + padding > windowWidth) {
      xPos = windowWidth - menuWidth - padding
    }
    xPos = Math.max(padding, xPos)
    
    themeMenu.setMenuPosition({ x: xPos, y: rect.bottom })
    themeMenu.setShowMenu(true)
  }

  return (
    <div className={theme.isDarkMode ? 'dark' : ''}>
      <button 
        onClick={handleThemeClick}
        className="px-4 py-2 bg-blue-500 text-white rounded"
      >
        Open Theme Menu
      </button>
      
      {themeMenu.showMenu && (
        <ThemeMenu
          menuRef={themeMenu.menuRef}
          position={themeMenu.menuPosition}
          isDragging={themeMenu.isDragging}
          onMouseDown={(e) => themeMenu.handleMouseDown(e)}
          colors={theme.colors}
          selectedElement={theme.selectedElement}
          onElementSelect={theme.setSelectedElement}
          onColorChange={theme.handleColorChange}
          onReset={theme.resetToDefault}
          isDarkMode={theme.isDarkMode}
          onThemeToggle={theme.toggleTheme}
        />
      )}
    </div>
  )
}
```

## Additional Notes

1. The menu automatically handles screen edge detection to prevent it from going off-screen
2. The theme colors are applied using CSS variables, making them easy to use throughout your application
3. The color picker includes HSL controls for fine-tuning colors
4. The menu is draggable by its header
5. All colors are stored in state and can be persisted to localStorage if needed
6. The component supports both light and dark modes
7. The menu is responsive and works well on different screen sizes

## Customization

You can customize the appearance by:
1. Modifying the CSS variables
2. Adjusting the Tailwind classes in the component
3. Adding or removing color elements in the ThemeColors interface
4. Customizing the color picker controls
5. Modifying the menu dimensions

## Best Practices

1. Always handle screen edges to prevent the menu from going off-screen
2. Use CSS variables for consistent theming
3. Implement proper cleanup in useEffect hooks
4. Handle keyboard accessibility
5. Provide proper aria labels for accessibility
6. Include proper type definitions
7. Handle color validation for hex inputs