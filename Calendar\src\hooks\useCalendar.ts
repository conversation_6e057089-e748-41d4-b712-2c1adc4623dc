import { useState } from 'react'
import { addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, setYear } from 'date-fns'

export function useCalendar() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [view, setView] = useState<'day' | 'workweek' | 'week' | 'month' | 'year'>('workweek')

  const nextMonth = () => {
    if (view === 'year') {
      setCurrentDate(prev => {
        const nextYear = new Date(prev)
        nextYear.setFullYear(prev.getFullYear() + 1)
        return nextYear
      })
    } else {
      setCurrentDate(prev => addMonths(prev, 1))
    }
    setSelectedDate(null)
  }
  
  const prevMonth = () => {
    if (view === 'year') {
      setCurrentDate(prev => {
        const prevYear = new Date(prev)
        prevYear.setFullYear(prev.getFullYear() - 1)
        return prevYear
      })
    } else {
      setCurrentDate(prev => subMonths(prev, 1))
    }
    setSelectedDate(null)
  }

  const setYear = (year: number) => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      newDate.setFullYear(year)
      return newDate
    })
  }

  const days = eachDayOfInterval({
    start: startOfMonth(currentDate),
    end: endOfMonth(currentDate)
  })

  const handleDayClick = (day: Date) => {
    setSelectedDate(day)
    setView('day')
  }

  return {
    currentDate,
    selectedDate,
    days,
    view,
    setView,
    nextMonth,
    prevMonth,
    setYear,
    handleDayClick
  }
}