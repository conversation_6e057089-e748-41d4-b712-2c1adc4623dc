import {
  createReactComponent
} from "./chunk-5HMDTYKJ.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconVinyl.mjs
var IconVinyl = createReactComponent("outline", "vinyl", "IconVinyl", [["path", { "d": "M16 3.937a9 9 0 1 0 5 8.063", "key": "svg-0" }], ["path", { "d": "M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-1" }], ["path", { "d": "M20 4m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-2" }], ["path", { "d": "M20 4l-3.5 10l-2.5 2", "key": "svg-3" }]]);

export {
  IconVinyl
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconVinyl.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-OXTSANEX.js.map
