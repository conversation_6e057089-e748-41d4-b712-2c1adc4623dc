import { useState, useEffect } from 'react'

export interface ThemeColors {
  primary: string
  header: string
  text: string
  border: string
  background: string
}

const defaultLightColors: ThemeColors = {
  primary: "#005499",
  header: "#FFFFFF",
  text: "#111827",
  border: "#E5E7EB",
  background: "#FFFFFF"
}

const defaultDarkColors: ThemeColors = {
  primary: "#005499",
  header: "#1F2937",
  text: "#D1D5DB",
  border: "#374151",
  background: "#1F2937"
}

export function useTheme() {
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [lightColors, setLightColors] = useState<ThemeColors>(defaultLightColors)
  const [darkColors, setDarkColors] = useState<ThemeColors>(defaultDarkColors)
  const [selectedElement, setSelectedElement] = useState<keyof ThemeColors>('primary')

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode)
  }

  const handleColorChange = (color: string) => {
    const colors = isDarkMode ? darkColors : lightColors
    const setColors = isDarkMode ? setDarkColors : setLightColors

    // Update the color state
    setColors(prev => ({
      ...prev,
      [selectedElement]: color
    }))

    // Apply the color to CSS variables
    switch (selectedElement) {
      case 'primary':
        document.documentElement.style.setProperty('--primary-color', color)
        break
      case 'background':
        document.documentElement.style.setProperty('--calendar-bg', color)
        if (isDarkMode) {
          document.documentElement.style.setProperty('--calendar-dark-bg', color)
        }
        break
      case 'header':
        document.documentElement.style.setProperty('--header-bg', color)
        break
      case 'text':
        document.documentElement.style.setProperty('--text-color', color)
        break
      case 'border':
        document.documentElement.style.setProperty('--border-color', color)
        break
    }
  }

  const resetToDefault = () => {
    const defaultColors = isDarkMode ? defaultDarkColors : defaultLightColors
    
    // Reset all colors to default
    Object.entries(defaultColors).forEach(([key, value]) => {
      handleColorChange(value)
    })

    // Reset the state
    if (isDarkMode) {
      setDarkColors(defaultDarkColors)
    } else {
      setLightColors(defaultLightColors)
    }
  }

  useEffect(() => {
    document.documentElement.classList.toggle('dark', isDarkMode)
    const colors = isDarkMode ? darkColors : lightColors
    
    // Apply all colors when theme changes
    Object.entries(colors).forEach(([key, value]) => {
      switch (key) {
        case 'primary':
          document.documentElement.style.setProperty('--primary-color', value)
          break
        case 'background':
          document.documentElement.style.setProperty('--calendar-bg', value)
          if (isDarkMode) {
            document.documentElement.style.setProperty('--calendar-dark-bg', value)
          }
          document.documentElement.style.backgroundColor = value
          break
        case 'header':
          document.documentElement.style.setProperty('--header-bg', value)
          break
        case 'text':
          document.documentElement.style.setProperty('--text-color', value)
          break
        case 'border':
          document.documentElement.style.setProperty('--border-color', value)
          break
      }
    })
  }, [isDarkMode, darkColors, lightColors])

  return {
    isDarkMode,
    colors: isDarkMode ? darkColors : lightColors,
    selectedElement,
    setSelectedElement,
    toggleTheme,
    handleColorChange,
    resetToDefault
  }
}