import {
  createReactComponent
} from "./chunk-5HMDTYKJ.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconGrillSpatula.mjs
var IconGrillSpatula = createReactComponent("outline", "grill-spatula", "IconGrillSpatula", [["path", { "d": "M10.2 10.2l6.3 6.3", "key": "svg-0" }], ["path", { "d": "M19.347 16.575l1.08 1.079a1.96 1.96 0 0 1 -2.773 2.772l-1.08 -1.079a1.96 1.96 0 0 1 2.773 -2.772z", "key": "svg-1" }], ["path", { "d": "M3 7l3.05 3.15a2.9 2.9 0 0 0 4.1 -4.1l-3.15 -3.05l-4 4z", "key": "svg-2" }]]);

export {
  IconGrillSpatula
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconGrillSpatula.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-OZBVARJY.js.map
