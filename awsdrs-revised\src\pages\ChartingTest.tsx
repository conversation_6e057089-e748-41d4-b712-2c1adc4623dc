import React, { useState, useEffect } from 'react';
import { Container, Title, Grid, Card, Text, Group, Button, Badge, Stack } from '@mantine/core';
import { Icon<PERSON><PERSON>Line, IconZoom, IconRefresh } from '@tabler/icons-react';
import D3ChartAdvanced from '../components/widgets/D3ChartWidgetAdvanced';
import ChartJsAdvanced from '../components/widgets/ChartJsWidgetAdvanced';

// Generate sample network traffic data
const generateNetworkData = (points: number = 50) => {
  const data = [];
  const startTime = new Date();
  startTime.setHours(0, 0, 0, 0);
  
  for (let i = 0; i < points; i++) {
    const time = new Date(startTime.getTime() + i * 30 * 60 * 1000); // 30-minute intervals
    const baseValue = 50 + Math.sin(i * 0.2) * 30; // Base sine wave
    const noise = Math.random() * 20 - 10; // Random noise
    const spike = i % 15 === 0 ? Math.random() * 40 : 0; // Occasional spikes
    
    const cpuBase = 30 + Math.sin(i * 0.15) * 20;
    const memoryBase = 45 + Math.cos(i * 0.1) * 15;

    data.push({
      name: time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
      time: time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
      value: Math.max(0, baseValue + noise + spike),
      cpu: Math.max(0, cpuBase + (Math.random() * 10 - 5)),
      memory: Math.max(0, memoryBase + (Math.random() * 8 - 4)),
      disk: Math.max(0, 60 + Math.sin(i * 0.05) * 10 + (Math.random() * 6 - 3)),
    });
  }
  
  return data;
};

const ChartingTest: React.FC = () => {
  const [data, setData] = useState(generateNetworkData());
  const [refreshCount, setRefreshCount] = useState(0);

  const refreshData = () => {
    setData(generateNetworkData());
    setRefreshCount(prev => prev + 1);
  };

  const exportData = () => {
    console.log('Exporting data...', data);
    alert('Data exported to console');
  };

  const chartFeatures = [
    {
      name: 'D3.js Advanced',
      component: (
        <D3ChartAdvanced
          title="D3.js - Maximum Customization"
          data={data}
          height={450}
          onRefresh={refreshData}
          onExport={exportData}
          minimal={true}
        />
      ),
      category: 'Custom Visualization Engine',
      features: [
        'Drag across chart to zoom (OpManager-style)',
        'Pulsing threshold lines with animated alerts',
        'Particle effects for high-activity data points',
        'Animated data flow visualization',
        'Interactive heatmap overlay with intensity mapping',
        'Custom diamond shapes for anomaly detection',
        'Real-time morphing animations and transitions',
        'Complex multi-layered visual compositions'
      ],
      pros: [
        'Create impossible-to-replicate visualizations',
        'Real-time animated effects and particle systems',
        'Custom interactive overlays and annotations',
        'Complex multi-dimensional data representations',
        'Unique brand differentiation through custom visuals',
        'Advanced user interaction patterns',
        'Morphing and transitioning between chart states',
        'Complete creative freedom in data storytelling'
      ],
      cons: [
        'Steeper learning curve',
        'More development time required',
        'Requires D3.js expertise',
        'More code to maintain',
        'Manual responsive design handling',
        'Custom accessibility implementation needed'
      ],
      bestFor: [
        'Unique dashboard requirements',
        'Complex data visualizations',
        'Custom interaction patterns',
        'Brand-specific styling needs',
        'Performance-critical applications',
        'Advanced animation requirements'
      ],
      dashboardBenefits: [
        'Creates distinctive, professional appearance',
        'Enables unique user interaction patterns',
        'Supports complex multi-dimensional data',
        'Allows for innovative visualization concepts',
        'Provides competitive advantage through custom UX'
      ]
    },
    {
      name: 'Chart.js Advanced',
      component: (
        <ChartJsAdvanced
          title="Chart.js - Enterprise Ready"
          data={data}
          height={450}
          onRefresh={refreshData}
          onExport={exportData}
          minimal={true}
        />
      ),
      category: 'Professional Charting Library',
      features: [
        'Multiple zoom methods (drag, wheel, pinch)',
        'Pan with modifier key (Shift+drag)',
        'Built-in export to PNG functionality',
        'Dynamic chart type switching (Line/Bar)',
        'Rich tooltip system with custom callbacks',
        'Professional legend with point styles',
        'Smooth animations with easing options',
        'Responsive design out-of-the-box'
      ],
      pros: [
        'Quick implementation and setup',
        'Excellent documentation and community',
        'Built-in responsive behavior',
        'Professional appearance by default',
        'Multiple interaction methods',
        'Strong TypeScript support',
        'Extensive plugin ecosystem',
        'Good performance with large datasets'
      ],
      cons: [
        'Less customization than D3.js',
        'Plugin dependencies for advanced features',
        'Limited custom animation control',
        'Styling constraints within Chart.js patterns',
        'Bundle size larger than minimal solutions'
      ],
      bestFor: [
        'Rapid dashboard development',
        'Standard business charts',
        'Teams with limited visualization expertise',
        'Consistent chart appearance across app',
        'Projects requiring quick delivery',
        'Enterprise applications with standard needs'
      ],
      dashboardBenefits: [
        'Reduces development time significantly',
        'Ensures consistent professional appearance',
        'Provides reliable cross-browser compatibility',
        'Offers multiple export and sharing options',
        'Supports accessibility standards out-of-box'
      ]
    },
  ];

  return (
    <Container size="xl" py="xl">
      <Stack gap="xl">
        <Group justify="space-between" align="center">
          <div>
            <Title order={1} mb="xs">
              <Group gap="sm">
                <IconChartLine size={32} />
                Advanced Dashboard Charting: D3.js vs Chart.js
              </Group>
            </Title>
            <Text c="dimmed" size="lg">
              Comprehensive comparison of professional charting solutions for modern dashboards
            </Text>
          </div>
          
          <Group gap="sm">
            <Badge variant="light" size="lg">
              Data Points: {data.length}
            </Badge>
            <Badge variant="light" size="lg" color="green">
              Refreshed: {refreshCount} times
            </Badge>
            <Button 
              leftSection={<IconRefresh size={16} />}
              onClick={refreshData}
              variant="light"
            >
              Refresh All Data
            </Button>
          </Group>
        </Group>

        <Grid>
          {chartFeatures.map((chart, index) => (
            <Grid.Col key={index} span={12}>
              <Card shadow="sm" padding="lg" radius="md" withBorder>
                <Stack gap="lg">
                  <Group justify="space-between" align="center">
                    <div>
                      <Title order={2}>{chart.name}</Title>
                      <Badge variant="gradient" gradient={{ from: 'blue', to: 'cyan' }} size="lg" mt="xs">
                        {chart.category}
                      </Badge>
                    </div>
                    <Group gap="sm">
                      <Badge variant="light" color="green">
                        <IconZoom size={12} style={{ marginRight: 4 }} />
                        Advanced Zoom
                      </Badge>
                      <Badge variant="light" color="blue">
                        Interactive
                      </Badge>
                      <Badge variant="light" color="purple">
                        Professional
                      </Badge>
                    </Group>
                  </Group>

                  <div style={{ height: '450px' }}>
                    {chart.component}
                  </div>

                  <Grid>
                    <Grid.Col span={6}>
                      <Stack gap="md">
                        <div>
                          <Text size="lg" fw={700} c="blue" mb="sm">🚀 Key Features</Text>
                          <Stack gap={4}>
                            {chart.features.map((feature, idx) => (
                              <Text key={idx} size="sm">• {feature}</Text>
                            ))}
                          </Stack>
                        </div>

                        <div>
                          <Text size="lg" fw={700} c="green" mb="sm">✅ Advantages</Text>
                          <Stack gap={4}>
                            {chart.pros.map((pro, idx) => (
                              <Text key={idx} size="sm" c="green">✓ {pro}</Text>
                            ))}
                          </Stack>
                        </div>
                      </Stack>
                    </Grid.Col>

                    <Grid.Col span={6}>
                      <Stack gap="md">
                        <div>
                          <Text size="lg" fw={700} c="orange" mb="sm">⚠️ Considerations</Text>
                          <Stack gap={4}>
                            {chart.cons.map((con, idx) => (
                              <Text key={idx} size="sm" c="orange">• {con}</Text>
                            ))}
                          </Stack>
                        </div>

                        <div>
                          <Text size="lg" fw={700} c="purple" mb="sm">🎯 Best For</Text>
                          <Stack gap={4}>
                            {chart.bestFor.map((use, idx) => (
                              <Text key={idx} size="sm" c="purple">→ {use}</Text>
                            ))}
                          </Stack>
                        </div>
                      </Stack>
                    </Grid.Col>
                  </Grid>

                  <div>
                    <Text size="lg" fw={700} c="indigo" mb="sm">📊 Dashboard UI Benefits</Text>
                    <Grid>
                      {chart.dashboardBenefits.map((benefit, idx) => (
                        <Grid.Col key={idx} span={6}>
                          <Text size="sm" c="indigo">💡 {benefit}</Text>
                        </Grid.Col>
                      ))}
                    </Grid>
                  </div>
                </Stack>
              </Card>
            </Grid.Col>
          ))}
        </Grid>

        <Card shadow="sm" padding="xl" radius="md" withBorder>
          <Title order={2} mb="xl" ta="center">📊 Comprehensive Dashboard Charting Analysis</Title>

          <Grid>
            <Grid.Col span={6}>
              <Card shadow="xs" padding="lg" radius="md" withBorder style={{ height: '100%' }}>
                <Stack gap="md">
                  <Group>
                    <Badge size="xl" variant="gradient" gradient={{ from: 'blue', to: 'purple' }}>
                      D3.js Advanced
                    </Badge>
                  </Group>

                  <Text fw={700} size="lg" c="blue">🎨 Maximum Customization</Text>

                  <div>
                    <Text fw={600} c="green" mb="xs">Perfect for:</Text>
                    <Text size="sm">• Unique brand requirements</Text>
                    <Text size="sm">• Complex data relationships</Text>
                    <Text size="sm">• Custom interaction patterns</Text>
                    <Text size="sm">• Performance-critical applications</Text>
                  </div>

                  <div>
                    <Text fw={600} c="blue" mb="xs">Dashboard Impact:</Text>
                    <Text size="sm">Creates a distinctive, professional appearance that sets your dashboard apart from standard solutions. Enables innovative user experiences that can become competitive advantages.</Text>
                  </div>

                  <div>
                    <Text fw={600} c="orange" mb="xs">Investment Required:</Text>
                    <Text size="sm">Higher development time and D3.js expertise needed, but provides unlimited customization potential.</Text>
                  </div>
                </Stack>
              </Card>
            </Grid.Col>

            <Grid.Col span={6}>
              <Card shadow="xs" padding="lg" radius="md" withBorder style={{ height: '100%' }}>
                <Stack gap="md">
                  <Group>
                    <Badge size="xl" variant="gradient" gradient={{ from: 'green', to: 'teal' }}>
                      Chart.js Advanced
                    </Badge>
                  </Group>

                  <Text fw={700} size="lg" c="green">⚡ Rapid Development</Text>

                  <div>
                    <Text fw={600} c="green" mb="xs">Perfect for:</Text>
                    <Text size="sm">• Quick dashboard delivery</Text>
                    <Text size="sm">• Standard business charts</Text>
                    <Text size="sm">• Teams with mixed skill levels</Text>
                    <Text size="sm">• Enterprise consistency needs</Text>
                  </div>

                  <div>
                    <Text fw={600} c="blue" mb="xs">Dashboard Impact:</Text>
                    <Text size="sm">Provides professional, consistent appearance across all charts with minimal effort. Ensures reliable performance and cross-browser compatibility.</Text>
                  </div>

                  <div>
                    <Text fw={600} c="orange" mb="xs">Investment Required:</Text>
                    <Text size="sm">Low learning curve with quick implementation. Some customization limitations but excellent for standard needs.</Text>
                  </div>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>

          <Card shadow="xs" padding="lg" radius="md" withBorder mt="xl">
            <Title order={3} mb="md" ta="center">🎯 Strategic Recommendation</Title>

            <Grid>
              <Grid.Col span={4}>
                <Stack gap="sm" ta="center">
                  <Text fw={700} size="lg" c="blue">🚀 For Innovation</Text>
                  <Text size="sm">Choose <strong>D3.js</strong> if you want to create a unique, memorable dashboard experience that differentiates your application.</Text>
                  <Badge variant="light" color="blue">High Impact, High Investment</Badge>
                </Stack>
              </Grid.Col>

              <Grid.Col span={4}>
                <Stack gap="sm" ta="center">
                  <Text fw={700} size="lg" c="green">⚡ For Efficiency</Text>
                  <Text size="sm">Choose <strong>Chart.js</strong> if you need professional results quickly with reliable, enterprise-grade functionality.</Text>
                  <Badge variant="light" color="green">Good Impact, Low Investment</Badge>
                </Stack>
              </Grid.Col>

              <Grid.Col span={4}>
                <Stack gap="sm" ta="center">
                  <Text fw={700} size="lg" c="purple">🎯 Hybrid Approach</Text>
                  <Text size="sm">Use <strong>Chart.js</strong> for standard charts and <strong>D3.js</strong> for key interactive features.</Text>
                  <Badge variant="light" color="purple">Balanced Solution</Badge>
                </Stack>
              </Grid.Col>
            </Grid>
          </Card>
        </Card>
      </Stack>
    </Container>
  );
};

export default ChartingTest;
