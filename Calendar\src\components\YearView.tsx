import { format, addMonths, startOfYear, getDaysInMonth, startOfMonth, addDays, isSameDay, isToday } from 'date-fns'
import { Paper, SimpleGrid, Text, Stack, Badge, Box } from '@mantine/core'
import { CalendarEvent } from '../types/calendar'

interface YearViewProps {
  currentDate: Date
  selectedDate: Date | null
  onMonthSelect: (date: Date) => void
  events: CalendarEvent[]
  onEventClick: (event: CalendarEvent) => void
}

export function YearView({ currentDate, selectedDate, onMonthSelect, events, onEventClick }: YearViewProps) {
  const yearStart = startOfYear(currentDate)
  const months = Array.from({ length: 12 }, (_, i) => addMonths(yearStart, i))
  const weekDays = [
    { key: 'sun', label: 'S' },
    { key: 'mon', label: 'M' },
    { key: 'tue', label: 'T' },
    { key: 'wed', label: 'W' },
    { key: 'thu', label: 'T' },
    { key: 'fri', label: 'F' },
    { key: 'sat', label: 'S' },
  ]

  const getMonthDays = (month: Date) => {
    const daysInMonth = getDaysInMonth(month)
    const firstDay = startOfMonth(month)
    return Array.from({ length: daysInMonth }, (_, i) => addDays(firstDay, i))
  }

  const getEventsForDay = (day: Date): CalendarEvent[] => {
    if (!events) return [];
    return events.filter(event => isSameDay(new Date(event.start), day))
  }

  return (
    <Paper className="flex-1 overflow-auto" p="md">
      <SimpleGrid cols={3} spacing="md">
        {months.map((month) => {
          const monthDays = getMonthDays(month)
          
          return (
            <Paper
              key={month.toString()}
              p="md"
              withBorder
              shadow="xs"
              className="hover:shadow-lg transition-all duration-200 cursor-pointer"
            >
              <Text 
                size="lg" 
                fw={600} 
                mb="md"
                className="hover:text-blue-600 cursor-pointer"
                onClick={() => onMonthSelect(month)}
              >
                {format(month, 'MMMM')}
              </Text>
              <SimpleGrid cols={7} spacing="xs">
                {weekDays.map((day) => (
                  <Text key={day.key} size="xs" fw={500} c="dimmed" ta="center">
                    {day.label}
                  </Text>
                ))}
                {Array.from({ length: monthDays[0].getDay() }, (_, i) => (
                  <div key={`empty-${i}`} />
                ))}
                {monthDays.map((day) => {
                  const dayEvents = getEventsForDay(day);
                  const isCurrentDay = isToday(day);
                  const isSelected = selectedDate ? isSameDay(day, selectedDate) : false;
                  
                  return (
                    <Box key={day.toString()} className="relative">
                      <Paper
                        p="xs"
                        withBorder
                        className={`cursor-pointer transition-colors ${
                          isCurrentDay 
                            ? 'bg-blue-100 dark:bg-blue-900' 
                            : isSelected
                            ? 'bg-gray-100 dark:bg-gray-700'
                            : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                        }`}
                        onClick={(e) => {
                          e.stopPropagation();
                          onMonthSelect(day);
                        }}
                      >
                        <Text 
                          size="sm" 
                          ta="center"
                          fw={isCurrentDay ? 'bold' : 'normal'}
                          c={isCurrentDay ? 'blue' : isSelected ? 'dark' : 'dimmed'}
                        >
                          {format(day, 'd')}
                        </Text>
                        {dayEvents.length > 0 && (
                          <div className="flex justify-center mt-1">
                            <div className="w-1 h-1 rounded-full bg-blue-500"></div>
                          </div>
                        )}
                      </Paper>
                      {dayEvents.length > 0 && (
                        <div className="absolute top-0 right-0 -mt-1 -mr-1">
                          <Badge 
                            size="xs" 
                            variant="filled" 
                            color={dayEvents[0].color || 'blue'}
                            className="h-3 min-w-3 p-0 flex items-center justify-center"
                          >
                            <span className="text-[10px]">{dayEvents.length}</span>
                          </Badge>
                        </div>
                      )}
                    </Box>
                  );
                })}
              </SimpleGrid>
              {getEventsForDay(month).slice(0, 3).map(event => (
                <div 
                  key={event.id} 
                  className="mt-2 text-xs truncate cursor-pointer hover:underline"
                  onClick={(e) => {
                    e.stopPropagation();
                    onEventClick(event);
                  }}
                >
                  <span className="font-medium">
                    {format(new Date(event.start), 'h:mm')} 
                  </span>
                  <span>{event.title}</span>
                </div>
              ))}
              {getEventsForDay(month).length > 3 && (
                <Text size="xs" c="dimmed" mt={4}>
                  +{getEventsForDay(month).length - 3} more
                </Text>
              )}
            </Paper>
          );
        })}
      </SimpleGrid>
    </Paper>
  )
}