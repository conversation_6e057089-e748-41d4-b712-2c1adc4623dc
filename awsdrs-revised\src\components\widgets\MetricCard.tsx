import { Text, Group, ThemeIcon, Stack } from '@mantine/core';
import { IconTrendingUp, IconTrendingDown, IconMinus } from '@tabler/icons-react';

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeType?: 'increase' | 'decrease' | 'neutral';
  icon?: React.ReactNode;
  color?: string;
  subtitle?: string;
}

const MetricCard = ({
  title,
  value,
  change,
  changeType = 'neutral',
  icon,
  color = 'blue',
  subtitle
}: MetricCardProps) => {
  const getTrendIcon = () => {
    switch (changeType) {
      case 'increase':
        return <IconTrendingUp size={16} />;
      case 'decrease':
        return <IconTrendingDown size={16} />;
      default:
        return <IconMinus size={16} />;
    }
  };

  const getTrendColor = () => {
    switch (changeType) {
      case 'increase':
        return 'green';
      case 'decrease':
        return 'red';
      default:
        return 'gray';
    }
  };

  return (
    <Stack gap="md" h="100%" p="md">
      <Group justify="space-between">
        <Text size="sm" c="dimmed" fw={500}>
          {title}
        </Text>
        {icon && (
          <ThemeIcon color={color} variant="light" size="lg">
            {icon}
          </ThemeIcon>
        )}
      </Group>

      <Stack gap="xs" style={{ flex: 1 }}>
        <Text size="xl" fw={700}>
          {value}
        </Text>

        {subtitle && (
          <Text size="xs" c="dimmed">
            {subtitle}
          </Text>
        )}

        {change !== undefined && (
          <Group gap="xs">
            <ThemeIcon color={getTrendColor()} variant="light" size="sm">
              {getTrendIcon()}
            </ThemeIcon>
            <Text size="sm" c={getTrendColor()}>
              {Math.abs(change)}%
            </Text>
            <Text size="xs" c="dimmed">
              vs last period
            </Text>
          </Group>
        )}
      </Stack>
    </Stack>
  );
};

export default MetricCard;
