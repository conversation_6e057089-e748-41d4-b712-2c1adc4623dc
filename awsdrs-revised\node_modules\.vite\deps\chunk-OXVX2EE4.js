import {
  createReactComponent
} from "./chunk-5HMDTYKJ.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowMoveLeft.mjs
var IconArrowMoveLeft = createReactComponent("outline", "arrow-move-left", "IconArrowMoveLeft", [["path", { "d": "M13 12h-10", "key": "svg-0" }], ["path", { "d": "M6 15l-3 -3l3 -3", "key": "svg-1" }], ["path", { "d": "M17 12a2 2 0 1 1 4 0a2 2 0 0 1 -4 0z", "key": "svg-2" }]]);

export {
  IconArrowMoveLeft
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowMoveLeft.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-OXVX2EE4.js.map
