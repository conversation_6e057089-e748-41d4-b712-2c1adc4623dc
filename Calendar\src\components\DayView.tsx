import { format } from 'date-fns'
import { Paper, Stack, Text, Group, Divider } from '@mantine/core'
import { CalendarEvent } from '../types/calendar'
import { EventCard } from './EventCard'

interface DayViewProps {
  currentDate: Date
  selectedDate: Date | null
  onDayClick: (day: Date) => void
  events: CalendarEvent[]
  onEventClick: (event: CalendarEvent) => void
  onTimeSlotClick: (date: Date, hour: number) => void
}

export function DayView({ 
  currentDate, 
  selectedDate, 
  onDayClick, 
  events, 
  onEventClick,
  onTimeSlotClick 
}: DayViewProps) {
  const hours = Array.from({ length: 24 }, (_, i) => i)
  
  const getEventsForDay = () => {
    return events.filter(event => {
      const eventStart = format(event.start, 'yyyy-MM-dd')
      const eventEnd = format(event.end, 'yyyy-MM-dd')
      const currentDay = format(currentDate, 'yyyy-MM-dd')
      
      // Check if the day falls within the event's date range
      return currentDay >= eventStart && currentDay <= eventEnd
    })
  }
  
  const dayEvents = getEventsForDay()
  
  const handleTimeSlotClick = (hour: number) => {
    const clickedDate = new Date(currentDate)
    clickedDate.setHours(hour, 0, 0, 0)
    onTimeSlotClick(clickedDate, hour)
  }

  return (
    <Paper className="flex-1 overflow-auto">
      <Stack gap={0}>
        <Paper p="md" withBorder>
          <Text size="xl" fw={600} ta="center">
            {format(currentDate, 'EEEE, MMMM d, yyyy')}
          </Text>
          {dayEvents.filter(e => e.allDay).length > 0 && (
            <Stack gap="xs" mt="md">
              <Text size="sm" fw={500}>All Day Events</Text>
              <Stack gap="xs">
                {dayEvents.filter(e => e.allDay).map(event => (
                  <EventCard
                    key={event.id}
                    event={event}
                    onClick={onEventClick}
                    isCompact={true}
                  />
                ))}
              </Stack>
            </Stack>
          )}
        </Paper>
        <Stack gap={0}>
          {hours.map((hour) => (
            <Paper
              key={hour}
              p="sm"
              withBorder
              className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
              onClick={() => handleTimeSlotClick(hour)}
            >
              <Group align="flex-start" gap="md">
                <Text size="sm" c="dimmed" style={{ minWidth: '80px', textAlign: 'right' }}>
                  {format(new Date().setHours(hour), 'h:mm a')}
                </Text>
                <Stack gap="xs" style={{ flex: 1, minHeight: '40px' }}>
                  {dayEvents
                    .filter(event => !event.allDay && event.start.getHours() === hour)
                    .map(event => (
                      <EventCard
                        key={event.id}
                        event={event}
                        onClick={onEventClick}
                      />
                    ))}
                </Stack>
              </Group>
            </Paper>
          ))}
        </Stack>
      </Stack>
    </Paper>
  )
}