import { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Box, Text } from '@mantine/core';
import { IconAlertCircle } from '@tabler/icons-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    
    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Update state with error info for the fallback UI
    this.setState({ error, errorInfo });
  }

  private handleReset = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
  };

  public render() {
    if (this.state.hasError) {
      // Use the provided fallback or render the default error UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Box p="md" style={{ minHeight: '100vh', display: 'grid', placeItems: 'center' }}>
          <Box style={{ maxWidth: 600, width: '100%' }}>
            <Alert
              icon={<IconAlertCircle size="1.5rem" />}
              title="Something went wrong"
              color="red"
              variant="filled"
              mb="md"
            >
              <Text mb="sm">
                An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.
              </Text>
              {import.meta.env.DEV && this.state.error && (
                <details style={{ marginTop: '1rem' }}>
                  <summary>Error details</summary>
                  <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                    {this.state.error.toString()}
                    {this.state.errorInfo?.componentStack}
                  </pre>
                </details>
              )}
            </Alert>
            <Button
              onClick={this.handleReset}
              variant="light"
              color="blue"
              fullWidth
            >
              Try again
            </Button>
          </Box>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
