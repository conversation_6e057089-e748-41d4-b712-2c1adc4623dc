<!--$Id $-->
<!-- $Id: server.xml,v 1.10 2010/03/03 05:44:35 suresh Exp $ -->
<!-- Example Server Configuration File -->
<!-- Note that component elements are nested corresponding to their
     parent-child relationships with each other -->

<!-- A "Server" is a singleton element that represents the entire JVM,
     which may contain one or more "Service" instances.  The Server
     listens for a shutdown command on the indicated port.

     Note:  A "Server" is not itself a "Container", so you may not
     define subcomponents such as "Valves" or "Loggers" at this level.
 -->

<Server>


  <!-- Comment these entries out to disable JMX MBeans support -->
  <!-- You may also configure custom components (e.g. Valves/Realms) by 
       including your own mbean-descriptor file(s), and setting the 
       "descriptors" attribute to point to a ';' seperated list of paths
       (in the ClassLoader sense) of files to add to the default list.
       e.g. descriptors="/com/myfirm/mypackage/mbean-descriptor.xml"
  -->
	<!--Listener className="org.apache.catalina.core.AprLifecycleListener" SSLEngine="on"/-->
  <!--
  <Listener className="org.apache.catalina.mbeans.ServerLifecycleListener"
            debug="0"/>
  <Listener className="org.apache.catalina.mbeans.GlobalResourcesLifecycleListener"
            debug="0"/>
		-->
  <!-- Global JNDI resources -->
  <GlobalNamingResources>

    <!-- Test entry for demonstration purposes -->
    <Environment name="simpleValue" type="java.lang.Integer" value="30"/>

    <!-- Editable user database that can also be used by
         UserDatabaseRealm to authenticate users -->
    <Resource name="UserDatabase" auth="Container"
              type="org.apache.catalina.UserDatabase"
       description="User database that can be updated and saved">
    </Resource>
    <ResourceParams name="UserDatabase">
      <parameter>
        <name>factory</name>
        <value>org.apache.catalina.users.MemoryUserDatabaseFactory</value>
      </parameter>
      <parameter>
        <name>pathname</name>
        <value>conf/tomcat-users.xml</value>
      </parameter>
    </ResourceParams>

  </GlobalNamingResources>

  <!-- A "Service" is a collection of one or more "Connectors" that share
       a single "Container" (and therefore the web applications visible
       within that Container).  Normally, that Container is an "Engine",
       but this is not required.

       Note:  A "Service" is not itself a "Container", so you may not
       define subcomponents such as "Valves" or "Loggers" at this level.
   -->

  <!-- Define the Tomcat Stand-Alone Service -->
  <Service name="Catalina">

    <!-- A "Connector" represents an endpoint by which requests are received
         and responses are returned.  Each Connector passes requests on to the
         associated "Container" (normally an Engine) for processing.

         By default, a non-SSL HTTP/1.1 Connector is established on port 8080.
         You can also enable an SSL HTTP/1.1 Connector on port 8443 by
         following the instructions below and uncommenting the second Connector
         entry.  SSL support requires the following steps (see the SSL Config
         HOWTO in the Tomcat 5 documentation bundle for more detailed
         instructions):
         * If your JDK version 1.3 or prior, download and install JSSE 1.0.2 or
           later, and put the JAR files into "$JAVA_HOME/jre/lib/ext".
         * Execute:
             %JAVA_HOME%\bin\keytool -genkey -alias tomcat -keyalg RSA (Windows)
             $JAVA_HOME/bin/keytool -genkey -alias tomcat -keyalg RSA  (Unix)
           with a password value of "changeit" for both the certificate and
           the keystore itself.

         By default, DNS lookups are enabled when a web application calls
         request.getRemoteHost().  This can have an adverse impact on
         performance, so you can disable it by setting the
         "enableLookups" attribute to "false".  When DNS lookups are disabled,
         request.getRemoteHost() will return the String version of the
         IP address of the remote client.
    -->

    <!-- Define a non-SSL Coyote HTTP/1.1 Connector on port 8080 -->
<!--
    <Connector port="8080"
               maxThreads="150" minSpareThreads="25" maxSpareThreads="75"
               enableLookups="false" redirectPort="8443" acceptCount="100"
               debug="3" connectionTimeout="20000" 
               disableUploadTimeout="true" /> 
-->
    <!-- Note : To disable connection timeouts, set connectionTimeout value
     to -1 -->
	
	<!-- Note : To use gzip compression you could set the following properties :
	
			   compression="on" 
			   compressionMinSize="2048" 
			   noCompressionUserAgents="gozilla, traviata" 
			   compressableMimeType="text/html,text/xml"
	-->

    <!-- Define a SSL Coyote HTTP/1.1 Connector on port 8443 -->
	    <!--Connector port="8443" 
               maxThreads="150" minSpareThreads="25" maxSpareThreads="75"
               enableLookups="false" disableUploadTimeout="true"
               acceptCount="100" debug="0" scheme="https" secure="true"
			   clientAuth="false" sslProtocol="TLS" keystoreFile="./conf/NFATrans.key" keystorePass="netflow" connectionTimeout="20000"/-->


<Connector port="WEBSERVER_PORT" address="SERVER_HOST" SSLEnabled="true" acceptCount="100" clientAuth="false" compressibleMimeType="text/html,text/xml,text/javascript,application/x-javascript,application/javascript,text/css,image/gif" compression="on" compressionMinSize="1024" connectionTimeout="20000" disableUploadTimeout="true" enableLookups="false" keystoreFile="${javax.net.ssl.keyStore}" keystorePass="${javax.net.ssl.keyStorePassword}" maxSpareThreads="75" maxThreads="150" minSpareThreads="25" noCompressionUserAgents="gozilla, traviata" protocol="org.apache.coyote.http11.Http11Nio2Protocol" scheme="https" secure="true" sslProtocol="TLS" URIEncoding="UTF-8" relaxedQueryChars="[]" SSLEnabledProtocols="${jdk.tls.server.protocols}" ciphers="${server.https.ciphers}" honorCipherOrder="true"/>
<!--Connector SSLEnabled="true" URIEncoding="UTF-8" compressableMimeType="text/html,text/xml" compression="force" compressionMinSize="1024" connectionTimeout="20000" noCompressionUserAgents="gozilla, traviata" port="444" protocol="org.apache.coyote.http11.Http11Nio2Protocol" scheme="https" secure="true">
	<UpgradeProtocol className="org.apache.coyote.http2.Http2Protocol"/>
    <SSLHostConfig sslProtocol="all" ciphers="ECDHE-ECDSA-AES256-GCM-SHA384,ECDHE-RSA-AES256-GCM-SHA384,ECDHE-ECDSA-CHACHA20-POLY1305,ECDHE-RSA-CHACHA20-POLY1305,ECDHE-ECDSA-AES128-GCM-SHA256,ECDHE-RSA-AES128-GCM-SHA256,ECDHE-ECDSA-AES256-SHA384,ECDHE-RSA-AES256-SHA384,ECDHE-ECDSA-AES128-SHA256,ECDHE-RSA-AES128-SHA256" >
        <Certificate certificateKeystoreFile="./conf/OPMTrans.key" certificateKeystorePassword="opmanager"/>
    </SSLHostConfig>
</Connector-->
		   

    <!-- Define a Coyote/JK2 AJP 1.3 Connector on port 8009 -->
	<!--
    <Connector port="8009" 
               enableLookups="false" redirectPort="8443" debug="0"
               protocol="AJP/1.3" />
	-->
    <!-- Define a Proxied HTTP/1.1 Connector on port 8082 -->
    <!-- See proxy documentation for more information about using this. -->
    <!--
    <Connector port="8082" 
               maxThreads="150" minSpareThreads="25" maxSpareThreads="75"
               enableLookups="false"
               acceptCount="100" debug="0" connectionTimeout="20000"
               proxyPort="80" disableUploadTimeout="true" />
    -->

    <!-- This connector is to check the status of the radius service-->
    <!--
    <Connector port="8494" 
               maxThreads="150" minSpareThreads="25" maxSpareThreads="75"
               enableLookups="false" disableUploadTimeout="true"
               acceptCount="100" debug="0" connectionTimeout="-1"/>
    -->
               
    <!-- An Engine represents the entry point (within Catalina) that processes
         every request.  The Engine implementation for Tomcat stand alone
         analyzes the HTTP headers included with the request, and passes them
         on to the appropriate Host (virtual host). -->

    <!-- You should set jvmRoute to support load-balancing via JK/JK2 ie :
    <Engine name="Standalone" defaultHost="localhost" debug="0" jvmRoute="jvm1">         
    --> 
         
    <!-- Define the top level container in our container hierarchy -->
    <Engine name="Catalina" defaultHost="localhost" debug="0">

      <!-- The request dumper valve dumps useful debugging information about
           the request headers and cookies that were received, and the response
           headers and cookies that were sent, for all requests received by
           this instance of Tomcat.  If you care only about requests to a
           particular virtual host, or a particular application, nest this
           element inside the corresponding <Host> or <Context> entry instead.

           For a similar mechanism that is portable to all Servlet 2.4
           containers, check out the "RequestDumperFilter" Filter in the
           example application (the source for this filter may be found in
           "$CATALINA_HOME/webapps/examples/WEB-INF/classes/filters").

           Request dumping is disabled by default.  Uncomment the following
           element to enable it. -->
      <!--
      <Valve className="org.apache.catalina.valves.RequestDumperValve"/>
      -->

      <!-- Global logger unless overridden at lower levels -->
      <!--Logger className="org.apache.catalina.logger.FileLogger"
              prefix="catalina_log." suffix=".txt"
              timestamp="true"/-->

      <!-- Because this Realm is here, an instance will be shared globally -->

      <!-- This Realm uses the UserDatabase configured in the global JNDI
           resources under the key "UserDatabase".  Any edits
           that are performed against this UserDatabase are immediately
           available for use by the Realm.  -->
      <!--Realm className="org.apache.catalina.realm.UserDatabaseRealm"
                 debug="0" resourceName="UserDatabase"/-->

      <!-- Comment out the old realm but leave here for now in case we
           need to go back quickly -->
      <!--
      <Realm className="org.apache.catalina.realm.MemoryRealm" />
      -->

      <!-- Replace the above Realm with one of the following to get a Realm
           stored in a database and accessed via JDBC -->

      <!--
      <Realm  className="org.apache.catalina.realm.JDBCRealm" debug="99"
             driverName="org.gjt.mm.mysql.Driver"
          connectionURL="********************************"
         connectionName="test" connectionPassword="test"
              userTable="users" userNameCol="user_name" userCredCol="user_pass"
          userRoleTable="user_roles" roleNameCol="role_name" />
      -->

      <!--
      <Realm  className="org.apache.catalina.realm.JDBCRealm" debug="99"
             driverName="oracle.jdbc.driver.OracleDriver"
          connectionURL="************************************"
         connectionName="scott" connectionPassword="tiger"
              userTable="users" userNameCol="user_name" userCredCol="user_pass"
          userRoleTable="user_roles" roleNameCol="role_name" />
      -->

      <!--
      <Realm  className="org.apache.catalina.realm.JDBCRealm" debug="99"
             driverName="sun.jdbc.odbc.JdbcOdbcDriver"
          connectionURL="jdbc:odbc:CATALINA"
              userTable="users" userNameCol="user_name" userCredCol="user_pass"
          userRoleTable="user_roles" roleNameCol="role_name" />
      -->
    <Realm
        className="com.adventnet.authentication.realm.CustomJAASRealm"
        appName="MickeyLite"
        userClassNames="com.adventnet.authentication.UserPrincipal"
	roleClassNames="com.adventnet.authentication.RolePrincipal"
	allRolesMode="strictAuthOnly">
    </Realm>
      
      <!-- Define the default virtual host
           Note: XML Schema validation will not work with Xerces 2.2.
       -->
      <Host name="localhost" hostConfigClass="org.apache.catalina.startup.OpManagerHostConfig" debug="0" appBase="webapps"
       unpackWARs="true" autoDeploy="false"
       xmlValidation="false" xmlNamespaceAware="false" errorReportValveClass="com.adventnet.me.opmanager.server.util.CustomErrorReportValve">

        <!-- Defines a cluster for this node,
             By defining this element, means that every manager will be changed.
             So when running a cluster, only make sure that you have webapps in there
             that need to be clustered and remove the other ones.
             A cluster has the following parameters:

             className = the fully qualified name of the cluster class

             name = a descriptive name for your cluster, can be anything

             debug = the debug level, higher means more output

             mcastAddr = the multicast address, has to be the same for all the nodes

             mcastPort = the multicast port, has to be the same for all the nodes

             mcastFrequency = the number of milliseconds in between sending a "I'm alive" heartbeat

             mcastDropTime = the number a milliseconds before a node is considered "dead" if no heartbeat is received

             tcpThreadCount = the number of threads to handle incoming replication requests, optimal would be the same amount of threads as nodes 

             tcpListenAddress = the listen address (bind address) for TCP cluster request on this host, 
                                in case of multiple ethernet cards.
                                auto means that address becomes
                                InetAddress.getLocalHost().getHostAddress()

             tcpListenPort = the tcp listen port

             tcpSelectorTimeout = the timeout (ms) for the Selector.select() method in case the OS
                                  has a wakup bug in java.nio. Set to 0 for no timeout

             printToScreen = true means that managers will also print to std.out

             expireSessionsOnShutdown = true means that 

             useDirtyFlag = true means that we only replicate a session after setAttribute,removeAttribute has been called.
                            false means to replicate the session after each request.
                            false means that replication would work for the following piece of code:
                            <%
                            HashMap map = (HashMap)session.getAttribute("map");
                            map.put("key","value");
                            %>
             replicationMode = can be either 'synchronous' or 'asynchronous'.
                               * Synchronous means that the thread that executes the request, is also the
                               thread the replicates the data to the other nodes, and will not return until all
                               nodes have received the information.
                               * Asynchronous means that there is a specific 'sender' thread for each cluster node,
                               so the request thread will queue the replication request into a "smart" queue,
                               and then return to the client.
                               The "smart" queue is a queue where when a session is added to the queue, and the same session
                               already exists in the queue from a previous request, that session will be replaced
                               in the queue instead of replicating two requests. This almost never happens, unless there is a 
                               large network delay.
        -->             
        
        <!-- When uncommenting the cluster, REMEMBER to uncomment the replication Valve below as well
        

        <Cluster  className="org.apache.catalina.cluster.tcp.SimpleTcpCluster"
                  name="FilipsCluster"
                  debug="10"
                  serviceclass="org.apache.catalina.cluster.mcast.McastService"
                  mcastAddr="*********"
                  mcastPort="45564"
                  mcastFrequency="500"
                  mcastDropTime="3000"
                  tcpThreadCount="2"
                  tcpListenAddress="auto"
                  tcpListenPort="4001"
                  tcpSelectorTimeout="100"
                  printToScreen="false"
                  expireSessionsOnShutdown="false"
                  useDirtyFlag="true"
                  replicationMode="synchronous"
        />
        -->
        <!--
            When configuring for clustering, you also add in a valve to catch all the requests
            coming in, at the end of the request, the session may or may not be replicated.
            A session is replicated if and only if all the conditions are met:
            1. useDirtyFlag is true or setAttribute or removeAttribute has been called AND
            2. a session exists (has been created)
            3. the request is not trapped by the "filter" attribute

            The filter attribute is to filter out requests that could not modify the session,
            hence we don't replicate the session after the end of this request.
            The filter is negative, ie, anything you put in the filter, you mean to filter out,
            ie, no replication will be done on requests that match one of the filters.
            The filter attribute is delimited by ;, so you can't escape out ; even if you wanted to.

            filter=".*\.gif;.*\.js;" means that we will not replicate the session after requests with the URI
            ending with .gif and .js are intercepted.
        -->
        <!--
        <Valve className="org.apache.catalina.cluster.tcp.ReplicationValve"
               filter=".*\.gif;.*\.js;.*\.jpg;.*\.htm;.*\.html;.*\.txt;"/>

        -->
        <!-- Normally, users must authenticate themselves to each web app
             individually.  Uncomment the following entry if you would like
             a user to be authenticated the first time they encounter a
             resource protected by a security constraint, and then have that
             user identity maintained across *all* web applications contained
             in this virtual host. -->
        <Valve className="org.apache.catalina.authenticator.SingleSignOn" debug="0"/>
       
       <!-- Access log processes all requests for this virtual host.  By
             default, log files are created in the "logs" directory relative to
             $CATALINA_HOME.  If you wish, you can specify a different
             directory with the "directory" attribute.  Specify either a relative
             (to $CATALINA_HOME) or absolute path to the desired directory.
        -->
        <!--Valve className="org.apache.catalina.valves.AccessLogValve"
                 directory="logs"  prefix="localhost_access_log." suffix=".txt"
                 pattern="common" resolveHosts="false"/-->
          <Valve className="org.apache.catalina.valves.ITOMAccessLogValve" directory="logs" pattern="${itom.accessLog.pattern}" prefix="access_log" renameOnRotate="true" resolveHosts="false" rotatable="true" suffix=".txt" maxDays="30"/>
        <!-- Logger shared by all Contexts related to this virtual host.  By
             default (when using FileLogger), log files are created in the "logs"
             directory relative to $CATALINA_HOME.  If you wish, you can specify
             a different directory with the "directory" attribute.  Specify either a
             relative (to $CATALINA_HOME) or absolute path to the desired
             directory.-->
			 <Logger className="org.apache.catalina.logger.FileLogger"
                 directory="logs"  prefix="localhost_log." suffix=".txt"
				 timestamp="true"/>
			 <!--	 <Logger className="com.adventnet.mfw.log.TomcatLog"
                 directory="logs"  prefix="localhost_log." suffix=".txt"
				 timestamp="true"/>-->

        <!-- Define properties for each web application.  This is only needed
             if you want to set non-default properties, or have web application
             document roots in places other than the virtual host's appBase
             directory.  -->

        <!-- Tomcat Root Context -->
        <Context debug="0" docBase="${server.home}" path="" reloadable="true">
		</Context>
      </Host>

    </Engine>

  </Service>

</Server>
