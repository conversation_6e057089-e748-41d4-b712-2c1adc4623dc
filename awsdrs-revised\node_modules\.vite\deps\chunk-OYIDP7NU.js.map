{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconNavigationShare.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'navigation-share', 'IconNavigationShare', [[\"path\",{\"d\":\"M16.633 13.043l-4.633 -10.043l-7.97 17.275c-.07 .2 -.017 .424 .135 .572c.15 .148 .374 .193 .57 .116l7.265 -2.463l.955 .324\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M16 22l5 -5\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M21 21.5v-4.5h-4.5\",\"key\":\"svg-2\"}]]);"], "mappings": ";;;;;AACA,IAAe,sBAAA,qBAAqB,WAAW,oBAAoB,uBAAuB,CAAC,CAAC,QAAO,EAAC,KAAI,8HAA6H,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,eAAc,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,sBAAqB,OAAM,QAAO,CAAC,CAAC,CAAC;", "names": []}