import { useEffect, useState, useCallback } from 'react';
import { Text, Card } from '@mantine/core';
import { useAppDispatch, useAppSelector } from '../store';
import { fetchServers } from '../store/slices/serverSlice';
import DashboardLayout from '../layouts/DashboardLayout';
import { DashboardWidget } from '../types/dashboard';
import WidgetMenu from '../components/WidgetMenu';
import { useLayoutManager } from '../hooks/useLayoutManager';
import { Responsive, WidthProvider } from 'react-grid-layout';
import type { Layout } from 'react-grid-layout';

const ResponsiveGridLayout = WidthProvider(Responsive);

// Import widget components
import ServerMetricsWidget from '../components/widgets/ServerMetricsWidget';
import ServerStatusWidget from '../components/widgets/ServerStatusWidget';
import LagTrendWidget from '../components/widgets/LagTrendWidget';
import RegionDistributionWidget from '../components/widgets/RegionDistributionWidget';
import ServerTableWidget from '../components/widgets/ServerTableWidget';
import AlertsWidget from '../components/widgets/AlertsWidget';

// Default widgets for AWS DRS dashboard
const defaultWidgets: DashboardWidget[] = [
  { id: 'server-metrics', title: 'Server Metrics Overview', type: 'server-metrics' },
  { id: 'server-status', title: 'Server Status', type: 'server-status' },
  { id: 'lag-trend', title: 'Replication Lag Trend', type: 'lag-trend' },
  { id: 'region-distribution', title: 'Region Distribution', type: 'region-distribution' },
  { id: 'server-table', title: 'Server Details', type: 'server-table' },
  { id: 'alerts', title: 'Alerts & Issues', type: 'alerts' }
];

// Default layout configuration
const defaultLayouts = {
  lg: [
    { i: 'server-metrics', x: 0, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 'alerts', x: 3, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 'server-status', x: 6, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 'region-distribution', x: 0, y: 2, w: 4, h: 3, minW: 3, minH: 2 },
    { i: 'lag-trend', x: 4, y: 2, w: 5, h: 3, minW: 4, minH: 2 },
    { i: 'server-table', x: 0, y: 5, w: 9, h: 6, minW: 6, minH: 4 }
  ],
  md: [
    { i: 'server-metrics', x: 0, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 'alerts', x: 3, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 'server-status', x: 6, y: 0, w: 4, h: 2, minW: 2, minH: 2 },
    { i: 'region-distribution', x: 0, y: 2, w: 4, h: 3, minW: 3, minH: 2 },
    { i: 'lag-trend', x: 4, y: 2, w: 6, h: 3, minW: 4, minH: 2 },
    { i: 'server-table', x: 0, y: 5, w: 10, h: 6, minW: 8, minH: 4 }
  ],
  sm: [
    { i: 'server-metrics', x: 0, y: 0, w: 6, h: 2, minW: 4, minH: 2 },
    { i: 'alerts', x: 0, y: 2, w: 6, h: 2, minW: 4, minH: 2 },
    { i: 'server-status', x: 0, y: 4, w: 6, h: 2, minW: 4, minH: 2 },
    { i: 'region-distribution', x: 0, y: 6, w: 6, h: 3, minW: 6, minH: 2 },
    { i: 'lag-trend', x: 0, y: 9, w: 6, h: 3, minW: 6, minH: 2 },
    { i: 'server-table', x: 0, y: 12, w: 6, h: 6, minW: 6, minH: 4 }
  ]
};

// Widget factory function
const renderWidget = (widget: DashboardWidget) => {
  switch (widget.type) {
    case 'server-metrics':
      return <ServerMetricsWidget widget={widget} />;
    case 'server-status':
      return <ServerStatusWidget widget={widget} />;
    case 'lag-trend':
      return <LagTrendWidget widget={widget} />;
    case 'region-distribution':
      return <RegionDistributionWidget widget={widget} />;
    case 'server-table':
      return <ServerTableWidget widget={widget} />;
    case 'alerts':
      return <AlertsWidget widget={widget} />;
    default:
      return (
        <Card h="100%" p="md">
          <Text c="dimmed" ta="center">
            Widget: {widget.type}
          </Text>
        </Card>
      );
  }
};

const getInitialMenuPosition = () => ({
  x: Math.max(50, window.innerWidth - 370),
  y: 100
});

const Dashboard = () => {
  const dispatch = useAppDispatch();
  const { loading } = useAppSelector((state) => state.servers);

  // Layout management with multiple presets
  const {
    layouts,
    layoutPresets,
    currentLayoutId,
    switchToLayout,
    saveCurrentLayout,
    deleteLayout,
    resetToDefault,
    handleLayoutChange
  } = useLayoutManager({
    pageKey: 'dashboard',
    defaultLayouts: defaultLayouts,
    defaultLayoutName: 'Default Dashboard Layout'
  });

  const [isEditing, setIsEditing] = useState(false);
  const [widgets, setWidgets] = useState(defaultWidgets);
  const [visibleWidgets, setVisibleWidgets] = useState(new Set(defaultWidgets.map(w => w.id)));
  const [showWidgetMenu, setShowWidgetMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState(getInitialMenuPosition());
  const [isDragging, setIsDragging] = useState(false);

  useEffect(() => {
    dispatch(fetchServers());
  }, [dispatch]);

  const handleRefresh = useCallback(() => {
    dispatch(fetchServers());
  }, [dispatch]);

  // Load saved widget visibility
  useEffect(() => {
    try {
      const savedVisibility = localStorage.getItem('dashboard-visible-widgets');
      if (savedVisibility) {
        setVisibleWidgets(new Set(JSON.parse(savedVisibility)));
      }
    } catch (error) {
      console.error('Error loading widget visibility:', error);
    }
  }, []);

  const toggleWidgetVisibility = (widgetId: string) => {
    setVisibleWidgets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(widgetId)) {
        newSet.delete(widgetId);
      } else {
        newSet.add(widgetId);
      }
      localStorage.setItem('dashboard-visible-widgets', JSON.stringify([...newSet]));
      return newSet;
    });
  };

  const closeWidgetMenu = () => {
    setShowWidgetMenu(false);
  };

  const resetToDefaultAndClose = () => {
    resetToDefault();
    setShowWidgetMenu(false);
    setMenuPosition(getInitialMenuPosition());
  };

  // Optimized drag handlers for floating menu
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target !== e.currentTarget && !(e.target as Element).closest('.widget-menu-header')) return;

    setIsDragging(true);
    const startX = e.clientX - menuPosition.x;
    const startY = e.clientY - menuPosition.y;

    const handleMouseMove = (e: MouseEvent) => {
      const newX = Math.max(0, Math.min(window.innerWidth - 300, e.clientX - startX));
      const newY = Math.max(0, Math.min(window.innerHeight - 200, e.clientY - startY));

      setMenuPosition({ x: newX, y: newY });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const allWidgets = widgets.map(widget => ({
    ...widget,
    isVisible: visibleWidgets.has(widget.id)
  }));

  const dashboardControls = {
    isEditing,
    loading,
    showWidgetMenu,
    onToggleEdit: () => setIsEditing(!isEditing),
    onRefresh: handleRefresh,
    onResetLayout: resetToDefaultAndClose,
    onToggleWidgetMenu: () => {
      if (!showWidgetMenu) {
        setMenuPosition(getInitialMenuPosition());
      }
      setShowWidgetMenu(!showWidgetMenu);
    }
  };

  return (
    <DashboardLayout dashboardControls={dashboardControls}>
      <div style={{ width: '100%', position: 'relative', maxWidth: '1400px', margin: '0 auto' }}>

        {/* Dashboard Grid */}
        <div style={{ flex: 1, overflow: 'auto', paddingTop: '40px' }}>
          <ResponsiveGridLayout
            className={`layout ${isEditing ? 'editing' : ''}`}
            layouts={layouts}
            onLayoutChange={(layout, layouts) => {
              if (isEditing) {
                handleLayoutChange(layout, layouts);
              }
            }}
            breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
            cols={{ lg: 9, md: 10, sm: 6, xs: 4, xxs: 2 }}
            rowHeight={60}
            isDraggable={isEditing}
            isResizable={isEditing}
            margin={[16, 16]}
            containerPadding={[16, 16]}
            useCSSTransforms={true}
            compactType="vertical"
            preventCollision={false}
            allowOverlap={false}
          >
            {allWidgets.map((widget) => {
              const isVisible = visibleWidgets.has(widget.id);
              return (
                <div
                  key={widget.id}
                  className="widget-container"
                  style={{
                    display: isVisible ? 'block' : 'none'
                  }}
                >
                  <Card
                    h="100%"
                    p={0}
                    style={{
                      position: 'relative',
                      overflow: 'hidden',
                      border: isEditing ? '2px solid var(--mantine-color-blue-4)' : '1px solid var(--mantine-color-gray-3)',
                      borderRadius: '8px',
                      backgroundColor: 'var(--mantine-color-body)'
                    }}
                  >
                    {renderWidget(widget)}
                  </Card>
                </div>
              );
            })}
          </ResponsiveGridLayout>
        </div>

        {/* Floating Widget Selection Menu */}
        {showWidgetMenu && (
          <WidgetMenu
            widgets={widgets}
            visibleWidgets={visibleWidgets}
            onToggleWidgetVisibility={toggleWidgetVisibility}
            layoutPresets={layoutPresets}
            currentLayoutId={currentLayoutId}
            onLayoutChange={switchToLayout}
            onSaveLayout={saveCurrentLayout}
            onDeleteLayout={deleteLayout}
            onResetToDefault={resetToDefaultAndClose}
            menuPosition={menuPosition}
            isDragging={isDragging}
            onMouseDown={handleMouseDown}
            onClose={closeWidgetMenu}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;