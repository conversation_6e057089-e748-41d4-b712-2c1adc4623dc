import {
  createReactComponent
} from "./chunk-5HMDTYKJ.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowsDown.mjs
var IconArrowsDown = createReactComponent("outline", "arrows-down", "IconArrowsDown", [["path", { "d": "M7 21l0 -18", "key": "svg-0" }], ["path", { "d": "M20 18l-3 3l-3 -3", "key": "svg-1" }], ["path", { "d": "M4 18l3 3l3 -3", "key": "svg-2" }], ["path", { "d": "M17 21l0 -18", "key": "svg-3" }]]);

export {
  IconArrowsDown
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowsDown.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-OZE2ZFT5.js.map
