import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { CalendarEvent, EventCategory } from '../types/calendar';

type ErrorType = 'validation' | 'not_found' | 'storage' | 'unknown';

interface AppError {
  id: string;
  type: ErrorType;
  message: string;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

const createError = (type: ErrorType, message: string, metadata?: Record<string, unknown>): AppError => ({
  id: crypto.randomUUID(),
  type,
  message,
  timestamp: new Date(),
  metadata
});

interface CalendarState {
  // Events state
  events: CalendarEvent[];
  errors: AppError[];
  lastError: AppError | null;
  addEvent: (event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>) => { success: boolean; error?: AppError };
  updateEvent: (id: string, updates: Partial<CalendarEvent>) => { success: boolean; error?: AppError };
  deleteEvent: (id: string) => { success: boolean; error?: AppError };
  getEventsForDate: (date: Date) => CalendarEvent[];
  getEventsForDateRange: (startDate: Date, endDate: Date) => CalendarEvent[];
  clearError: (errorId: string) => void;
  clearAllErrors: () => void;
  
  // UI state
  currentDate: Date;
  selectedDate: Date | null;
  view: 'day' | 'workweek' | 'week' | 'month' | 'year';
  setCurrentDate: (date: Date) => void;
  setSelectedDate: (date: Date | null) => void;
  setView: (view: 'day' | 'workweek' | 'week' | 'month' | 'year') => void;
  nextPeriod: () => void;
  prevPeriod: () => void;
  
  // Categories
  categories: EventCategory[];
  addCategory: (category: Omit<EventCategory, 'id'>) => { success: boolean; error?: AppError };
  updateCategory: (id: string, updates: Partial<EventCategory>) => { success: boolean; error?: AppError };
  deleteCategory: (id: string) => { success: boolean; error?: AppError };
  
  // Validation
  validateEvent: (event: Partial<CalendarEvent>) => { isValid: boolean; errors: string[] };
  validateCategory: (category: Partial<EventCategory>) => { isValid: boolean; errors: string[] };
}

export const useCalendarStore = create<CalendarState>()(
  persist(
    (set, get) => ({
      // Initial state
      events: [],
      errors: [],
      lastError: null,
      currentDate: new Date(),
      selectedDate: null,
      view: 'month',
      categories: [
        { id: 'work', name: 'Work', color: '#3B82F6' },
        { id: 'personal', name: 'Personal', color: '#10B981' },
        { id: 'meeting', name: 'Meeting', color: '#F59E0B' },
        { id: 'reminder', name: 'Reminder', color: '#EF4444' },
        { id: 'event', name: 'Event', color: '#8B5CF6' },
      ],
      
      // Error handling
      clearError: (errorId) => {
        set((state) => ({
          errors: state.errors.filter((error) => error.id !== errorId),
          lastError: state.lastError?.id === errorId ? null : state.lastError,
        }));
      },
      
      clearAllErrors: () => {
        set({ errors: [], lastError: null });
      },

      // Validation helpers
      validateEvent: (event) => {
        const errors: string[] = [];
        if (!event.title?.trim()) {
          errors.push('Event title is required');
        }
        if (!event.start) {
          errors.push('Start date is required');
        }
        if (event.end && new Date(event.end) < new Date(event.start as string)) {
          errors.push('End date must be after start date');
        }
        return {
          isValid: errors.length === 0,
          errors
        };
      },
      
      validateCategory: (category) => {
        const errors: string[] = [];
        if (!category.name?.trim()) {
          errors.push('Category name is required');
        }
        if (!category.color) {
          errors.push('Category color is required');
        }
        return {
          isValid: errors.length === 0,
          errors
        };
      },
      
      // Event actions
      addEvent: (eventData) => {
        const { isValid, errors } = get().validateEvent(eventData);
        if (!isValid) {
          const error = createError('validation', 'Invalid event data', { errors });
          set((state) => ({
            errors: [...state.errors, error],
            lastError: error,
          }));
          return { success: false, error };
        }
        
        try {
          const newEvent: CalendarEvent = {
            ...eventData,
            id: crypto.randomUUID(),
            createdAt: new Date(),
            updatedAt: new Date(),
          };
          set((state) => ({
            events: [...state.events, newEvent],
          }));
          return { success: true };
        } catch (error) {
          const appError = createError('unknown', 'Failed to add event', { error });
          set((state) => ({
            errors: [...state.errors, appError],
            lastError: appError,
          }));
          return { success: false, error: appError };
        }
      },

      updateEvent: (id, updates) => {
        try {
          const event = get().events.find(e => e.id === id);
          if (!event) {
            const error = createError('not_found', 'Event not found', { eventId: id });
            set((state) => ({
              errors: [...state.errors, error],
              lastError: error,
            }));
            return { success: false, error };
          }
          
          const updatedEvent = { ...event, ...updates, updatedAt: new Date() };
          const { isValid, errors } = get().validateEvent(updatedEvent);
          
          if (!isValid) {
            const error = createError('validation', 'Invalid event data', { errors });
            set((state) => ({
              errors: [...state.errors, error],
              lastError: error,
            }));
            return { success: false, error };
          }
          
          set((state) => ({
            events: state.events.map((event) =>
              event.id === id ? updatedEvent : event
            ),
          }));
          return { success: true };
        } catch (error) {
          const appError = createError('unknown', 'Failed to update event', { error });
          set((state) => ({
            errors: [...state.errors, appError],
            lastError: appError,
          }));
          return { success: false, error: appError };
        }
      },

      deleteEvent: (id) => {
        try {
          const eventExists = get().events.some(event => event.id === id);
          if (!eventExists) {
            const error = createError('not_found', 'Event not found', { eventId: id });
            set((state) => ({
              errors: [...state.errors, error],
              lastError: error,
            }));
            return { success: false, error };
          }
          
          set((state) => ({
            events: state.events.filter((event) => event.id !== id),
          }));
          return { success: true };
        } catch (error) {
          const appError = createError('unknown', 'Failed to delete event', { error });
          set((state) => ({
            errors: [...state.errors, appError],
            lastError: appError,
          }));
          return { success: false, error: appError };
        }
      },

      getEventsForDate: (date) => {
        const dateStr = date.toISOString().split('T')[0];
        return get().events.filter((event) => {
          const startStr = new Date(event.start).toISOString().split('T')[0];
          const endStr = new Date(event.end).toISOString().split('T')[0];
          return dateStr >= startStr && dateStr <= endStr;
        });
      },

      getEventsForDateRange: (startDate, endDate) => {
        const startStr = startDate.toISOString().split('T')[0];
        const endStr = endDate.toISOString().split('T')[0];
        
        return get().events.filter((event) => {
          const eventStartStr = new Date(event.start).toISOString().split('T')[0];
          const eventEndStr = new Date(event.end).toISOString().split('T')[0];
          return (
            (eventStartStr >= startStr && eventStartStr <= endStr) ||
            (eventEndStr >= startStr && eventEndStr <= endStr) ||
            (eventStartStr <= startStr && eventEndStr >= endStr)
          );
        });
      },

      // UI actions
      setCurrentDate: (date) => set({ currentDate: date }),
      setSelectedDate: (date) => set({ selectedDate: date }),
      setView: (view) => set({ view }),
      
      nextPeriod: () => {
        const { currentDate, view } = get();
        const newDate = new Date(currentDate);
        
        switch (view) {
          case 'day':
            newDate.setDate(newDate.getDate() + 1);
            break;
          case 'workweek':
          case 'week':
            newDate.setDate(newDate.getDate() + 7);
            break;
          case 'month':
            newDate.setMonth(newDate.getMonth() + 1);
            break;
          case 'year':
            newDate.setFullYear(newDate.getFullYear() + 1);
            break;
        }
        
        set({ currentDate: newDate });
      },
      
      prevPeriod: () => {
        const { currentDate, view } = get();
        const newDate = new Date(currentDate);
        
        switch (view) {
          case 'day':
            newDate.setDate(newDate.getDate() - 1);
            break;
          case 'workweek':
          case 'week':
            newDate.setDate(newDate.getDate() - 7);
            break;
          case 'month':
            newDate.setMonth(newDate.getMonth() - 1);
            break;
          case 'year':
            newDate.setFullYear(newDate.getFullYear() - 1);
            break;
        }
        
        set({ currentDate: newDate });
      },

      // Category actions
      addCategory: (category) => {
        const { isValid, errors } = get().validateCategory(category);
        if (!isValid) {
          const error = createError('validation', 'Invalid category data', { errors });
          set((state) => ({
            errors: [...state.errors, error],
            lastError: error,
          }));
          return { success: false, error };
        }
        
        try {
          const newCategory: EventCategory = {
            ...category,
            id: crypto.randomUUID(),
          };
          set((state) => ({
            categories: [...state.categories, newCategory],
          }));
          return { success: true };
        } catch (error) {
          const appError = createError('unknown', 'Failed to add category', { error });
          set((state) => ({
            errors: [...state.errors, appError],
            lastError: appError,
          }));
          return { success: false, error: appError };
        }
      },

      updateCategory: (id, updates) => {
        try {
          const category = get().categories.find(c => c.id === id);
          if (!category) {
            const error = createError('not_found', 'Category not found', { categoryId: id });
            set((state) => ({
              errors: [...state.errors, error],
              lastError: error,
            }));
            return { success: false, error };
          }
          
          const updatedCategory = { ...category, ...updates };
          const { isValid, errors } = get().validateCategory(updatedCategory);
          
          if (!isValid) {
            const error = createError('validation', 'Invalid category data', { errors });
            set((state) => ({
              errors: [...state.errors, error],
              lastError: error,
            }));
            return { success: false, error };
          }
          
          set((state) => ({
            categories: state.categories.map((category) =>
              category.id === id ? updatedCategory : category
            ),
          }));
          return { success: true };
        } catch (error) {
          const appError = createError('unknown', 'Failed to update category', { error });
          set((state) => ({
            errors: [...state.errors, appError],
            lastError: appError,
          }));
    }
    
    set((state) => ({
      events: state.events.filter((event) => event.id !== id),
    }));
    return { success: true };
  } catch (error) {
    const appError = createError('unknown', 'Failed to delete event', { error });
    set((state) => ({
      errors: [...state.errors, appError],
      lastError: appError,
    }));
    return { success: false, error: appError };
  }
},

getEventsForDate: (date) => {
  const dateStr = date.toISOString().split('T')[0];
  return get().events.filter((event) => {
    const startStr = new Date(event.start).toISOString().split('T')[0];
    const endStr = new Date(event.end).toISOString().split('T')[0];
    return dateStr >= startStr && dateStr <= endStr;
  });
},

getEventsForDateRange: (startDate, endDate) => {
  const startStr = startDate.toISOString().split('T')[0];
  const endStr = endDate.toISOString().split('T')[0];
  
  return get().events.filter((event) => {
    const eventStartStr = new Date(event.start).toISOString().split('T')[0];
    const eventEndStr = new Date(event.end).toISOString().split('T')[0];
    return (
      (eventStartStr >= startStr && eventStartStr <= endStr) ||
      (eventEndStr >= startStr && eventEndStr <= endStr) ||
      (eventStartStr <= startStr && eventEndStr >= endStr)
    );
  });
},

// UI actions
setCurrentDate: (date) => set({ currentDate: date }),
setSelectedDate: (date) => set({ selectedDate: date }),
setView: (view) => set({ view }),
  
nextPeriod: () => {
  const { currentDate, view } = get();
  const newDate = new Date(currentDate);
  
  switch (view) {
    case 'day':
      newDate.setDate(newDate.getDate() + 1);
      break;
    case 'workweek':
    case 'week':
      newDate.setDate(newDate.getDate() + 7);
      break;
    case 'month':
      newDate.setMonth(newDate.getMonth() + 1);
      break;
    case 'year':
      newDate.setFullYear(newDate.getFullYear() + 1);
      break;
  }
  
  set({ currentDate: newDate });
},
  
prevPeriod: () => {
  const { currentDate, view } = get();
  const newDate = new Date(currentDate);
  
  switch (view) {
    case 'day':
      newDate.setDate(newDate.getDate() - 1);
      break;
    case 'workweek':
    case 'week':
      newDate.setDate(newDate.getDate() - 7);
      break;
    case 'month':
      newDate.setMonth(newDate.getMonth() - 1);
      break;
    case 'year':
      newDate.setFullYear(newDate.getFullYear() - 1);
      break;
  }
  
  set({ currentDate: newDate });
},

// Category actions
addCategory: (category) => {
  const { isValid, errors } = get().validateCategory(category);
  if (!isValid) {
    const error = createError('validation', 'Invalid category data', { errors });
    set((state) => ({
      errors: [...state.errors, error],
      lastError: error,
    }));
    return { success: false, error };
  }
  
  try {
    const newCategory: EventCategory = {
      ...category,
      id: crypto.randomUUID(),
    };
    set((state) => ({
      categories: [...state.categories, newCategory],
    }));
    return { success: true };
  } catch (error) {
    const appError = createError('unknown', 'Failed to add category', { error });
    set((state) => ({
      errors: [...state.errors, appError],
      lastError: appError,
    }));
    return { success: false, error: appError };
  }
},

updateCategory: (id, updates) => {
  try {
    const category = get().categories.find(c => c.id === id);
    if (!category) {
      const error = createError('not_found', 'Category not found', { categoryId: id });
      set((state) => ({
        errors: [...state.errors, error],
        lastError: error,
      }));
      return { success: false, error };
    }
    
    const updatedCategory = { ...category, ...updates };
    const { isValid, errors } = get().validateCategory(updatedCategory);
    
    if (!isValid) {
      const error = createError('validation', 'Invalid category data', { errors });
      set((state) => ({
        errors: [...state.errors, error],
        lastError: error,
      }));
      return { success: false, error };
    }
    
    set((state) => ({
      categories: state.categories.map((category) =>
        category.id === id ? updatedCategory : category
      ),
    }));
    return { success: true };
  } catch (error) {
    const appError = createError('unknown', 'Failed to update category', { error });
    set((state) => ({
      errors: [...state.errors, appError],
      lastError: appError,
    }));
    return { success: false, error: appError };
  }
},

deleteCategory: (id) => {
  try {
    const categoryExists = get().categories.some(cat => cat.id === id);
    if (!categoryExists) {
      const error = createError('not_found', 'Category not found', { categoryId: id });
      set((state) => ({
        errors: [...state.errors, error],
        lastError: error,
      }));
      return { success: false, error };
    }
    
    // Don't allow deletion if there are events using this category
    const eventsUsingCategory = get().events.some(
      (event) => event.categoryId === id
    );
    if (eventsUsingCategory) {
      const error = createError('validation', 'Cannot delete category: there are events using it', { categoryId: id });
      set((state) => ({
        errors: [...state.errors, error],
        lastError: error,
      }));
      return { success: false, error };
    }
    
    set((state) => ({
      categories: state.categories.filter((category) => category.id !== id),
    }));
    return { success: true };
  } catch (error) {
    const appError = createError('unknown', 'Failed to delete category', { error });
    set((state) => ({
      errors: [...state.errors, appError],
      lastError: appError,
    }));
    return { success: false, error: appError };
  }
},
}),
{
  name: 'calendar-storage', // name of the item in the storage (must be unique)
  getStorage: () => localStorage, // (optional) by default, 'localStorage' is used
}
  )
);
