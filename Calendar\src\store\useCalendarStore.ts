import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { CalendarEvent, EventCategory } from '../types/calendar';

interface CalendarState {
  // Events state
  events: CalendarEvent[];
  addEvent: (event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateEvent: (id: string, updates: Partial<CalendarEvent>) => void;
  deleteEvent: (id: string) => void;
  getEventsForDate: (date: Date) => CalendarEvent[];
  getEventsForDateRange: (startDate: Date, endDate: Date) => CalendarEvent[];
  
  // UI state
  currentDate: Date;
  selectedDate: Date | null;
  view: 'day' | 'workweek' | 'week' | 'month' | 'year';
  setCurrentDate: (date: Date) => void;
  setSelectedDate: (date: Date | null) => void;
  setView: (view: 'day' | 'workweek' | 'week' | 'month' | 'year') => void;
  nextPeriod: () => void;
  prevPeriod: () => void;
  
  // Categories
  categories: EventCategory[];
  addCategory: (category: Omit<EventCategory, 'id'>) => void;
  updateCategory: (id: string, updates: Partial<EventCategory>) => void;
  deleteCategory: (id: string) => void;
}

export const useCalendarStore = create<CalendarState>()(
  persist(
    (set, get) => ({
      // Initial state
      events: [],
      currentDate: new Date(),
      selectedDate: null,
      view: 'month',
      categories: [
        { id: 'work', name: 'Work', color: '#3B82F6' },
        { id: 'personal', name: 'Personal', color: '#10B981' },
        { id: 'meeting', name: 'Meeting', color: '#F59E0B' },
        { id: 'reminder', name: 'Reminder', color: '#EF4444' },
        { id: 'event', name: 'Event', color: '#8B5CF6' },
      ],

      // Event actions
      addEvent: (eventData) => {
        const newEvent: CalendarEvent = {
          ...eventData,
          id: crypto.randomUUID(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        set((state) => ({
          events: [...state.events, newEvent],
        }));
        return newEvent;
      },

      updateEvent: (id, updates) => {
        set((state) => ({
          events: state.events.map((event) =>
            event.id === id
              ? { ...event, ...updates, updatedAt: new Date() }
              : event
          ),
        }));
      },

      deleteEvent: (id) => {
        set((state) => ({
          events: state.events.filter((event) => event.id !== id),
        }));
      },

      getEventsForDate: (date) => {
        const dateStr = date.toISOString().split('T')[0];
        return get().events.filter((event) => {
          const startStr = new Date(event.start).toISOString().split('T')[0];
          const endStr = new Date(event.end).toISOString().split('T')[0];
          return dateStr >= startStr && dateStr <= endStr;
        });
      },

      getEventsForDateRange: (startDate, endDate) => {
        const startStr = startDate.toISOString().split('T')[0];
        const endStr = endDate.toISOString().split('T')[0];
        
        return get().events.filter((event) => {
          const eventStartStr = new Date(event.start).toISOString().split('T')[0];
          const eventEndStr = new Date(event.end).toISOString().split('T')[0];
          return (
            (eventStartStr >= startStr && eventStartStr <= endStr) ||
            (eventEndStr >= startStr && eventEndStr <= endStr) ||
            (eventStartStr <= startStr && eventEndStr >= endStr)
          );
        });
      },

      // UI actions
      setCurrentDate: (date) => set({ currentDate: date }),
      setSelectedDate: (date) => set({ selectedDate: date }),
      setView: (view) => set({ view }),
      
      nextPeriod: () => {
        const { currentDate, view } = get();
        const newDate = new Date(currentDate);
        
        switch (view) {
          case 'day':
            newDate.setDate(newDate.getDate() + 1);
            break;
          case 'workweek':
          case 'week':
            newDate.setDate(newDate.getDate() + 7);
            break;
          case 'month':
            newDate.setMonth(newDate.getMonth() + 1);
            break;
          case 'year':
            newDate.setFullYear(newDate.getFullYear() + 1);
            break;
        }
        
        set({ currentDate: newDate });
      },
      
      prevPeriod: () => {
        const { currentDate, view } = get();
        const newDate = new Date(currentDate);
        
        switch (view) {
          case 'day':
            newDate.setDate(newDate.getDate() - 1);
            break;
          case 'workweek':
          case 'week':
            newDate.setDate(newDate.getDate() - 7);
            break;
          case 'month':
            newDate.setMonth(newDate.getMonth() - 1);
            break;
          case 'year':
            newDate.setFullYear(newDate.getFullYear() - 1);
            break;
        }
        
        set({ currentDate: newDate });
      },

      // Category actions
      addCategory: (category) => {
        const newCategory: EventCategory = {
          ...category,
          id: crypto.randomUUID(),
        };
        set((state) => ({
          categories: [...state.categories, newCategory],
        }));
      },

      updateCategory: (id, updates) => {
        set((state) => ({
          categories: state.categories.map((category) =>
            category.id === id ? { ...category, ...updates } : category
          ),
        }));
      },

      deleteCategory: (id) => {
        // Don't delete if there are events using this category
        const eventsUsingCategory = get().events.some(
          (event) => event.category === id
        );
        
        if (eventsUsingCategory) {
          console.warn('Cannot delete category: it is being used by events');
          return;
        }
        
        set((state) => ({
          categories: state.categories.filter((category) => category.id !== id),
        }));
      },
    }),
    {
      name: 'calendar-storage', // name of the item in the storage (must be unique)
      getStorage: () => localStorage, // (optional) by default, 'localStorage' is used
    }
  )
);
