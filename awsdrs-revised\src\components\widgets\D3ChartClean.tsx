import React, { useEffect, useRef, useState } from 'react';
import { Card, Group, Text, Switch, Button } from '@mantine/core';
import { IconZoomReset } from '@tabler/icons-react';
import * as d3 from 'd3';

interface DataPoint {
  time: string;
  value: number;
  cpu?: number;
  memory?: number;
}

interface D3ChartCleanProps {
  title: string;
  data: DataPoint[];
  height?: number;
  onRefresh?: () => void;
  onExport?: () => void;
  minimal?: boolean;
  withBorder?: boolean;
  withShadow?: boolean;
}

const D3ChartClean: React.FC<D3ChartCleanProps> = ({
  title,
  data,
  height = 400,
  minimal = false,
  withBorder = true,
  withShadow = true,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [enableCrosshair, setEnableCrosshair] = useState(true);
  const [enableZoom, setEnableZoom] = useState(false);
  const [resetTrigger, setResetTrigger] = useState(0);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    // Clear previous chart
    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 80, bottom: 40, left: 60 };
    const width = (containerRef.current?.clientWidth || 800) - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom - 100;

    // Process data with index for positioning
    const processedData = data.map((d, i) => ({
      ...d,
      index: i,
      timeLabel: d.time,
    }));

    // Set up scales
    const xScale = d3.scaleLinear()
      .domain([0, processedData.length - 1])
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(processedData, d => d.value) || 100])
      .range([chartHeight, 0]);

    // Create main group
    const g = svg.append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Add axes
    const xAxis = g.append('g')
      .attr('transform', `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale)
        .tickFormat((d) => processedData[d as number]?.timeLabel || '')
        .ticks(Math.min(10, processedData.length))
      );

    g.append('g')
      .call(d3.axisLeft(yScale));

    // Add grid lines
    g.append('g')
      .attr('class', 'grid')
      .attr('transform', `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale)
        .tickSize(-chartHeight)
        .tickFormat(() => '')
        .ticks(Math.min(8, processedData.length))
      )
      .style('stroke-dasharray', '2,2')
      .style('opacity', 0.3);

    g.append('g')
      .attr('class', 'grid')
      .call(d3.axisLeft(yScale)
        .tickSize(-width)
        .tickFormat(() => '')
      )
      .style('stroke-dasharray', '2,2')
      .style('opacity', 0.3);

    // Create line generator
    const line = d3.line<any>()
      .x(d => xScale(d.index))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Add the main line
    const mainPath = g.append('path')
      .datum(processedData)
      .attr('class', 'main-line')
      .attr('fill', 'none')
      .attr('stroke', '#3b82f6')
      .attr('stroke-width', 2)
      .attr('d', line);

    // Add area fill
    const area = d3.area<any>()
      .x(d => xScale(d.index))
      .y0(chartHeight)
      .y1(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    const areaPath = g.append('path')
      .datum(processedData)
      .attr('class', 'area-fill')
      .attr('fill', 'url(#gradient)')
      .attr('d', area);

    // Add gradient definition
    const gradient = svg.append('defs')
      .append('linearGradient')
      .attr('id', 'gradient')
      .attr('gradientUnits', 'userSpaceOnUse')
      .attr('x1', 0).attr('y1', chartHeight)
      .attr('x2', 0).attr('y2', 0);

    gradient.append('stop')
      .attr('offset', '0%')
      .attr('stop-color', '#3b82f6')
      .attr('stop-opacity', 0.1);

    gradient.append('stop')
      .attr('offset', '100%')
      .attr('stop-color', '#3b82f6')
      .attr('stop-opacity', 0.3);

    // Add dots
    g.selectAll('.dot')
      .data(processedData)
      .enter().append('circle')
      .attr('class', 'dot')
      .attr('cx', d => xScale(d.index))
      .attr('cy', d => yScale(d.value))
      .attr('r', 4)
      .attr('fill', '#3b82f6')
      .attr('stroke', '#ffffff')
      .attr('stroke-width', 2);

    // Create tooltip
    const tooltip = d3.select('body').append('div')
      .attr('class', 'd3-clean-tooltip')
      .style('position', 'absolute')
      .style('visibility', 'hidden')
      .style('background', 'rgba(0, 0, 0, 0.8)')
      .style('color', 'white')
      .style('padding', '8px')
      .style('border-radius', '4px')
      .style('font-size', '12px')
      .style('pointer-events', 'none')
      .style('z-index', 1000);

    // Create crosshair elements (always create them)
    const crosshair = g.append('g')
      .attr('class', 'crosshair')
      .style('display', 'none');

    const verticalLine = crosshair.append('line')
      .attr('class', 'crosshair-vertical')
      .attr('y1', 0)
      .attr('y2', chartHeight)
      .style('stroke', '#ff0000')
      .style('stroke-width', 2)
      .style('stroke-dasharray', '5,5')
      .style('pointer-events', 'none')
      .style('opacity', 0.8);

    const horizontalLine = crosshair.append('line')
      .attr('class', 'crosshair-horizontal')
      .attr('x1', 0)
      .attr('x2', width)
      .style('stroke', '#ff0000')
      .style('stroke-width', 2)
      .style('stroke-dasharray', '5,5')
      .style('pointer-events', 'none')
      .style('opacity', 0.8);

    // Create invisible overlay for mouse events
    const overlay = g.append('rect')
      .attr('class', 'overlay')
      .attr('width', width)
      .attr('height', chartHeight)
      .style('fill', 'none')
      .style('pointer-events', 'all')
      .style('cursor', 'crosshair');

    // Mouse move handler - works for crosshair when zoom is disabled
    overlay.on('mousemove', function(event) {
      // Only show crosshair if zoom is disabled OR if zoom is enabled but we're not actively brushing
      if (!enableZoom) {
        const [mouseX, mouseY] = d3.pointer(event);

        // Find closest data point
        const x0 = xScale.invert(mouseX);
        const i = Math.round(Math.max(0, Math.min(x0, processedData.length - 1)));
        const d = processedData[i];

        if (d && enableCrosshair) {
          const x = xScale(d.index);
          const y = yScale(d.value);

          // Show crosshair
          crosshair.style('display', null);

          // Update crosshair position
          verticalLine
            .attr('x1', x)
            .attr('x2', x);

          horizontalLine
            .attr('y1', y)
            .attr('y2', y);

          // Show tooltip
          tooltip
            .style('visibility', 'visible')
            .html(`
              <div><strong>Time:</strong> ${d.timeLabel}</div>
              <div><strong>Value:</strong> ${d.value.toFixed(2)}</div>
            `)
            .style('left', (event.pageX + 10) + 'px')
            .style('top', (event.pageY - 10) + 'px');
        } else {
          crosshair.style('display', 'none');
          tooltip.style('visibility', 'hidden');
        }
      }
    });

    // Mouse leave handler
    overlay.on('mouseleave', function() {
      crosshair.style('display', 'none');
      tooltip.style('visibility', 'hidden');
    });

    // Add zoom functionality if enabled
    if (enableZoom) {
      const brush = d3.brushX()
        .extent([[0, 0], [width, chartHeight]])
        .on('start', () => {
          // Hide crosshair during brush selection
          crosshair.style('display', 'none');
          tooltip.style('visibility', 'hidden');
        })
        .on('end', (event) => {
          if (!event.selection) return;

          const [x0, x1] = event.selection;
          const startIndex = Math.round(xScale.invert(x0));
          const endIndex = Math.round(xScale.invert(x1));

          // Get zoomed data
          const zoomedData = processedData.slice(
            Math.max(0, startIndex),
            Math.min(processedData.length, endIndex + 1)
          );

          if (zoomedData.length < 2) return;

          // Create new scale for zoomed data
          const newXScale = d3.scaleLinear()
            .domain([0, zoomedData.length - 1])
            .range([0, width]);

          // Update axes
          xAxis.transition()
            .duration(750)
            .call(d3.axisBottom(newXScale)
              .tickFormat((d) => zoomedData[Math.round(d as number)]?.timeLabel || '')
              .ticks(Math.min(10, zoomedData.length))
            );

          // Create new line and area generators
          const newLine = d3.line<any>()
            .x((d, i) => newXScale(i))
            .y(d => yScale(d.value))
            .curve(d3.curveMonotoneX);

          const newArea = d3.area<any>()
            .x((d, i) => newXScale(i))
            .y0(chartHeight)
            .y1(d => yScale(d.value))
            .curve(d3.curveMonotoneX);

          // Update paths
          mainPath.datum(zoomedData)
            .transition()
            .duration(750)
            .attr('d', newLine);

          areaPath.datum(zoomedData)
            .transition()
            .duration(750)
            .attr('d', newArea);

          // Update dots
          const dots = g.selectAll('.dot')
            .data(zoomedData);

          // Remove old dots
          dots.exit().remove();

          // Update existing dots
          dots.transition()
            .duration(750)
            .attr('cx', (d, i) => newXScale(i))
            .attr('cy', d => yScale(d.value));

          // Add new dots
          dots.enter()
            .append('circle')
            .attr('class', 'dot')
            .attr('r', 4)
            .attr('fill', '#3b82f6')
            .attr('stroke', '#ffffff')
            .attr('stroke-width', 2)
            .attr('cx', (d, i) => newXScale(i))
            .attr('cy', d => yScale(d.value));

          // Clear brush selection
          g.select('.brush').call(brush.move, null);
        });

      const brushGroup = g.append('g')
        .attr('class', 'brush')
        .call(brush);

      // Modify brush overlay to allow crosshair events to pass through
      brushGroup.select('.overlay')
        .on('mousemove.crosshair', function(event) {
          if (enableCrosshair) {
            const [mouseX, mouseY] = d3.pointer(event);

            // Find closest data point
            const x0 = xScale.invert(mouseX);
            const i = Math.round(Math.max(0, Math.min(x0, processedData.length - 1)));
            const d = processedData[i];

            if (d) {
              const x = xScale(d.index);
              const y = yScale(d.value);

              // Show crosshair
              crosshair.style('display', null);

              // Update crosshair position
              verticalLine
                .attr('x1', x)
                .attr('x2', x);

              horizontalLine
                .attr('y1', y)
                .attr('y2', y);

              // Show tooltip
              tooltip
                .style('visibility', 'visible')
                .html(`
                  <div><strong>Time:</strong> ${d.timeLabel}</div>
                  <div><strong>Value:</strong> ${d.value.toFixed(2)}</div>
                `)
                .style('left', (event.pageX + 10) + 'px')
                .style('top', (event.pageY - 10) + 'px');
            }
          }
        })
        .on('mouseleave.crosshair', function() {
          crosshair.style('display', 'none');
          tooltip.style('visibility', 'hidden');
        });
    }

    // Cleanup function
    return () => {
      tooltip.remove();
    };

  }, [data, height, enableCrosshair, enableZoom, resetTrigger]);

  const resetZoom = () => {
    setResetTrigger(prev => prev + 1);
  };

  const content = (
    <div ref={containerRef} style={{ height: '100%' }}>
      <Group justify="space-between" mb="md">
        <Text size="lg" fw={600}>
          {title}
        </Text>
        <Group gap="md">
          <Switch
            label="Crosshair"
            checked={enableCrosshair}
            onChange={(event) => setEnableCrosshair(event.currentTarget.checked)}
            size="sm"
            color="red"
          />
          <Switch
            label="Zoom"
            checked={enableZoom}
            onChange={(event) => setEnableZoom(event.currentTarget.checked)}
            size="sm"
            color="blue"
          />
          {enableZoom && (
            <Button
              size="xs"
              variant="light"
              leftSection={<IconZoomReset size={14} />}
              onClick={resetZoom}
            >
              Reset
            </Button>
          )}
        </Group>
      </Group>

      <svg
        ref={svgRef}
        width="100%"
        height={height - 100}
        style={{ overflow: 'visible' }}
      />
      
      <Text size="xs" c="dimmed" mt="xs">
        <strong>Clean D3 Chart:</strong> ✅ Crosshair + ✅ Zoom work together! Enable both toggles and hover while zoom is active.
      </Text>
    </div>
  );

  if (minimal) {
    return content;
  }

  return (
    <Card shadow={withShadow ? "sm" : "none"} padding="lg" radius="md" withBorder={withBorder}>
      {content}
    </Card>
  );
};

export default D3ChartClean;
