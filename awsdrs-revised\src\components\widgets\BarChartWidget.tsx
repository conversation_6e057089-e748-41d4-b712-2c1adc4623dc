import React, { useState, useRef } from 'react';
import { Card, Text, Group, ActionIcon, Tooltip } from '@mantine/core';
import { IconRefresh, IconDownload } from '@tabler/icons-react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import zoomPlugin from 'chartjs-plugin-zoom';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  zoomPlugin
);

interface BarChartWidgetProps {
  title: string;
  data: any[];
  bars: {
    dataKey: string;
    fill: string;
    name: string;
  }[];
  height?: number;
  onRefresh?: () => void;
  onExport?: () => void;
  withBorder?: boolean;
  withShadow?: boolean;
  minimal?: boolean;
}

const BarChartWidget = ({
  title,
  data,
  bars,
  height = 300,
  onRefresh,
  onExport,
  withBorder = true,
  withShadow = true,
  minimal = false
}: BarChartWidgetProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const chartRef = useRef<ChartJS<'bar'>>(null);

  // Consistent tooltip styling to match header row tooltips
  const tooltipStyles = {
    tooltip: {
      fontSize: '11px',
      padding: '4px 8px',
      fontWeight: 400
    }
  };

  // Prepare data for Chart.js
  const chartData = {
    labels: data.map(d => d.name || d.time || d.label),
    datasets: bars.map((bar, index) => ({
      label: bar.name,
      data: data.map(d => d[bar.dataKey]),
      backgroundColor: bar.fill,
      borderColor: bar.fill,
      borderWidth: 1,
      borderRadius: 4,
      borderSkipped: false,
    })),
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
      },
      zoom: {
        zoom: {
          wheel: {
            enabled: true,
          },
          pinch: {
            enabled: true,
          },
          mode: 'x' as const,
        },
        pan: {
          enabled: true,
          mode: 'x' as const,
        },
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      y: {
        display: true,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)',
        },
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false,
    },
  };

  if (minimal) {
    return (
      <div style={{ height: '100%', padding: '16px' }}>
        <Group justify="space-between" mb="md">
          <Text size="lg" fw={600}>
            {title}
          </Text>
          <Group gap="xs">
            {onRefresh && (
              <Tooltip label="Refresh chart data" styles={tooltipStyles}>
                <ActionIcon variant="light" size="sm" onClick={onRefresh}>
                  <IconRefresh size={16} />
                </ActionIcon>
              </Tooltip>
            )}
            {onExport && (
              <Tooltip label="Export chart data" styles={tooltipStyles}>
                <ActionIcon variant="light" size="sm" onClick={onExport}>
                  <IconDownload size={16} />
                </ActionIcon>
              </Tooltip>
            )}
          </Group>
        </Group>

        <div style={{ height: `${height}px`, width: '100%' }}>
          <Bar ref={chartRef} data={chartData} options={options} />
        </div>
      </div>
    );
  }

  return (
    <Card shadow={withShadow ? "sm" : "none"} padding="lg" radius="md" withBorder={withBorder}>
      <Group justify="space-between" mb="md">
        <Text size="lg" fw={600}>
          {title}
        </Text>
        <Group gap="xs">
          {onRefresh && (
            <Tooltip label="Refresh chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onRefresh}>
                <IconRefresh size={16} />
              </ActionIcon>
            </Tooltip>
          )}
          {onExport && (
            <Tooltip label="Export chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onExport}>
                <IconDownload size={16} />
              </ActionIcon>
            </Tooltip>
          )}
        </Group>
      </Group>

      <div style={{ height: `${height}px`, width: '100%' }}>
        <Bar ref={chartRef} data={chartData} options={options} />
      </div>
    </Card>
  );
};

export default BarChartWidget;
