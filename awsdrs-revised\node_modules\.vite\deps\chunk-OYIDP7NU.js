import {
  createReactComponent
} from "./chunk-5HMDTYKJ.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconNavigationShare.mjs
var IconNavigationShare = createReactComponent("outline", "navigation-share", "IconNavigationShare", [["path", { "d": "M16.633 13.043l-4.633 -10.043l-7.97 17.275c-.07 .2 -.017 .424 .135 .572c.15 .148 .374 .193 .57 .116l7.265 -2.463l.955 .324", "key": "svg-0" }], ["path", { "d": "M16 22l5 -5", "key": "svg-1" }], ["path", { "d": "M21 21.5v-4.5h-4.5", "key": "svg-2" }]]);

export {
  IconNavigationShare
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconNavigationShare.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-OYIDP7NU.js.map
