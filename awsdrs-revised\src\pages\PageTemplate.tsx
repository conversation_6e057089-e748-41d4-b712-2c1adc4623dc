import { useState, useEffect, useCallback, useRef } from "react";
import { Text, Card, Group, ActionIcon, Stack, Button } from "@mantine/core";
import { IconEye, IconEyeOff, IconRestore, IconServer, IconDatabase, IconActivity, IconCpu } from "@tabler/icons-react";
import DashboardLayout from "../layouts/DashboardLayout";
import { MetricCard, LineChartWidget, BarChartWidget, PieChartWidget } from "../components/widgets";
import WidgetMenu from "../components/WidgetMenu";
import { useLayoutManager } from "../hooks/useLayoutManager";
import dataService from "../services/dataService";
import { Responsive, WidthProvider } from 'react-grid-layout';
import type { Layout } from 'react-grid-layout';

const ResponsiveGridLayout = WidthProvider(Responsive);

// Define Template widgets (copy and modify for each page)
interface TemplateWidget {
  id: string;
  title: string;
  type: 'metric' | 'chart' | 'custom';
}

// Default widgets configuration (customize for each page)
const defaultTemplateWidgets: TemplateWidget[] = [
  { id: 'sample-metric-1', title: 'Sample Metric 1', type: 'metric' },
  { id: 'sample-metric-2', title: 'Sample Metric 2', type: 'metric' },
  { id: 'sample-chart-1', title: 'Sample Chart 1', type: 'chart' },
  { id: 'sample-chart-2', title: 'Sample Chart 2', type: 'chart' },
];

// Default layouts configuration (customize for each page)
const defaultTemplateLayouts = {
  lg: [
    { i: 'sample-metric-1', x: 0, y: 0, w: 3, h: 2 },
    { i: 'sample-metric-2', x: 3, y: 0, w: 3, h: 2 },
    { i: 'sample-chart-1', x: 6, y: 0, w: 6, h: 4 },
    { i: 'sample-chart-2', x: 0, y: 2, w: 6, h: 4 },
  ],
  md: [
    { i: 'sample-metric-1', x: 0, y: 0, w: 3, h: 2 },
    { i: 'sample-metric-2', x: 3, y: 0, w: 3, h: 2 },
    { i: 'sample-chart-1', x: 0, y: 2, w: 6, h: 4 },
    { i: 'sample-chart-2', x: 0, y: 6, w: 6, h: 4 },
  ],
  sm: [
    { i: 'sample-metric-1', x: 0, y: 0, w: 2, h: 2 },
    { i: 'sample-metric-2', x: 2, y: 0, w: 2, h: 2 },
    { i: 'sample-chart-1', x: 0, y: 2, w: 4, h: 4 },
    { i: 'sample-chart-2', x: 0, y: 6, w: 4, h: 4 },
  ],
  xs: [
    { i: 'sample-metric-1', x: 0, y: 0, w: 2, h: 2 },
    { i: 'sample-metric-2', x: 0, y: 2, w: 2, h: 2 },
    { i: 'sample-chart-1', x: 0, y: 4, w: 2, h: 4 },
    { i: 'sample-chart-2', x: 0, y: 8, w: 2, h: 4 },
  ]
};

// Helper function for initial menu position
const getInitialMenuPosition = () => ({
  x: window.innerWidth - 320,
  y: 100
});

// Widget factory function (customize for each page)
const renderTemplateWidget = (widget: TemplateWidget) => {
  // Get data from services (customize for each page)
  const sampleData = dataService.getSystemResourceData();
  
  switch (widget.id) {
    case 'sample-metric-1':
      return (
        <Card h="100%">
          <MetricCard
            title="Sample Metric 1"
            value="100%"
            change={5}
            changeType="increase"
            icon={<IconServer size={20} />}
            color="blue"
            subtitle="Sample description"
          />
        </Card>
      );
    case 'sample-metric-2':
      return (
        <Card h="100%">
          <MetricCard
            title="Sample Metric 2"
            value="85%"
            change={-2}
            changeType="decrease"
            icon={<IconDatabase size={20} />}
            color="green"
            subtitle="Sample description"
          />
        </Card>
      );
    case 'sample-chart-1':
      return (
        <LineChartWidget
          title="Sample Line Chart"
          data={sampleData}
          lines={[
            { dataKey: 'cpu', stroke: '#3b82f6', name: 'Sample Data' }
          ]}
          height={280}
          onRefresh={() => console.log('Refreshing sample chart...')}
          onExport={() => console.log('Exporting sample chart...')}
        />
      );
    case 'sample-chart-2':
      return (
        <BarChartWidget
          title="Sample Bar Chart"
          data={sampleData}
          bars={[
            { dataKey: 'memory', fill: '#10b981', name: 'Sample Data' }
          ]}
          height={280}
          onRefresh={() => console.log('Refreshing sample bar chart...')}
          onExport={() => console.log('Exporting sample bar chart...')}
        />
      );
    default:
      return (
        <Card h="100%" p="md">
          <Text>Unknown widget: {widget.id}</Text>
        </Card>
      );
  }
};

// Main Template Component (rename and customize for each page)
const PageTemplate = () => {
  // Layout management with multiple presets
  const {
    layouts,
    layoutPresets,
    currentLayoutId,
    switchToLayout,
    saveCurrentLayout,
    deleteLayout,
    resetToDefault,
    handleLayoutChange
  } = useLayoutManager({
    pageKey: 'template', // Change this for each page (e.g., 'dashboard', 'metrics', 'analytics')
    defaultLayouts: defaultTemplateLayouts,
    defaultLayoutName: 'Default Template Layout'
  });

  // State management (standard for all pages)
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [widgets, setWidgets] = useState(defaultTemplateWidgets);
  const [visibleWidgets, setVisibleWidgets] = useState(new Set(defaultTemplateWidgets.map(w => w.id)));
  const [showWidgetMenu, setShowWidgetMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState(getInitialMenuPosition());
  const [isDragging, setIsDragging] = useState(false);

  // Auto-refresh data (customize interval and data sources for each page)
  useEffect(() => {
    const interval = setInterval(() => {
      // Add data refresh logic here
      console.log('Auto-refreshing template data...');
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Load saved widget visibility
  useEffect(() => {
    try {
      const savedVisibility = localStorage.getItem('template-visible-widgets');
      if (savedVisibility) {
        setVisibleWidgets(new Set(JSON.parse(savedVisibility)));
      }
    } catch (error) {
      console.error('Error loading widget visibility:', error);
    }
  }, []);

  // Widget visibility toggle (standard for all pages)
  const toggleWidgetVisibility = (widgetId: string) => {
    setVisibleWidgets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(widgetId)) {
        newSet.delete(widgetId);
      } else {
        newSet.add(widgetId);
      }
      // Save to localStorage (customize key for each page)
      localStorage.setItem('template-visible-widgets', JSON.stringify([...newSet]));
      return newSet;
    });
  };

  const closeWidgetMenu = () => {
    setShowWidgetMenu(false);
  };

  const resetToDefaultAndClose = () => {
    resetToDefault();
    setShowWidgetMenu(false);
    setMenuPosition(getInitialMenuPosition());
  };

  // Optimized drag handlers for floating menu using direct DOM manipulation
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target !== e.currentTarget && !(e.target as Element).closest('.widget-menu-header')) return;

    setIsDragging(true);
    const startX = e.clientX - menuPosition.x;
    const startY = e.clientY - menuPosition.y;

    const handleMouseMove = (e: MouseEvent) => {
      const newX = Math.max(0, Math.min(window.innerWidth - 300, e.clientX - startX));
      const newY = Math.max(0, Math.min(window.innerHeight - 200, e.clientY - startY));

      if (menuRef.current) {
        menuRef.current.style.left = `${newX}px`;
        menuRef.current.style.top = `${newY}px`;
      }

      setMenuPosition({ x: newX, y: newY });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle refresh (customize for each page)
  const handleRefresh = useCallback(() => {
    setLoading(true);
    // Add refresh logic here
    setTimeout(() => setLoading(false), 1000);
  }, []);

  // Filter visible widgets
  const allWidgets = widgets.filter(widget => visibleWidgets.has(widget.id));

  // Dashboard controls (standard for all pages)
  const dashboardControls = {
    isEditing,
    loading,
    showWidgetMenu,
    onToggleEdit: () => setIsEditing(!isEditing),
    onRefresh: handleRefresh,
    onResetLayout: resetToDefaultAndClose,
    onToggleWidgetMenu: () => {
      if (!showWidgetMenu) {
        setMenuPosition(getInitialMenuPosition());
      }
      setShowWidgetMenu(!showWidgetMenu);
    }
  };

  return (
    <DashboardLayout dashboardControls={dashboardControls}>
      <div style={{ width: '100%', position: 'relative', maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ flex: 1, overflow: 'auto', paddingTop: '40px', padding: '40px 24px 24px 24px' }}>
          
          {/* Draggable Grid Layout */}
          <ResponsiveGridLayout
            className="layout"
            layouts={layouts}
            onLayoutChange={(layout, layouts) => {
              if (isEditing) {
                handleLayoutChange(layout, layouts);
              }
            }}
            isDraggable={isEditing}
            isResizable={isEditing}
            breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480 }}
            cols={{ lg: 12, md: 6, sm: 4, xs: 2 }}
            rowHeight={60}
            margin={[16, 16]}
            containerPadding={[0, 0]}
            useCSSTransforms={true}
            preventCollision={false}
            compactType="vertical"
          >
            {allWidgets.map((widget) => {
              return (
                <div key={widget.id} style={{ position: 'relative' }}>
                  {renderTemplateWidget(widget)}
                </div>
              );
            })}
          </ResponsiveGridLayout>
        </div>

        {/* Floating Widget Selection Menu */}
        {showWidgetMenu && (
          <WidgetMenu
            widgets={widgets}
            visibleWidgets={visibleWidgets}
            onToggleWidgetVisibility={toggleWidgetVisibility}
            layoutPresets={layoutPresets}
            currentLayoutId={currentLayoutId}
            onLayoutChange={switchToLayout}
            onSaveLayout={saveCurrentLayout}
            onDeleteLayout={deleteLayout}
            onResetToDefault={resetToDefaultAndClose}
            menuPosition={menuPosition}
            isDragging={isDragging}
            onMouseDown={handleMouseDown}
            onClose={closeWidgetMenu}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default PageTemplate;
