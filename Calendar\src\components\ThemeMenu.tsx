import { <PERSON>u, Button, Group, Text, ColorPicker, Paper, Switch, Slider, TextInput } from '@mantine/core'
import { Stack, Divider, ActionIcon, Box } from '@mantine/core'
import { Cog6ToothIcon } from '@heroicons/react/24/outline'
import type { ThemeColors } from '../hooks/useTheme'

interface ThemeMenuProps {
  colors: ThemeColors
  selectedElement: keyof ThemeColors
  onElementSelect: (element: keyof ThemeColors) => void
  onColorChange: (color: string) => void
  onReset: () => void
}

const elementLabels: Record<keyof ThemeColors, string> = {
  primary: 'Header Row',
  header: 'Calendar Row',
  text: 'Text Color',
  border: 'Border Color',
  background: 'Calendar Background'
}

export function ThemeMenu({
  colors,
  selectedElement,
  onElementSelect,
  onReset,
}: ThemeMenuProps) {
  return (
    <Menu shadow="md" width={320} position="bottom-end" withinPortal>
      <Menu.Target>
        <ActionIcon
          variant="subtle"
          color="white"
          size="sm"
          styles={{
            root: {
              backgroundColor: 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
              }
            }
          }}
        >
          <Cog6ToothIcon className="h-4 w-4" />
        </ActionIcon>
      </Menu.Target>

      <Menu.Dropdown>
        <Paper p="md" maw={320} mah={500} style={{ overflowY: 'auto' }}>
          <Stack gap="md">
            <Text fw={600} size="lg">Theme Settings</Text>

            <Stack gap="sm">
              <Text size="sm" fw={500}>Color Elements</Text>
              <Stack gap="xs">
                {(Object.keys(elementLabels) as Array<keyof ThemeColors>).map((element) => (
                  <Button
                    key={element}
                    variant={selectedElement === element ? 'filled' : 'light'}
                    size="xs"
                    fullWidth
                    leftSection={
                      <Box
                        w={12}
                        h={12}
                        style={{
                          backgroundColor: colors[element],
                          borderRadius: 2,
                          border: '1px solid #e5e7eb'
                        }}
                      />
                    }
                    onClick={() => onElementSelect(element)}
                    color={selectedElement === element ? 'blue' : 'gray'}
                  >
                    {elementLabels[element]}
                  </Button>
                ))}
              </Stack>
            </Stack>

            <Group justify="space-between">
              <Text size="sm" c="dimmed">
                Editing: {elementLabels[selectedElement]}
              </Text>
              <Button
                onClick={onReset}
                variant="light"
                size="sm"
                color="gray"
              >
                Reset All
              </Button>
            </Group>
          </Stack>
        </Paper>
      </Menu.Dropdown>
    </Menu>
  )
}