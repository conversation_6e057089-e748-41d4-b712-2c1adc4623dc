import {
  createReactComponent
} from "./chunk-5HMDTYKJ.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconMathCtg.mjs
var IconMathCtg = createReactComponent("outline", "math-ctg", "IconMathCtg", [["path", { "d": "M10 8h4", "key": "svg-0" }], ["path", { "d": "M21 8h-2a2 2 0 0 0 -2 2v4a2 2 0 0 0 2 2h2v-4h-1", "key": "svg-1" }], ["path", { "d": "M12 8v8", "key": "svg-2" }], ["path", { "d": "M7 10a2 2 0 1 0 -4 0v4a2 2 0 1 0 4 0", "key": "svg-3" }]]);

export {
  IconMathCtg
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconMathCtg.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-OZFROHIR.js.map
