import { useState, useCallback, useEffect } from 'react'
import { CalendarEvent, EventCategory } from '../types/calendar'
import { format, isWithinInterval, startOfDay, endOfDay } from 'date-fns'

// Default event categories
const defaultCategories: EventCategory[] = [
  { id: 'work', name: 'Work', color: '#3B82F6' },
  { id: 'personal', name: 'Personal', color: '#10B981' },
  { id: 'meeting', name: 'Meeting', color: '#F59E0B' },
  { id: 'reminder', name: 'Reminder', color: '#EF4444' },
  { id: 'event', name: 'Event', color: '#8B5CF6' },
]

export function useEvents() {
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [categories, setCategories] = useState<EventCategory[]>(defaultCategories)
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null)

  // Load events from localStorage on mount
  useEffect(() => {
    const savedEvents = localStorage.getItem('calendar-events')
    if (savedEvents) {
      try {
        const parsed = JSON.parse(savedEvents)
        const eventsWithDates = parsed.map((event: any) => ({
          ...event,
          start: new Date(event.start),
          end: new Date(event.end),
          createdAt: new Date(event.createdAt),
          updatedAt: new Date(event.updatedAt),
        }))
        setEvents(eventsWithDates)
      } catch (error) {
        console.error('Error loading events:', error)
      }
    }
  }, [])

  // Save events to localStorage whenever events change
  useEffect(() => {
    localStorage.setItem('calendar-events', JSON.stringify(events))
  }, [events])

  const createEvent = useCallback((eventData: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newEvent: CalendarEvent = {
      ...eventData,
      id: crypto.randomUUID(),
      createdAt: new Date(),
      updatedAt: new Date(),
    }
    setEvents(prev => [...prev, newEvent])
    return newEvent
  }, [])

  const updateEvent = useCallback((eventId: string, updates: Partial<CalendarEvent>) => {
    setEvents(prev => prev.map(event => 
      event.id === eventId 
        ? { ...event, ...updates, updatedAt: new Date() }
        : event
    ))
  }, [])

  const deleteEvent = useCallback((eventId: string) => {
    setEvents(prev => prev.filter(event => event.id !== eventId))
    if (selectedEvent?.id === eventId) {
      setSelectedEvent(null)
    }
  }, [selectedEvent])

  const getEventsForDate = useCallback((date: Date) => {
    const dayStart = startOfDay(date)
    const dayEnd = endOfDay(date)
    
    return events.filter(event => {
      if (event.allDay) {
        return format(event.start, 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
      }
      return isWithinInterval(event.start, { start: dayStart, end: dayEnd }) ||
             isWithinInterval(event.end, { start: dayStart, end: dayEnd }) ||
             (event.start < dayStart && event.end > dayEnd)
    })
  }, [events])

  const getEventsForDateRange = useCallback((startDate: Date, endDate: Date) => {
    return events.filter(event => {
      return isWithinInterval(event.start, { start: startDate, end: endDate }) ||
             isWithinInterval(event.end, { start: startDate, end: endDate }) ||
             (event.start < startDate && event.end > endDate)
    })
  }, [events])

  const getCategoryById = useCallback((categoryId: string) => {
    return categories.find(cat => cat.id === categoryId)
  }, [categories])

  return {
    events,
    categories,
    selectedEvent,
    setSelectedEvent,
    createEvent,
    updateEvent,
    deleteEvent,
    getEventsForDate,
    getEventsForDateRange,
    getCategoryById,
  }
}