@echo off
rem $Id$

rem -----------------------------------------------------------------------------
rem check the server status
rem -----------------------------------------------------------------------------
call .\..\setEnv.bat
call .\..\bin\setCommonEnv.bat

:START
echo Please take a backup of Database before running this script.
set /P DO_PROCESS=Do you really want to continue this script? [y/n]: 
if "%DO_PROCESS%" NEQ "y" goto END

set errmsg="Firewall Analyzer Server is running. Server should not run while running this tool. Please shutdown the server before running this tool."

cd ..
set HOME_DIR="%CD%"
set ENT_FILE=%HOME_DIR%\enterprise.txt
set JAVA_HOME=%HOME_DIR%\jre

set UTIL_CLASSPATH=%FIPS_CLASSPATH%;%HOME_DIR%\lib\*;
set JAVA=%JAVA_HOME%\bin\java
echo.
echo Checking Server status. Please wait
cd troubleshooting
set ERRORLEVEL=
%JAVA% %FIPS_Config% -Dserver.home=%HOME_DIR% -cp %UTIL_CLASSPATH% com.adventnet.la.util.LAClientUtil

set e=%ERRORLEVEL%
echo %e%
if %e% NEQ 0  goto ERRMSG

goto CreateEnterpriseTxt



rem -------------------------------------------------------------------------
rem Give Warning Message about the Server run
rem ------------------------------------------------------------------------
:ERRMSG
if %e% EQU 2 (echo "Postgres is still running. Please execute <Opm-Home>\bin\stopPgSQL.bat to stop database") else (echo %errmsg%)
rem echo %errmsg%
goto END



rem -------------------------------------------------------------------------
rem Creating enterprise.txt file
rem ------------------------------------------------------------------------

:CreateEnterpriseTxt
cd %HOME_DIR%\troubleshooting
if EXIST %HOME_DIR%\enterprise.txt goto RegisterWithAdmin

set /P MY_WEB_PORT=Enter Web Port of this server [8060] :
set /P MY_WEB_PROTO=Enter Web Server Protocol of this server [http] :
set /P ADMIN_NAME=Enter Admin Server Name/IPAddress :
if "%ADMIN_NAME%" == "" goto PRINTERROR
set /P ADMIN_WEB_PORT=Enter Admin Server Web Port [8060] :
set /P ADMIN_WEB_PROTO=Enter Admin Server Web Protocol [http] :
set /P ADMIN_SERVERID=Enter Admin Server ID :
if "%ADMIN_SERVERID%" == "" goto PRINTNOTE


if "%MY_WEB_PORT%" == "" set MY_WEB_PORT=8060
if "%MY_WEB_PROTO%" == "" set MY_WEB_PROTO=http
if "%ADMIN_WEB_PORT%" == "" set ADMIN_WEB_PORT=8060
if "%ADMIN_WEB_PROTO%" == "" set ADMIN_WEB_PROTO=http

echo context=fw>>%ENT_FILE%
echo server.type=DS>>%ENT_FILE%
echo webserver.port=%MY_WEB_PORT%>>%ENT_FILE%
echo webserver.protocol=%MY_WEB_PROTO%>>%ENT_FILE%
echo adminserver.hostname=%ADMIN_NAME%>>%ENT_FILE%
echo adminserver.webserver.port=%ADMIN_WEB_PORT%>>%ENT_FILE%
echo adminserver.webserver.protocol=%ADMIN_WEB_PROTO%>>%ENT_FILE%
echo adminserver.id=%ADMIN_SERVERID%>>%ENT_FILE%


echo isConverted=true>>%ENT_FILE%

set /P PROXY_NEEDED=Use Proxy to reach Admin server [n/y] :
if "%PROXY_NEEDED%" NEQ "y" goto RegisterWithAdmin

set /P PROXY_HOST=Enter Proxy Server Name :
set /P PROXY_PORT=Enter Proxy Server Port :
set /P PROXY_UNAME=Enter Proxy UserName :
set /P PROXY_PASSW=Enter Proxy Password :

if "%PROXY_HOST%" NEQ "" (if "%PROXY_PORT%" NEQ "" (if "%PROXY_UNAME%" NEQ "" (if "%PROXY_PASSW%" NEQ "" (goto WRITEPROXYINFO))))
goto RegisterWithAdmin


:PRINTERROR
echo Value should not be empty
goto END


:PRINTNOTE
echo Server ID should not be empty. Please refer Settings -> Collector ->Collector Settings page of Admin Server to get the Id 
goto END


:WRITEPROXYINFO
echo proxy.host=%PROXY_HOST%>>%ENT_FILE%
echo proxy.port=%PROXY_PORT%>>%ENT_FILE%
echo proxy.username=%PROXY_UNAME%>>%ENT_FILE%
echo proxy.password=%PROXY_PASSW%>>%ENT_FILE%



rem -------------------------------------------------------------------------
rem Register this collector in Admin server
rem ------------------------------------------------------------------------
:RegisterWithAdmin
rem call %HOME_DIR%\troubleshooting\registerWithAdminServer.bat
set REGISTER_CLASSPATH=%FIPS_CLASSPATH%;%HOME_DIR%\classes\HTTPClient.jar;%HOME_DIR%\classes\OpManagerServerClasses.jar;%HOME_DIR%\lib\jcifs-ng-2.1.10.jar;%HOME_DIR%\lib\EnterpriseStartUP.jar;%HOME_DIR%\lib\AdvPersistence.jar;%HOME_DIR%\lib\AdvAuthentication.jar;%HOME_DIR%\lib\ear\EncryptUtil.jar
%JAVA% %FIPS_Config% -Dserver.home=%HOME_DIR% -cp %REGISTER_CLASSPATH% com.adventnet.la.enterprise.EnterpriseStartUpUtil %HOME_DIR% converted
goto ExecuteUpdateQueries



rem -------------------------------------------------------------------------
rem Execute Convertion query
rem ------------------------------------------------------------------------

:ExecuteUpdateQueries
set DRIVER_CLASSES=%HOME_DIR%\lib\mssql-jdbc-12.2.0.jre8.jar;%HOME_DIR%\lib\postgresql-42.7.3.jar

set My_CLASSPATH=%FIPS_CLASSPATH%;%DRIVER_CLASSES%;%HOME_DIR%\bin\run.jar;%HOME_DIR%\lib\*;%HOME_DIR%\classes\*;

set JAVA_OPTS=%FIPS_Config% -Ddb.home=%DB_HOME% -DseparateJVM=true -Dserver.home=%HOME_DIR% -Xms512m -Xmx1024m
%JAVA% %JAVA_OPTS% -cp %My_CLASSPATH% com.adventnet.fa.server.enterprise.HandleCollectorConversion
goto END


rem -------------------------------------------------------------------------
rem Cleanup all variables
rem ------------------------------------------------------------------------

:END
set ENT_FILE=
set DB_HOME=
set DB_PORT=
set DB_USER=
set DB_HOST=
set PASSWORD=
set DB_NAME=
set HOME_DIR=
set JAVA_HOME=
set CHERRY_HOME=
set UTIL_CLASSPATH=
set JAVA=
set MY_WEB_PORT=
set MY_WEB_PROTO=
set ADMIN_NAME=
set ADMIN_WEB_PORT=
set ADMIN_WEB_PROTO=
set ADMIN_SERVERID=
set MY_CLASSPATH=
