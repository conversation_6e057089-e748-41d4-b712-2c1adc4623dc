import { format, isSameMonth, isToday } from 'date-fns'
import { Paper, SimpleGrid, Stack, Group, Text, Badge } from '@mantine/core'
import { CalendarEvent } from '../types/calendar'
import { EventCard } from './EventCard'

interface CalendarGridProps {
  days: Date[]
  currentDate: Date
  selectedDate: Date | null
  onDayClick: (day: Date) => void
  events: CalendarEvent[]
  onEventClick: (event: CalendarEvent) => void
  onDateDoubleClick: (date: Date) => void
}

export function CalendarGrid({ 
  days, 
  currentDate, 
  selectedDate, 
  onDayClick, 
  events, 
  onEventClick,
  onDateDoubleClick 
}: CalendarGridProps) {
  const weekDays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  
  const getEventsForDay = (day: Date) => {
    return events.filter(event => {
      const eventStart = format(event.start, 'yyyy-MM-dd')
      const eventEnd = format(event.end, 'yyyy-MM-dd')
      const currentDay = format(day, 'yyyy-MM-dd')
      
      // Check if the day falls within the event's date range
      return currentDay >= eventStart && currentDay <= eventEnd
    })
  }

  const isFirstDayOfEvent = (event: CalendarEvent, day: Date) => {
    return format(event.start, 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd')
  }

  const isLastDayOfEvent = (event: CalendarEvent, day: Date) => {
    return format(event.end, 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd')
  }

  const isMiddleDayOfEvent = (event: CalendarEvent, day: Date) => {
    const eventStart = format(event.start, 'yyyy-MM-dd')
    const eventEnd = format(event.end, 'yyyy-MM-dd')
    const currentDay = format(day, 'yyyy-MM-dd')
    
    return currentDay > eventStart && currentDay < eventEnd
  }
  const handleDayDoubleClick = (day: Date) => {
    onDateDoubleClick(day)
  }

  return (
    <Paper className="flex-1 overflow-auto" p={0}>
      <SimpleGrid cols={7} spacing={0}>
        {weekDays.map(day => (
          <Paper key={day} className="calendar-header p-2 text-center border-b">
            <Text size="sm" fw={600} c="var(--primary-color)">
              {day}
            </Text>
          </Paper>
        ))}
        {days.map(day => {
          const isCurrentMonth = isSameMonth(day, currentDate)
          const dayEvents = getEventsForDay(day)
          
          return (
            <Paper
              key={day.toString()}
              onClick={() => onDayClick(day)}
              onDoubleClick={() => handleDayDoubleClick(day)}
              className={`calendar-day ${
                isToday(day) ? 'today' : ''
              } ${selectedDate && day.toDateString() === selectedDate.toDateString() ? 'selected' : ''} ${
                !isCurrentMonth ? 'out-of-month' : ''
              }`}
              p="xs"
              withBorder
              style={{ cursor: 'pointer', minHeight: '120px' }}
            >
              <Text
                size="sm"
                fw={500}
                mb="xs"
                c={!isCurrentMonth ? 'dimmed' : isToday(day) ? 'var(--primary-color)' : undefined}
              >
                {format(day, 'd')}
              </Text>
              
              <Stack gap="xs">
                {dayEvents.slice(0, 3).map(event => (
                  <EventCard
                    key={event.id}
                    event={event}
                    onClick={onEventClick}
                    isCompact={true}
                    isFirstDay={isFirstDayOfEvent(event, day)}
                    isLastDay={isLastDayOfEvent(event, day)}
                    isMiddleDay={isMiddleDayOfEvent(event, day)}
                  />
                ))}
                {dayEvents.length > 3 && (
                  <Text size="xs" c="dimmed" px="xs">
                    +{dayEvents.length - 3} more
                  </Text>
                )}
              </Stack>
            </Paper>
          )
        })}
      </SimpleGrid>
    </Paper>
  )
}