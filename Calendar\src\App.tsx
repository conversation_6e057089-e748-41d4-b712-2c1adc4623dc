import { useState, useEffect } from 'react';
import { format, eachDayOfInterval, startOfMonth, endOfMonth } from 'date-fns';
import { Button, ActionIcon, Tooltip } from '@mantine/core';
import { ChevronLeftIcon, ChevronRightIcon, SunIcon } from '@heroicons/react/24/outline';
import { CalendarGrid } from './components/CalendarGrid';
import { DayView } from './components/DayView';
import { WeekView } from './components/WeekView';
import { YearView } from './components/YearView';
import { NewItemMenu } from './components/NewItemMenu';
import { ViewSelector } from './components/ViewSelector';
import { EventModal } from './components/EventModal';
import { CalendarEvent } from './types/calendar';
import { useCalendarStore } from './store/useCalendarStore';
import ErrorBoundary from './components/ErrorBoundary';
import { NotificationProvider, useNotifications, showAppNotification } from './components/NotificationProvider';

function App() {
  // State from store
  const {
    currentDate,
    selectedDate,
    view,
    setCurrentDate,
    setSelectedDate,
    setView,
    nextPeriod,
    prevPeriod,
    addEvent,
    updateEvent,
    getEventsForDate,
    getEventsForDateRange,
    categories,
  } = useCalendarStore();

  // Local UI state
  const [showEventModal, setShowEventModal] = useState(false);
  const [selectedDateForEvent, setSelectedDateForEvent] = useState<Date | null>(null);
  const [editingEvent, setEditingEvent] = useState<CalendarEvent | null>(null);

  const { showSuccess, showError } = useNotifications();

  const handleMenuItemClick = () => {
    setSelectedDateForEvent(new Date());
    setEditingEvent(null);
    setShowEventModal(true);
  };

  const handleEventClick = (event: CalendarEvent) => {
    setEditingEvent(event);
    setSelectedDateForEvent(null);
    setShowEventModal(true);
  };

  const handleDateDoubleClick = (date: Date) => {
    setSelectedDateForEvent(date);
    setEditingEvent(null);
    setShowEventModal(true);
  };

  const handleTimeSlotClick = (date: Date) => {
    setSelectedDateForEvent(date);
    setEditingEvent(null);
    setShowEventModal(true);
  };

  const handleSaveEvent = async (eventData: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      if (editingEvent) {
        const result = updateEvent(editingEvent.id, eventData);
        if (result.success) {
          showSuccess('Event updated successfully');
        } else if (result.error) {
          showError(`Failed to update event: ${result.error.message}`);
        }
      } else {
        const result = addEvent(eventData);
        if (result.success) {
          showSuccess('Event created successfully');
        } else if (result.error) {
          showError(`Failed to create event: ${result.error.message}`);
        }
      }
      setShowEventModal(false);
    } catch (error) {
      console.error('Error saving event:', error);
      showError('An unexpected error occurred while saving the event');
    }
  };

  const handleViewChange = (newView: 'day' | 'workweek' | 'week' | 'month' | 'year') => {
    setView(newView);
  };

  const renderCalendarView = () => {
    const daysInMonth = eachDayOfInterval({
      start: startOfMonth(currentDate),
      end: endOfMonth(currentDate)
    });
    
    switch (view) {
      case 'day':
        return (
          <DayView
            currentDate={currentDate}
            selectedDate={selectedDate}
            onDayClick={setSelectedDate}
            events={getEventsForDate(selectedDate || currentDate)}
            onEventClick={handleEventClick}
            onTimeSlotClick={handleTimeSlotClick}
          />
        );
      case 'workweek':
      case 'week':
        return (
          <WeekView
            currentDate={currentDate}
            selectedDate={selectedDate}
            onDayClick={setSelectedDate}
            events={getEventsForDateRange(
              // Get events for the current week
              new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() - currentDate.getDay()),
              new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() + (6 - currentDate.getDay()))
            )}
            onEventClick={handleEventClick}
            onTimeSlotClick={handleTimeSlotClick}
            isWorkWeek={view === 'workweek'}
          />
        );
      case 'month':
        return (
          <CalendarGrid
            days={daysInMonth} // Need to calculate days in month
            currentDate={currentDate}
            selectedDate={selectedDate}
            onDayClick={setSelectedDate}
            events={getEventsForDateRange(
              // Get events for the current month
              new Date(currentDate.getFullYear(), currentDate.getMonth(), 1),
              new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
            )}
            onEventClick={handleEventClick}
            onDateDoubleClick={handleDateDoubleClick}
          />
        );
      case 'year':
        return (
          <YearView
            currentDate={currentDate}
            selectedDate={selectedDate}
            onMonthSelect={(date) => {
              setCurrentDate(date);
              setView('month');
            }}
            events={getEventsForDateRange(
              new Date(currentDate.getFullYear(), 0, 1),
              new Date(currentDate.getFullYear(), 11, 31)
            )}
            onEventClick={handleEventClick}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Calendar
              </h1>
              <div className="ml-6 flex items-center space-x-4">
                <Button
                  variant="subtle"
                  leftSection={<ChevronLeftIcon className="h-5 w-5" />}
                  onClick={prevPeriod}
                  aria-label="Previous period"
                />
                <div className="text-lg font-medium text-gray-900 dark:text-white">
                  {format(currentDate, 'MMMM yyyy')}
                </div>
                <Button
                  variant="subtle"
                  leftSection={<ChevronRightIcon className="h-5 w-5" />}
                  onClick={nextPeriod}
                  aria-label="Next period"
                />
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <ViewSelector view={view} onChange={handleViewChange} />
              <NewItemMenu onItemClick={handleMenuItemClick} onClose={() => {}} />
              <Tooltip label="Toggle color scheme">
                <ActionIcon
                  variant="default"
                  size="lg"
                  onClick={() => {}}
                  aria-label="Toggle color scheme"
                >
                  <SunIcon className="h-5 w-5" />
                </ActionIcon>
              </Tooltip>
            </div>
          </div>
        </div>
      </header>

      <main className="flex-1 overflow-auto p-4">
        {renderCalendarView()}
      </main>

      <EventModal
        isOpen={showEventModal}
        onClose={() => setShowEventModal(false)}
        onSave={handleEventSave}
        event={editingEvent}
        selectedDate={selectedDateForEvent || new Date()}
        categories={categories}
      />

    </div>
  )
}

export default App