import { useState, useEffect, useCallback } from 'react';
import {
  Title,
  Text,
  Card,
  Group,
  Badge,
  Stack,
  Button,
  ActionIcon,
  TextInput,
  Switch,
  Tooltip,
  useMantineColorScheme
} from "@mantine/core";
import { format } from 'date-fns';
import {
  IconCalendar,
  IconPlus,
  IconChevronLeft,
  IconChevronRight,
  IconBell,
  IconSun,
  IconMoon
} from "@tabler/icons-react";
import DashboardLayout from "../layouts/DashboardLayout";
import WidgetMenu from "../components/WidgetMenu";
import { useLayoutManager } from "../hooks/useLayoutManager";
import { GenericCardWidget } from '../components/widgets/GenericCardWidget';
import { DashboardWidget } from '../types/dashboard';
import { Responsive, WidthProvider } from 'react-grid-layout';

// For now, let's create a simplified version without the custom calendar components
// to avoid import issues. We'll use a basic calendar implementation.

const ResponsiveGridLayout = WidthProvider(Responsive);

// Test Calendar layouts - larger calendar widget to use more page space
const testCalendarLayouts = {
  lg: [
    { i: 'calendar-main', x: 0, y: 0, w: 9, h: 12, minW: 6, minH: 10 },
    { i: 'calendar-controls', x: 9, y: 0, w: 3, h: 6, minW: 2, minH: 4 },
    { i: 'event-summary', x: 9, y: 6, w: 3, h: 6, minW: 2, minH: 4 }
  ],
  md: [
    { i: 'calendar-main', x: 0, y: 0, w: 7, h: 12, minW: 5, minH: 10 },
    { i: 'calendar-controls', x: 7, y: 0, w: 3, h: 6, minW: 3, minH: 4 },
    { i: 'event-summary', x: 7, y: 6, w: 3, h: 6, minW: 3, minH: 4 }
  ],
  sm: [
    { i: 'calendar-main', x: 0, y: 0, w: 6, h: 12, minW: 6, minH: 10 },
    { i: 'calendar-controls', x: 0, y: 12, w: 6, h: 4, minW: 6, minH: 3 },
    { i: 'event-summary', x: 0, y: 16, w: 6, h: 4, minW: 6, minH: 3 }
  ]
};

// Widget definitions
const testCalendarWidgets: DashboardWidget[] = [
  {
    id: 'calendar-main',
    type: 'calendar-main',
    title: 'Advanced Calendar',
    description: 'Full-featured calendar with multiple views and event management'
  },
  {
    id: 'calendar-controls',
    type: 'calendar-controls', 
    title: 'Calendar Controls',
    description: 'View selector and navigation controls'
  },
  {
    id: 'event-summary',
    type: 'event-summary',
    title: 'Event Summary',
    description: 'Summary of upcoming events and statistics'
  }
];

const TestCalendar = () => {
  // Get main dashboard theme
  const { colorScheme } = useMantineColorScheme();

  // Simplified calendar state
  const [view, setView] = useState<'month' | 'week' | 'day' | 'year'>('month');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  // Calendar-specific theme override (defaults to dashboard theme)
  const [calendarThemeOverride, setCalendarThemeOverride] = useState<'auto' | 'light' | 'dark'>('auto');

  // Determine effective dark mode state
  const isDarkMode = calendarThemeOverride === 'auto'
    ? colorScheme === 'dark'
    : calendarThemeOverride === 'dark';

  const [events, setEvents] = useState([
    {
      id: '1',
      title: 'Team Meeting',
      date: new Date(),
      type: 'meeting',
      color: 'blue'
    },
    {
      id: '2',
      title: 'Project Deadline',
      date: new Date(Date.now() + 86400000), // Tomorrow
      type: 'deadline',
      color: 'red'
    }
  ]);

  // Dashboard state
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showWidgetMenu, setShowWidgetMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ x: 100, y: 100 });
  const [isDragging, setIsDragging] = useState(false);
  const [visibleWidgets, setVisibleWidgets] = useState(new Set(testCalendarWidgets.map(w => w.id)));

  // Load saved widget visibility and theme override
  useEffect(() => {
    try {
      const savedVisibility = localStorage.getItem('test-calendar-visible-widgets');
      if (savedVisibility) {
        setVisibleWidgets(new Set(JSON.parse(savedVisibility)));
      }

      const savedThemeOverride = localStorage.getItem('test-calendar-theme-override') as 'auto' | 'light' | 'dark';
      if (savedThemeOverride) {
        setCalendarThemeOverride(savedThemeOverride);
      }
    } catch (error) {
      console.error('Error loading saved settings:', error);
    }
  }, []);

  // Layout management
  const {
    layouts,
    layoutPresets,
    currentLayoutId,
    switchToLayout,
    saveCurrentLayout,
    deleteLayout,
    resetToDefault,
    handleLayoutChange
  } = useLayoutManager({
    pageKey: 'test-calendar',
    defaultLayouts: testCalendarLayouts,
    defaultLayoutName: 'Default Test Calendar Layout'
  });

  // Simplified calendar event handlers
  const handlePrevMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1));
  };

  const handleNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1));
  };

  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
  };

  const handleViewChange = (newView: 'month' | 'week' | 'day' | 'year') => {
    setView(newView);
  };

  const toggleCalendarTheme = () => {
    const nextTheme = calendarThemeOverride === 'auto'
      ? 'light'
      : calendarThemeOverride === 'light'
        ? 'dark'
        : 'auto';

    setCalendarThemeOverride(nextTheme);
    localStorage.setItem('test-calendar-theme-override', nextTheme);
  };

  // Year view rendering
  const renderYearView = () => {
    const year = currentDate.getFullYear();
    const months = [];

    for (let month = 0; month < 12; month++) {
      months.push(new Date(year, month, 1));
    }

    return (
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(4, 1fr)',
        gap: '16px',
        padding: '16px',
        height: '100%'
      }}>
        {months.map((monthDate, index) => {
          const monthStart = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1);
          const monthEnd = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0);
          const startDate = new Date(monthStart);
          startDate.setDate(startDate.getDate() - monthStart.getDay());

          const days = [];
          const date = new Date(startDate);

          for (let i = 0; i < 42; i++) {
            days.push(new Date(date));
            date.setDate(date.getDate() + 1);
          }

          return (
            <div
              key={index}
              style={{
                backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
                border: `1px solid ${isDarkMode ? '#4b5563' : '#e5e7eb'}`,
                borderRadius: '8px',
                padding: '8px',
                cursor: 'pointer'
              }}
              onClick={() => {
                setCurrentDate(monthDate);
                setView('month');
              }}
            >
              {/* Month Header */}
              <div style={{
                textAlign: 'center',
                fontWeight: 600,
                fontSize: '12px',
                marginBottom: '8px',
                color: isDarkMode ? '#ffffff' : '#374151'
              }}>
                {format(monthDate, 'MMM yyyy')}
              </div>

              {/* Mini Calendar Grid */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(7, 1fr)',
                gap: '1px',
                fontSize: '10px'
              }}>
                {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map(day => (
                  <div key={day} style={{
                    textAlign: 'center',
                    fontWeight: 500,
                    color: isDarkMode ? '#9ca3af' : '#6b7280',
                    padding: '2px'
                  }}>
                    {day}
                  </div>
                ))}
                {days.slice(0, 35).map((day, dayIndex) => {
                  const isCurrentMonth = day.getMonth() === monthDate.getMonth();
                  const isToday = day.toDateString() === new Date().toDateString();
                  const dayEvents = events.filter(event => event.date.toDateString() === day.toDateString());

                  return (
                    <div
                      key={dayIndex}
                      style={{
                        textAlign: 'center',
                        padding: '2px',
                        opacity: isCurrentMonth ? 1 : 0.3,
                        color: isToday
                          ? isDarkMode ? '#34d399' : '#10b981'
                          : isDarkMode ? '#f3f4f6' : '#374151',
                        fontWeight: isToday ? 600 : 400,
                        backgroundColor: dayEvents.length > 0
                          ? isDarkMode ? '#3b82f6' : '#dbeafe'
                          : 'transparent',
                        borderRadius: '2px'
                      }}
                    >
                      {day.getDate()}
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Simple calendar view rendering
  const renderCalendarView = () => {
    if (view === 'year') {
      return renderYearView();
    }

    const monthStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const monthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    const startDate = new Date(monthStart);
    startDate.setDate(startDate.getDate() - monthStart.getDay());

    const days = [];
    const date = new Date(startDate);

    for (let i = 0; i < 42; i++) {
      days.push(new Date(date));
      date.setDate(date.getDate() + 1);
    }

    return (
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(7, 1fr)',
        gridTemplateRows: 'auto repeat(6, 1fr)',
        gap: '1px',
        backgroundColor: isDarkMode ? '#374151' : '#e5e7eb',
        height: '100%',
        minHeight: '500px'
      }}>
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} style={{
            padding: '12px',
            backgroundColor: isDarkMode ? '#4b5563' : '#f9fafb',
            textAlign: 'center',
            fontWeight: 600,
            color: isDarkMode ? '#f3f4f6' : '#374151',
            fontSize: '14px',
            border: isDarkMode ? '1px solid #6b7280' : 'none'
          }}>
            {day}
          </div>
        ))}
        {days.map((day, index) => {
          const isCurrentMonth = day.getMonth() === currentDate.getMonth();
          const isToday = day.toDateString() === new Date().toDateString();
          const isSelected = selectedDate && day.toDateString() === selectedDate.toDateString();
          const dayEvents = events.filter(event => event.date.toDateString() === day.toDateString());

          return (
            <div
              key={index}
              onClick={() => handleDateClick(day)}
              style={{
                padding: '8px',
                backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
                cursor: 'pointer',
                opacity: isCurrentMonth ? 1 : 0.4,
                border: isSelected
                  ? `2px solid ${isDarkMode ? '#60a5fa' : '#3b82f6'}`
                  : isToday
                    ? `2px solid ${isDarkMode ? '#34d399' : '#10b981'}`
                    : isDarkMode
                      ? '1px solid #4b5563'
                      : 'none',
                color: isDarkMode ? '#f3f4f6' : '#374151',
                display: 'flex',
                flexDirection: 'column',
                minHeight: '0',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (!isSelected && !isToday) {
                  e.currentTarget.style.backgroundColor = isDarkMode ? '#374151' : '#f3f4f6';
                }
              }}
              onMouseLeave={(e) => {
                if (!isSelected && !isToday) {
                  e.currentTarget.style.backgroundColor = isDarkMode ? '#1f2937' : '#ffffff';
                }
              }}
            >
              <div style={{
                fontWeight: isToday ? 600 : 400,
                fontSize: '14px',
                marginBottom: '4px',
                color: isToday
                  ? isDarkMode ? '#34d399' : '#10b981'
                  : isSelected
                    ? isDarkMode ? '#60a5fa' : '#3b82f6'
                    : isDarkMode ? '#f3f4f6' : '#374151'
              }}>
                {day.getDate()}
              </div>
              <div style={{ flex: 1, overflow: 'hidden' }}>
                {dayEvents.map(event => (
                  <div
                    key={event.id}
                    style={{
                      fontSize: '11px',
                      backgroundColor: event.color === 'blue'
                        ? isDarkMode ? '#3b82f6' : '#2563eb'
                        : isDarkMode ? '#ef4444' : '#dc2626',
                      color: 'white',
                      padding: '2px 4px',
                      borderRadius: '3px',
                      marginBottom: '2px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      boxShadow: isDarkMode ? '0 1px 2px rgba(0, 0, 0, 0.3)' : '0 1px 2px rgba(0, 0, 0, 0.1)'
                    }}
                  >
                    {event.title}
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Dashboard widget management
  const toggleWidgetVisibility = (widgetId: string) => {
    setVisibleWidgets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(widgetId)) {
        newSet.delete(widgetId);
      } else {
        newSet.add(widgetId);
      }
      localStorage.setItem('test-calendar-visible-widgets', JSON.stringify([...newSet]));
      return newSet;
    });
  };

  const getInitialMenuPosition = () => ({
    x: Math.max(50, window.innerWidth - 370),
    y: 100
  });

  const resetToDefaultAndClose = () => {
    resetToDefault();
    setShowWidgetMenu(false);
  };

  const closeWidgetMenu = () => {
    setShowWidgetMenu(false);
  };

  // Menu dragging handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    const startX = e.clientX - menuPosition.x;
    const startY = e.clientY - menuPosition.y;

    const handleMouseMove = (e: MouseEvent) => {
      setMenuPosition({
        x: Math.max(0, Math.min(e.clientX - startX, window.innerWidth - 320)),
        y: Math.max(0, Math.min(e.clientY - startY, window.innerHeight - 400))
      });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1000);
  }, []);

  const allWidgets = testCalendarWidgets.map(widget => ({
    ...widget,
    isVisible: visibleWidgets.has(widget.id)
  }));

  const dashboardControls = {
    isEditing,
    loading,
    showWidgetMenu,
    onToggleEdit: () => setIsEditing(!isEditing),
    onRefresh: handleRefresh,
    onResetLayout: resetToDefaultAndClose,
    onToggleWidgetMenu: () => {
      if (!showWidgetMenu) {
        setMenuPosition(getInitialMenuPosition());
      }
      setShowWidgetMenu(!showWidgetMenu);
    }
  };

  // Widget render function
  const renderWidget = (widget: DashboardWidget) => {
    switch (widget.type) {
      case 'calendar-main':
        return (
          <Card
            h="100%"
            p="md"
            style={{
              backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
              borderColor: isDarkMode ? '#374151' : '#e5e7eb',
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            {/* Calendar Navigation with View Selector */}
            <Group justify="space-between" mb="md">
              <Group gap="xs">
                <ActionIcon
                  onClick={handlePrevMonth}
                  variant="subtle"
                  size="sm"
                  style={{
                    color: isDarkMode ? '#ffffff' : '#374151',
                    '&:hover': {
                      backgroundColor: isDarkMode ? '#4b5563' : '#f3f4f6'
                    }
                  }}
                >
                  <IconChevronLeft size={16} />
                </ActionIcon>

                <Text fw={600} style={{
                  color: isDarkMode ? '#ffffff' : '#374151',
                  fontSize: '16px',
                  minWidth: '140px',
                  textAlign: 'center'
                }}>
                  {format(currentDate, 'MMMM yyyy')}
                </Text>

                <ActionIcon
                  onClick={handleNextMonth}
                  variant="subtle"
                  size="sm"
                  style={{
                    color: isDarkMode ? '#ffffff' : '#374151',
                    '&:hover': {
                      backgroundColor: isDarkMode ? '#4b5563' : '#f3f4f6'
                    }
                  }}
                >
                  <IconChevronRight size={16} />
                </ActionIcon>
              </Group>

              <Button.Group>
                <Button
                  size="xs"
                  variant={view === 'month' ? 'filled' : 'outline'}
                  onClick={() => handleViewChange('month')}
                  style={{
                    backgroundColor: view === 'month'
                      ? isDarkMode ? '#3b82f6' : '#3b82f6'
                      : 'transparent',
                    borderColor: isDarkMode ? '#4b5563' : '#d1d5db',
                    color: view === 'month'
                      ? '#ffffff'
                      : isDarkMode ? '#f3f4f6' : '#374151'
                  }}
                >
                  Month
                </Button>
                <Button
                  size="xs"
                  variant={view === 'week' ? 'filled' : 'outline'}
                  onClick={() => handleViewChange('week')}
                  style={{
                    backgroundColor: view === 'week'
                      ? isDarkMode ? '#3b82f6' : '#3b82f6'
                      : 'transparent',
                    borderColor: isDarkMode ? '#4b5563' : '#d1d5db',
                    color: view === 'week'
                      ? '#ffffff'
                      : isDarkMode ? '#f3f4f6' : '#374151'
                  }}
                >
                  Week
                </Button>
                <Button
                  size="xs"
                  variant={view === 'day' ? 'filled' : 'outline'}
                  onClick={() => handleViewChange('day')}
                  style={{
                    backgroundColor: view === 'day'
                      ? isDarkMode ? '#3b82f6' : '#3b82f6'
                      : 'transparent',
                    borderColor: isDarkMode ? '#4b5563' : '#d1d5db',
                    color: view === 'day'
                      ? '#ffffff'
                      : isDarkMode ? '#f3f4f6' : '#374151'
                  }}
                >
                  Day
                </Button>
                <Button
                  size="xs"
                  variant={view === 'year' ? 'filled' : 'outline'}
                  onClick={() => handleViewChange('year')}
                  style={{
                    backgroundColor: view === 'year'
                      ? isDarkMode ? '#3b82f6' : '#3b82f6'
                      : 'transparent',
                    borderColor: isDarkMode ? '#4b5563' : '#d1d5db',
                    color: view === 'year'
                      ? '#ffffff'
                      : isDarkMode ? '#f3f4f6' : '#374151'
                  }}
                >
                  Year
                </Button>
              </Button.Group>
            </Group>

            {/* Calendar View */}
            <div style={{
              flex: 1,
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column'
            }}>
              {renderCalendarView()}
            </div>
          </Card>
        );

      case 'calendar-controls':
        return (
          <GenericCardWidget title="Calendar Controls" icon={<IconCalendar size={20} />}>
            <Stack gap="md">
              <Group justify="space-between">
                <Text size="sm" fw={500}>Calendar Theme</Text>
                <Group gap="xs">
                  <Badge
                    color={calendarThemeOverride === 'auto' ? 'blue' : 'gray'}
                    variant={calendarThemeOverride === 'auto' ? 'filled' : 'outline'}
                    size="xs"
                  >
                    {calendarThemeOverride === 'auto' ? `Auto (${colorScheme})` : calendarThemeOverride}
                  </Badge>
                  <Tooltip label={`Current: ${calendarThemeOverride === 'auto' ? `Auto (${colorScheme})` : calendarThemeOverride}. Click to cycle: Auto → Light → Dark`}>
                    <ActionIcon
                      onClick={toggleCalendarTheme}
                      variant="subtle"
                      size="sm"
                    >
                      {calendarThemeOverride === 'auto' ? (
                        <IconSun size={16} />
                      ) : isDarkMode ? (
                        <IconMoon size={16} />
                      ) : (
                        <IconSun size={16} />
                      )}
                    </ActionIcon>
                  </Tooltip>
                </Group>
              </Group>

              <Group justify="space-between">
                <Text size="sm" fw={500}>Current View</Text>
                <Badge color="blue" variant="light">
                  {view.charAt(0).toUpperCase() + view.slice(1)}
                </Badge>
              </Group>

              <Group justify="space-between">
                <Text size="sm" fw={500}>Selected Date</Text>
                <Text size="sm" c="dimmed">
                  {selectedDate ? format(selectedDate, 'MMM dd, yyyy') : 'None'}
                </Text>
              </Group>
            </Stack>
          </GenericCardWidget>
        );

      case 'event-summary':
        return (
          <GenericCardWidget title="Event Summary" icon={<IconBell size={20} />}>
            <Stack gap="md">
              <Group justify="space-between">
                <Text size="sm" fw={500}>Total Events</Text>
                <Badge color="green" variant="light">
                  {events.length}
                </Badge>
              </Group>

              <Group justify="space-between">
                <Text size="sm" fw={500}>Event Types</Text>
                <Badge color="orange" variant="light">
                  {new Set(events.map(e => e.type)).size}
                </Badge>
              </Group>

              <Group justify="space-between">
                <Text size="sm" fw={500}>Today's Events</Text>
                <Badge color="blue" variant="light">
                  {events.filter(e => e.date.toDateString() === new Date().toDateString()).length}
                </Badge>
              </Group>

              {selectedDate && (
                <Group justify="space-between">
                  <Text size="sm" fw={500}>Selected Date Events</Text>
                  <Badge color="purple" variant="light">
                    {events.filter(e => e.date.toDateString() === selectedDate.toDateString()).length}
                  </Badge>
                </Group>
              )}

              <div>
                <Text size="sm" fw={500} mb="xs">Recent Events</Text>
                {events.slice(0, 3).map(event => (
                  <div key={event.id} style={{ marginBottom: '4px' }}>
                    <Text size="xs" c="dimmed">
                      {event.title} - {format(event.date, 'MMM dd')}
                    </Text>
                  </div>
                ))}
              </div>
            </Stack>
          </GenericCardWidget>
        );

      default:
        return (
          <GenericCardWidget title={widget.title} icon={<IconCalendar size={20} />}>
            <Text c="dimmed">Widget type not implemented: {widget.type}</Text>
          </GenericCardWidget>
        );
    }
  };

  return (
    <DashboardLayout dashboardControls={dashboardControls}>
      <div style={{ width: '100%', position: 'relative', maxWidth: '1400px', margin: '0 auto' }}>

        {/* Dashboard Grid */}
        <div style={{ flex: 1, overflow: 'auto', paddingTop: '40px' }}>
          <ResponsiveGridLayout
            className={`layout ${isEditing ? 'editing' : ''}`}
            layouts={layouts}
            onLayoutChange={(layout, layouts) => {
              if (isEditing) {
                handleLayoutChange(layout, layouts);
              }
            }}
            breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
            cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
            rowHeight={60}
            isDraggable={isEditing}
            isResizable={isEditing}
            compactType="vertical"
            preventCollision={false}
            margin={[16, 16]}
            containerPadding={[0, 0]}
          >
            {testCalendarWidgets.map(widget => {
              const isVisible = visibleWidgets.has(widget.id);
              return (
                <div
                  key={widget.id}
                  className="widget-container"
                  style={{
                    display: isVisible ? 'block' : 'none'
                  }}
                >
                  {renderWidget(widget)}
                </div>
              );
            })}
          </ResponsiveGridLayout>
        </div>

        {/* Event Modal would go here in the full implementation */}

        {/* Floating Widget Selection Menu */}
        {showWidgetMenu && (
          <WidgetMenu
            widgets={testCalendarWidgets}
            visibleWidgets={visibleWidgets}
            onToggleWidgetVisibility={toggleWidgetVisibility}
            layoutPresets={layoutPresets}
            currentLayoutId={currentLayoutId}
            onLayoutChange={switchToLayout}
            onSaveLayout={saveCurrentLayout}
            onDeleteLayout={deleteLayout}
            onResetToDefault={resetToDefaultAndClose}
            menuPosition={menuPosition}
            isDragging={isDragging}
            onMouseDown={handleMouseDown}
            onClose={closeWidgetMenu}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default TestCalendar;
