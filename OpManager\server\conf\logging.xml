<?xml version="1.0" encoding="iso-8859-1"?>
<!-- $Id$ -->
<LoggingConfiguration>
    
	<!-- This is the default Configuration that should not be removed -->
	<Logger loggername="default" logger_level="INFO" useparenthandler="false">
		<HandlerList>
            <Handler formatterpattern="[%T]|[%D]|[%N]|[%L]|[%Th]|: %M|" type="FileHandler" handler_level="ALL" formatterclassname="com.adventnet.logging.DefaultFormatter" handlername="DefaultHandler" handlerfactoryname="com.adventnet.logging.internal.DefaultHandlerFactoryImpl">
		    <FileHandler append="true" archivedirectory="log/archive" filecount="10" filepattern="serverout%g.txt" archivestatus="true" logdirectory="log" maxlimit="10000000"/>
			</Handler>
		</HandlerList>
    </Logger>

<Logger loggername="com.adventnet.webclient.components.table.TableModelTag" logger_level="OFF" useparenthandler="true"/>
<Logger loggername="com.adventnet.authorization.AuthorizationEngine" logger_level="OFF" useparenthandler="true"/>
</LoggingConfiguration>
