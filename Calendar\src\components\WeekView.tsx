import { format, startOfWeek, addDays, isSameDay, isToday } from 'date-fns'
import { CalendarEvent } from '../types/calendar'
import { EventCard } from './EventCard'

interface WeekViewProps {
  currentDate: Date
  selectedDate: Date | null
  onDayClick: (day: Date) => void
  events: CalendarEvent[]
  onEventClick: (event: CalendarEvent) => void
  onTimeSlotClick: (date: Date, hour: number) => void
  isWorkWeek?: boolean
}

export function WeekView({ 
  currentDate, 
  selectedDate, 
  onDayClick, 
  events, 
  onEventClick,
  onTimeSlotClick,
  isWorkWeek = false,
}: WeekViewProps) {
  // Get the start of the week (Sunday)
  const weekStart = startOfWeek(currentDate)
  
  // Generate days for the week
  const days = isWorkWeek 
    ? Array.from({ length: 5 }, (_, i) => addDays(weekStart, i + 1)) // Monday to Friday
    : Array.from({ length: 7 }, (_, i) => addDays(weekStart, i)) // Sunday to Saturday
  
  // Generate hours
  const hours = Array.from({ length: 24 }, (_, i) => i)
  
  const formatHour = (hour: number) => {
    if (hour === 0) return '12 AM'
    if (hour === 12) return '12 PM'
    if (hour < 12) return `${hour} AM`
    return `${hour - 12} PM`
  }
  
  const getEventsForDay = (day: Date) => {
    return events.filter(event => {
      const eventStart = format(event.start, 'yyyy-MM-dd')
      const eventEnd = format(event.end, 'yyyy-MM-dd')
      const dayStr = format(day, 'yyyy-MM-dd')
      
      return dayStr >= eventStart && dayStr <= eventEnd
    })
  }
  
  const getEventsForHour = (day: Date, hour: number) => {
    return events.filter(event => {
      if (event.allDay) return false
      
      const eventStart = format(event.start, 'yyyy-MM-dd')
      const dayStr = format(day, 'yyyy-MM-dd')
      
      return dayStr === eventStart && event.start.getHours() === hour
    })
  }
  
  const handleTimeSlotClick = (day: Date, hour: number) => {
    const clickedDate = new Date(day)
    clickedDate.setHours(hour, 0, 0, 0)
    onTimeSlotClick(clickedDate, hour)
  }

  return (
    <div className="flex-1 flex flex-col bg-[var(--calendar-bg)] dark:bg-[var(--calendar-dark-bg)]">
      {/* Header */}
      <div className="flex border-b border-[var(--border-color)] bg-[var(--header-bg)] dark:bg-[var(--calendar-dark-bg)]">
        {/* Time column header */}
        <div className="w-20 flex-shrink-0 border-r border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
          <div className="p-2"></div>
        </div>
        
        {/* Day columns header */}
        {days.map((day, index) => (
          <div
            key={day.toString()}
            className={`flex-1 p-2 text-left cursor-pointer border-r border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
              selectedDate && isSameDay(day, selectedDate) ? 'bg-blue-500 text-white' : 'hover:bg-gray-50 dark:hover:bg-gray-700'
            } ${isToday(day) && (!selectedDate || !isSameDay(day, selectedDate)) ? 'bg-blue-50 dark:bg-blue-900/20 font-bold' : ''}`}
          >
            <div className={`text-xs ${isToday(day) ? 'text-blue-600 dark:text-blue-400 font-bold' : 'text-gray-500 dark:text-gray-400'}`}>
              {format(day, 'EEEE')}
            </div>
            <div className={`text-xl font-semibold ${
              selectedDate && isSameDay(day, selectedDate) ? 'text-white' : 
              isToday(day) ? 'text-blue-600 dark:text-blue-400 font-bold' : 'text-gray-900 dark:text-white'
            }`}>
              {format(day, 'd')}
            </div>
            
            {/* All day events */}
            <div className="mt-1 space-y-1">
              {getEventsForDay(day).filter(e => e.allDay).map(event => (
                <EventCard
                  key={event.id}
                  event={event}
                  onClick={onEventClick}
                  isCompact={true}
                />
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Time grid */}
      <div className="flex-1 flex overflow-auto bg-white dark:bg-gray-800">
        {/* Time column */}
        <div className="w-20 flex-shrink-0 border-r border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
          {hours.map((hour) => (
            <div
              key={hour}
              className="h-16 border-b border-gray-200 dark:border-gray-600 flex items-start justify-end pr-2 pt-1"
            >
              <span className="text-xs text-gray-600 dark:text-gray-400 font-medium">
                {formatHour(hour)}
              </span>
            </div>
          ))}
        </div>
        
        {/* Day columns */}
        {days.map((day, dayIndex) => (
          <div
            key={day.toString()}
            className="flex-1 border-r border-[var(--border-color)]"
          >
            {hours.map((hour) => (
              <div
                key={hour}
                className="h-16 border-b border-gray-200 dark:border-gray-600 border-r border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer p-1"
                onClick={() => handleTimeSlotClick(day, hour)}
              >
                <div className="space-y-1">
                  {getEventsForHour(day, hour).map(event => (
                    <EventCard
                      key={event.id}
                      event={event}
                      onClick={onEventClick}
                      isCompact={true}
                    />
                  ))}
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  )
}