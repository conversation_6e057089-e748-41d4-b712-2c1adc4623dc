import {
  createReactComponent
} from "./chunk-5HMDTYKJ.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowUpBar.mjs
var IconArrowUpBar = createReactComponent("outline", "arrow-up-bar", "IconArrowUpBar", [["path", { "d": "M12 21l0 -18", "key": "svg-0" }], ["path", { "d": "M15 6l-3 -3l-3 3", "key": "svg-1" }], ["path", { "d": "M9 21l6 0", "key": "svg-2" }]]);

export {
  IconArrowUpBar
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowUpBar.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-OYVTF7SD.js.map
