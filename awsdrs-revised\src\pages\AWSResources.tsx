import { useState, useEffect, useCallback } from "react";
import { Text, Card } from "@mantine/core";
import { IconServer, IconDatabase, IconCloud, IconCurrencyDollar } from "@tabler/icons-react";
import DashboardLayout from "../layouts/DashboardLayout";
import { MetricCard, LineChartWidget, BarChartWidget, PieChartWidget } from "../components/widgets";
import WidgetMenu from "../components/WidgetMenu";
import { useLayoutManager } from "../hooks/useLayoutManager";
import dataService from "../services/dataService";
import { Responsive, WidthProvider } from 'react-grid-layout';

const ResponsiveGridLayout = WidthProvider(Responsive);

// Define AWS Resources widgets (like Dashboard pattern)
interface AWSResourcesWidget {
  id: string;
  title: string;
  type: string;
}

const defaultAWSResourcesWidgets: AWSResourcesWidget[] = [
  { id: 'ec2-metric', title: 'EC2 Instances', type: 'ec2-metric' },
  { id: 'rds-metric', title: 'RDS Databases', type: 'rds-metric' },
  { id: 's3-metric', title: 'S3 Storage', type: 's3-metric' },
  { id: 'cost-metric', title: 'Monthly Cost', type: 'cost-metric' },
  { id: 'resource-usage-chart', title: 'Resource Usage Trends', type: 'resource-usage-chart' },
  { id: 'cost-breakdown-chart', title: 'Cost Breakdown', type: 'cost-breakdown-chart' },
  { id: 'regional-distribution-chart', title: 'Regional Distribution', type: 'regional-distribution-chart' }
];

// Default layout configuration (like Dashboard pattern)
const defaultAWSResourcesLayouts = {
  lg: [
    { i: 'ec2-metric', x: 0, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 'rds-metric', x: 3, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 's3-metric', x: 6, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 'cost-metric', x: 0, y: 2, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 'resource-usage-chart', x: 3, y: 2, w: 6, h: 4, minW: 4, minH: 3 },
    { i: 'cost-breakdown-chart', x: 0, y: 4, w: 3, h: 4, minW: 3, minH: 3 },
    { i: 'regional-distribution-chart', x: 0, y: 8, w: 9, h: 3, minW: 6, minH: 2 }
  ],
  md: [
    { i: 'ec2-metric', x: 0, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 'rds-metric', x: 3, y: 0, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 's3-metric', x: 6, y: 0, w: 4, h: 2, minW: 2, minH: 2 },
    { i: 'cost-metric', x: 0, y: 2, w: 3, h: 2, minW: 2, minH: 2 },
    { i: 'resource-usage-chart', x: 3, y: 2, w: 7, h: 4, minW: 4, minH: 3 },
    { i: 'cost-breakdown-chart', x: 0, y: 4, w: 3, h: 4, minW: 3, minH: 3 },
    { i: 'regional-distribution-chart', x: 0, y: 8, w: 10, h: 3, minW: 6, minH: 2 }
  ],
  sm: [
    { i: 'ec2-metric', x: 0, y: 0, w: 6, h: 2, minW: 4, minH: 2 },
    { i: 'rds-metric', x: 0, y: 2, w: 6, h: 2, minW: 4, minH: 2 },
    { i: 's3-metric', x: 0, y: 4, w: 6, h: 2, minW: 4, minH: 2 },
    { i: 'cost-metric', x: 0, y: 6, w: 6, h: 2, minW: 4, minH: 2 },
    { i: 'resource-usage-chart', x: 0, y: 8, w: 6, h: 4, minW: 6, minH: 3 },
    { i: 'cost-breakdown-chart', x: 0, y: 12, w: 6, h: 4, minW: 6, minH: 3 },
    { i: 'regional-distribution-chart', x: 0, y: 16, w: 6, h: 3, minW: 6, minH: 2 }
  ]
};

// Widget factory function (like Dashboard pattern)
const renderAWSResourcesWidget = (widget: AWSResourcesWidget) => {
  const awsMetrics = dataService.getAWSMetrics();
  const awsCostData = dataService.getAWSCostData();
  const awsResourceUsageData = dataService.getAWSResourceUsageData();
  const systemHealthData = dataService.getSystemHealthData();

  switch (widget.type) {
    case 'ec2-metric':
      return (
        <MetricCard
          title="EC2 Instances"
          value={awsMetrics.ec2.value}
          change={awsMetrics.ec2.change}
          changeType={awsMetrics.ec2.changeType}
          icon={<IconServer size={20} />}
          color="blue"
          subtitle="Running instances"
        />
      );
    case 'rds-metric':
      return (
        <MetricCard
          title="RDS Databases"
          value={awsMetrics.rds.value}
          change={awsMetrics.rds.change}
          changeType={awsMetrics.rds.changeType}
          icon={<IconDatabase size={20} />}
          color="orange"
          subtitle="Active databases"
        />
      );
    case 's3-metric':
      return (
        <MetricCard
          title="S3 Storage"
          value={awsMetrics.s3.value}
          change={awsMetrics.s3.change}
          changeType={awsMetrics.s3.changeType}
          icon={<IconCloud size={20} />}
          color="cyan"
          subtitle="Total storage used"
        />
      );
    case 'cost-metric':
      return (
        <MetricCard
          title="Monthly Cost"
          value={awsMetrics.cost.value}
          change={awsMetrics.cost.change}
          changeType={awsMetrics.cost.changeType}
          icon={<IconCurrencyDollar size={20} />}
          color="green"
          subtitle="Current month"
        />
      );
    case 'resource-usage-chart':
      return (
        <LineChartWidget
          title="Resource Usage Trends (30 Days)"
          data={awsResourceUsageData}
          lines={[
            { dataKey: 'instances', stroke: '#3b82f6', name: 'EC2 Instances' },
            { dataKey: 'databases', stroke: '#f59e0b', name: 'RDS Databases' },
            { dataKey: 'buckets', stroke: '#10b981', name: 'S3 Buckets' }
          ]}
          height={280}
          onRefresh={() => console.log('Refreshing resource usage...')}
          onExport={() => console.log('Exporting resource usage...')}
        />
      );
    case 'cost-breakdown-chart':
      return (
        <PieChartWidget
          title="Cost Breakdown"
          data={systemHealthData}
          colors={['#3b82f6', '#f59e0b', '#10b981', '#ef4444', '#8b5cf6']}
          height={280}
          onRefresh={() => console.log('Refreshing cost data...')}
          onExport={() => console.log('Exporting cost data...')}
        />
      );
    case 'regional-distribution-chart':
      return (
        <BarChartWidget
          title="Regional Distribution"
          data={awsCostData}
          bars={[
            { dataKey: 'ec2', fill: '#06b6d4', name: 'EC2 Costs' },
            { dataKey: 'rds', fill: '#8b5cf6', name: 'RDS Costs' },
            { dataKey: 's3', fill: '#10b981', name: 'S3 Costs' }
          ]}
          height={200}
          onRefresh={() => console.log('Refreshing regional data...')}
          onExport={() => console.log('Exporting regional data...')}
        />
      );
    default:
      return (
        <Card h="100%" p="md">
          <Text c="dimmed" ta="center">
            Widget: {widget.type}
          </Text>
        </Card>
      );
  }
};

const getInitialMenuPosition = () => ({
  x: Math.max(50, window.innerWidth - 370),
  y: 100
});

const AWSResources = () => {
  // Layout management with multiple presets
  const {
    layouts,
    layoutPresets,
    currentLayoutId,
    switchToLayout,
    saveCurrentLayout,
    deleteLayout,
    resetToDefault,
    handleLayoutChange
  } = useLayoutManager({
    pageKey: 'awsresources',
    defaultLayouts: defaultAWSResourcesLayouts,
    defaultLayoutName: 'Default AWS Resources Layout'
  });

  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [widgets] = useState(defaultAWSResourcesWidgets);
  const [visibleWidgets, setVisibleWidgets] = useState(new Set(defaultAWSResourcesWidgets.map(w => w.id)));
  const [showWidgetMenu, setShowWidgetMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState(getInitialMenuPosition());
  const [isDragging, setIsDragging] = useState(false);

  // Auto-refresh data
  useEffect(() => {
    const interval = setInterval(() => {
      // Trigger re-render for fresh data
      setLoading(false);
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Load saved widget visibility
  useEffect(() => {
    try {
      const savedVisibility = localStorage.getItem('awsresources-visible-widgets');
      if (savedVisibility) {
        setVisibleWidgets(new Set(JSON.parse(savedVisibility)));
      }
    } catch (error) {
      console.error('Error loading widget visibility:', error);
    }
  }, []);

  const toggleWidgetVisibility = (widgetId: string) => {
    setVisibleWidgets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(widgetId)) {
        newSet.delete(widgetId);
      } else {
        newSet.add(widgetId);
      }
      localStorage.setItem('awsresources-visible-widgets', JSON.stringify([...newSet]));
      return newSet;
    });
  };

  const closeWidgetMenu = () => {
    setShowWidgetMenu(false);
  };

  const resetToDefaultAndClose = () => {
    resetToDefault();
    setShowWidgetMenu(false);
    setMenuPosition(getInitialMenuPosition());
  };

  // Optimized drag handlers for floating menu
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target !== e.currentTarget && !(e.target as Element).closest('.widget-menu-header')) return;

    setIsDragging(true);
    const startX = e.clientX - menuPosition.x;
    const startY = e.clientY - menuPosition.y;

    const handleMouseMove = (e: MouseEvent) => {
      const newX = Math.max(0, Math.min(window.innerWidth - 300, e.clientX - startX));
      const newY = Math.max(0, Math.min(window.innerHeight - 200, e.clientY - startY));

      setMenuPosition({ x: newX, y: newY });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1000);
  }, []);

  // Create allWidgets array (like Dashboard)
  const allWidgets = widgets.filter(widget => visibleWidgets.has(widget.id));

  const dashboardControls = {
    isEditing,
    loading,
    showWidgetMenu,
    onToggleEdit: () => setIsEditing(!isEditing),
    onRefresh: handleRefresh,
    onResetLayout: resetToDefaultAndClose,
    onToggleWidgetMenu: () => {
      if (!showWidgetMenu) {
        setMenuPosition(getInitialMenuPosition());
      }
      setShowWidgetMenu(!showWidgetMenu);
    }
  };

  return (
    <DashboardLayout dashboardControls={dashboardControls}>
      <div style={{ width: '100%', position: 'relative', maxWidth: '1400px', margin: '0 auto' }}>

        {/* Dashboard Grid */}
        <div style={{ flex: 1, overflow: 'auto', paddingTop: '40px' }}>
          <ResponsiveGridLayout
            className={`layout ${isEditing ? 'editing' : ''}`}
            layouts={layouts}
            onLayoutChange={handleLayoutChange}
            breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
            cols={{ lg: 9, md: 10, sm: 6, xs: 4, xxs: 2 }}
            rowHeight={60}
            isDraggable={isEditing}
            isResizable={isEditing}
            margin={[16, 16]}
            containerPadding={[16, 16]}
            useCSSTransforms={true}
            compactType="vertical"
            preventCollision={false}
            allowOverlap={false}
          >
            {allWidgets.map((widget) => {
              const isVisible = visibleWidgets.has(widget.id);
              return (
                <div
                  key={widget.id}
                  className="widget-container"
                  style={{
                    display: isVisible ? 'block' : 'none'
                  }}
                >
                  <Card
                    h="100%"
                    p={0}
                    style={{
                      position: 'relative',
                      overflow: 'hidden',
                      border: isEditing ? '2px solid var(--mantine-color-blue-4)' : '1px solid var(--mantine-color-gray-3)',
                      borderRadius: '8px',
                      backgroundColor: 'var(--mantine-color-body)'
                    }}
                  >
                    {renderAWSResourcesWidget(widget)}
                  </Card>
                </div>
              );
            })}
          </ResponsiveGridLayout>
        </div>

        {/* Floating Widget Selection Menu */}
        {showWidgetMenu && (
          <WidgetMenu
            widgets={widgets}
            visibleWidgets={visibleWidgets}
            onToggleWidgetVisibility={toggleWidgetVisibility}
            layoutPresets={layoutPresets}
            currentLayoutId={currentLayoutId}
            onLayoutChange={switchToLayout}
            onSaveLayout={saveCurrentLayout}
            onDeleteLayout={deleteLayout}
            onResetToDefault={resetToDefaultAndClose}
            menuPosition={menuPosition}
            isDragging={isDragging}
            onMouseDown={handleMouseDown}
            onClose={closeWidgetMenu}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default AWSResources;
