export interface CalendarEvent {
  id: string
  title: string
  description?: string
  start: Date
  end: Date
  allDay: boolean
  color?: string
  category?: string
  recurring?: RecurringPattern
  reminders?: Reminder[]
  location?: string
  attendees?: string[]
  createdAt: Date
  updatedAt: Date
}

export interface RecurringPattern {
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly'
  interval: number
  endDate?: Date
  daysOfWeek?: number[]
  dayOfMonth?: number
}

export interface Reminder {
  id: string
  type: 'popup' | 'email' | 'notification'
  minutesBefore: number
  triggered?: boolean
}

export interface EventCategory {
  id: string
  name: string
  color: string
}

export type ViewType = 'day' | 'workweek' | 'week' | 'month' | 'year'