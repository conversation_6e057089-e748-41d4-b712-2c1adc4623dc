import { Select } from '@mantine/core'
import { 
  CalendarDaysIcon, 
  BriefcaseIcon, 
  CalendarIcon, 
  Squares2X2Icon,
  ViewColumnsIcon,
} from '@heroicons/react/24/outline'

const views = [
  { value: 'day', label: 'Day', icon: CalendarDaysIcon },
  { value: 'workweek', label: 'Work Week', icon: BriefcaseIcon },
  { value: 'week', label: 'Week', icon: ViewColumnsIcon },
  { value: 'month', label: 'Month', icon: Squares2X2Icon },
  { value: 'year', label: 'Year', icon: CalendarIcon },
]

interface ViewSelectorProps {
  view: 'day' | 'workweek' | 'week' | 'month' | 'year'
  onChange: (view: 'day' | 'workweek' | 'week' | 'month' | 'year') => void
}

export function ViewSelector({ view, onChange }: ViewSelectorProps) {
  return (
    <Select
      value={view}
      onChange={(value) => onChange(value as 'day' | 'workweek' | 'week' | 'month' | 'year')}
      data={views.map(v => ({ value: v.value, label: v.label }))}
      size="sm"
      w={160}
      styles={{
        input: {
          backgroundColor: 'white',
          borderColor: '#e5e7eb',
          color: '#374151',
          opacity: 1,
          '&:hover': {
            backgroundColor: '#f9fafb',
          },
          '&:focus': {
            backgroundColor: 'white',
            borderColor: '#3b82f6',
          }
        },
        dropdown: {
          backgroundColor: 'white',
          border: '1px solid #e5e7eb',
          opacity: 1,
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        }
      }}
    />
  )
}