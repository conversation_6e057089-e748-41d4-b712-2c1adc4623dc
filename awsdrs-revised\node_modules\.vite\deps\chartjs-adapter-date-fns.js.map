{"version": 3, "sources": ["../../chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.esm.js"], "sourcesContent": ["/*!\n * chartjs-adapter-date-fns v3.0.0\n * https://www.chartjs.org\n * (c) 2022 chartjs-adapter-date-fns Contributors\n * Released under the MIT license\n */\nimport { _adapters } from 'chart.js';\nimport { toDate, parse, parseISO, isValid, format, addYears, addQuarters, addMonths, addWeeks, addDays, addHours, addMinutes, addSeconds, addMilliseconds, differenceInYears, differenceInQuarters, differenceInMonths, differenceInWeeks, differenceInDays, differenceInHours, differenceInMinutes, differenceInSeconds, differenceInMilliseconds, startOfYear, startOfQuarter, startOfMonth, startOfWeek, startOfDay, startOfHour, startOfMinute, startOfSecond, endOfYear, endOfQuarter, endOfMonth, endOfWeek, endOfDay, endOfHour, endOfMinute, endOfSecond } from 'date-fns';\n\nconst FORMATS = {\n  datetime: 'MMM d, yyyy, h:mm:ss aaaa',\n  millisecond: 'h:mm:ss.SSS aaaa',\n  second: 'h:mm:ss aaaa',\n  minute: 'h:mm aaaa',\n  hour: 'ha',\n  day: 'MMM d',\n  week: 'PP',\n  month: 'MMM yyyy',\n  quarter: 'qqq - yyyy',\n  year: 'yyyy'\n};\n\n_adapters._date.override({\n  _id: 'date-fns', // DEBUG\n\n  formats: function() {\n    return FORMATS;\n  },\n\n  parse: function(value, fmt) {\n    if (value === null || typeof value === 'undefined') {\n      return null;\n    }\n    const type = typeof value;\n    if (type === 'number' || value instanceof Date) {\n      value = toDate(value);\n    } else if (type === 'string') {\n      if (typeof fmt === 'string') {\n        value = parse(value, fmt, new Date(), this.options);\n      } else {\n        value = parseISO(value, this.options);\n      }\n    }\n    return isValid(value) ? value.getTime() : null;\n  },\n\n  format: function(time, fmt) {\n    return format(time, fmt, this.options);\n  },\n\n  add: function(time, amount, unit) {\n    switch (unit) {\n    case 'millisecond': return addMilliseconds(time, amount);\n    case 'second': return addSeconds(time, amount);\n    case 'minute': return addMinutes(time, amount);\n    case 'hour': return addHours(time, amount);\n    case 'day': return addDays(time, amount);\n    case 'week': return addWeeks(time, amount);\n    case 'month': return addMonths(time, amount);\n    case 'quarter': return addQuarters(time, amount);\n    case 'year': return addYears(time, amount);\n    default: return time;\n    }\n  },\n\n  diff: function(max, min, unit) {\n    switch (unit) {\n    case 'millisecond': return differenceInMilliseconds(max, min);\n    case 'second': return differenceInSeconds(max, min);\n    case 'minute': return differenceInMinutes(max, min);\n    case 'hour': return differenceInHours(max, min);\n    case 'day': return differenceInDays(max, min);\n    case 'week': return differenceInWeeks(max, min);\n    case 'month': return differenceInMonths(max, min);\n    case 'quarter': return differenceInQuarters(max, min);\n    case 'year': return differenceInYears(max, min);\n    default: return 0;\n    }\n  },\n\n  startOf: function(time, unit, weekday) {\n    switch (unit) {\n    case 'second': return startOfSecond(time);\n    case 'minute': return startOfMinute(time);\n    case 'hour': return startOfHour(time);\n    case 'day': return startOfDay(time);\n    case 'week': return startOfWeek(time);\n    case 'isoWeek': return startOfWeek(time, {weekStartsOn: +weekday});\n    case 'month': return startOfMonth(time);\n    case 'quarter': return startOfQuarter(time);\n    case 'year': return startOfYear(time);\n    default: return time;\n    }\n  },\n\n  endOf: function(time, unit) {\n    switch (unit) {\n    case 'second': return endOfSecond(time);\n    case 'minute': return endOfMinute(time);\n    case 'hour': return endOfHour(time);\n    case 'day': return endOfDay(time);\n    case 'week': return endOfWeek(time);\n    case 'month': return endOfMonth(time);\n    case 'quarter': return endOfQuarter(time);\n    case 'year': return endOfYear(time);\n    default: return time;\n    }\n  }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,UAAU;AAAA,EACd,UAAU;AAAA,EACV,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AAAA,EACN,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AACR;AAEA,SAAU,MAAM,SAAS;AAAA,EACvB,KAAK;AAAA;AAAA,EAEL,SAAS,WAAW;AAClB,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,SAAS,OAAO,KAAK;AAC1B,QAAI,UAAU,QAAQ,OAAO,UAAU,aAAa;AAClD,aAAO;AAAA,IACT;AACA,UAAM,OAAO,OAAO;AACpB,QAAI,SAAS,YAAY,iBAAiB,MAAM;AAC9C,cAAQ,OAAO,KAAK;AAAA,IACtB,WAAW,SAAS,UAAU;AAC5B,UAAI,OAAO,QAAQ,UAAU;AAC3B,gBAAQ,MAAM,OAAO,KAAK,oBAAI,KAAK,GAAG,KAAK,OAAO;AAAA,MACpD,OAAO;AACL,gBAAQ,SAAS,OAAO,KAAK,OAAO;AAAA,MACtC;AAAA,IACF;AACA,WAAO,QAAQ,KAAK,IAAI,MAAM,QAAQ,IAAI;AAAA,EAC5C;AAAA,EAEA,QAAQ,SAAS,MAAM,KAAK;AAC1B,WAAO,OAAO,MAAM,KAAK,KAAK,OAAO;AAAA,EACvC;AAAA,EAEA,KAAK,SAAS,MAAM,QAAQ,MAAM;AAChC,YAAQ,MAAM;AAAA,MACd,KAAK;AAAe,eAAO,gBAAgB,MAAM,MAAM;AAAA,MACvD,KAAK;AAAU,eAAO,WAAW,MAAM,MAAM;AAAA,MAC7C,KAAK;AAAU,eAAO,WAAW,MAAM,MAAM;AAAA,MAC7C,KAAK;AAAQ,eAAO,SAAS,MAAM,MAAM;AAAA,MACzC,KAAK;AAAO,eAAO,QAAQ,MAAM,MAAM;AAAA,MACvC,KAAK;AAAQ,eAAO,SAAS,MAAM,MAAM;AAAA,MACzC,KAAK;AAAS,eAAO,UAAU,MAAM,MAAM;AAAA,MAC3C,KAAK;AAAW,eAAO,YAAY,MAAM,MAAM;AAAA,MAC/C,KAAK;AAAQ,eAAO,SAAS,MAAM,MAAM;AAAA,MACzC;AAAS,eAAO;AAAA,IAChB;AAAA,EACF;AAAA,EAEA,MAAM,SAAS,KAAK,KAAK,MAAM;AAC7B,YAAQ,MAAM;AAAA,MACd,KAAK;AAAe,eAAO,yBAAyB,KAAK,GAAG;AAAA,MAC5D,KAAK;AAAU,eAAO,oBAAoB,KAAK,GAAG;AAAA,MAClD,KAAK;AAAU,eAAO,oBAAoB,KAAK,GAAG;AAAA,MAClD,KAAK;AAAQ,eAAO,kBAAkB,KAAK,GAAG;AAAA,MAC9C,KAAK;AAAO,eAAO,iBAAiB,KAAK,GAAG;AAAA,MAC5C,KAAK;AAAQ,eAAO,kBAAkB,KAAK,GAAG;AAAA,MAC9C,KAAK;AAAS,eAAO,mBAAmB,KAAK,GAAG;AAAA,MAChD,KAAK;AAAW,eAAO,qBAAqB,KAAK,GAAG;AAAA,MACpD,KAAK;AAAQ,eAAO,kBAAkB,KAAK,GAAG;AAAA,MAC9C;AAAS,eAAO;AAAA,IAChB;AAAA,EACF;AAAA,EAEA,SAAS,SAAS,MAAM,MAAM,SAAS;AACrC,YAAQ,MAAM;AAAA,MACd,KAAK;AAAU,eAAO,cAAc,IAAI;AAAA,MACxC,KAAK;AAAU,eAAO,cAAc,IAAI;AAAA,MACxC,KAAK;AAAQ,eAAO,YAAY,IAAI;AAAA,MACpC,KAAK;AAAO,eAAO,WAAW,IAAI;AAAA,MAClC,KAAK;AAAQ,eAAO,YAAY,IAAI;AAAA,MACpC,KAAK;AAAW,eAAO,YAAY,MAAM,EAAC,cAAc,CAAC,QAAO,CAAC;AAAA,MACjE,KAAK;AAAS,eAAO,aAAa,IAAI;AAAA,MACtC,KAAK;AAAW,eAAO,eAAe,IAAI;AAAA,MAC1C,KAAK;AAAQ,eAAO,YAAY,IAAI;AAAA,MACpC;AAAS,eAAO;AAAA,IAChB;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,MAAM,MAAM;AAC1B,YAAQ,MAAM;AAAA,MACd,KAAK;AAAU,eAAO,YAAY,IAAI;AAAA,MACtC,KAAK;AAAU,eAAO,YAAY,IAAI;AAAA,MACtC,KAAK;AAAQ,eAAO,UAAU,IAAI;AAAA,MAClC,KAAK;AAAO,eAAO,SAAS,IAAI;AAAA,MAChC,KAAK;AAAQ,eAAO,UAAU,IAAI;AAAA,MAClC,KAAK;AAAS,eAAO,WAAW,IAAI;AAAA,MACpC,KAAK;AAAW,eAAO,aAAa,IAAI;AAAA,MACxC,KAAK;AAAQ,eAAO,UAAU,IAAI;AAAA,MAClC;AAAS,eAAO;AAAA,IAChB;AAAA,EACF;AACF,CAAC;", "names": []}