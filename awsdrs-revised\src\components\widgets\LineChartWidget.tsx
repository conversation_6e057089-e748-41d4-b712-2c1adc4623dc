import React, { useState, useRef } from 'react';
import { Card, Text, Group, ActionIcon, Tooltip } from '@mantine/core';
import { IconRefresh, IconDownload } from '@tabler/icons-react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  TimeScale,
  Filler,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import zoomPlugin from 'chartjs-plugin-zoom';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  ChartTooltip,
  Legend,
  TimeScale,
  Filler,
  zoomPlugin
);

interface LineChartWidgetProps {
  title: string;
  data: any[];
  lines: {
    dataKey: string;
    stroke: string;
    name: string;
  }[];
  height?: number;
  onRefresh?: () => void;
  onExport?: () => void;
  withBorder?: boolean;
  withShadow?: boolean;
  minimal?: boolean;
}

const LineChartWidget: React.FC<LineChartWidgetProps> = ({
  title,
  data,
  lines,
  height = 300,
  onRefresh,
  onExport,
  withBorder = true,
  withShadow = true,
  minimal = false,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const chartRef = useRef<ChartJS<'line'>>(null);

  // Consistent tooltip styling to match header row tooltips
  const tooltipStyles = {
    tooltip: {
      fontSize: '11px',
      padding: '4px 8px',
      fontWeight: 400
    }
  };

  // Prepare data for Chart.js
  const chartData = {
    labels: data.map(d => d.name || d.time || d.label),
    datasets: lines.map((line, index) => ({
      label: line.name,
      data: data.map(d => d[line.dataKey]),
      borderColor: line.stroke,
      backgroundColor: line.stroke + '20', // Add transparency
      borderWidth: 2,
      fill: false,
      tension: 0.4,
      pointRadius: 4,
      pointHoverRadius: 6,
    })),
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
      },
      zoom: {
        zoom: {
          wheel: {
            enabled: true,
          },
          pinch: {
            enabled: true,
          },
          mode: 'x' as const,
        },
        pan: {
          enabled: true,
          mode: 'x' as const,
        },
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      y: {
        display: true,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false,
    },
  };

  if (minimal) {
    return (
      <div style={{ height: '100%', padding: '16px' }}>
        <Group justify="space-between" mb="md">
          <Text size="lg" fw={600}>
            {title}
          </Text>
          <Group gap="xs">
            {onRefresh && (
              <Tooltip label="Refresh chart data" styles={tooltipStyles}>
                <ActionIcon variant="light" size="sm" onClick={onRefresh}>
                  <IconRefresh size={16} />
                </ActionIcon>
              </Tooltip>
            )}
            {onExport && (
              <Tooltip label="Export chart data" styles={tooltipStyles}>
                <ActionIcon variant="light" size="sm" onClick={onExport}>
                  <IconDownload size={16} />
                </ActionIcon>
              </Tooltip>
            )}
          </Group>
        </Group>

        <div style={{ height: `${height}px`, width: '100%' }}>
          <Line ref={chartRef} data={chartData} options={options} />
        </div>
      </div>
    );
  }

  return (
    <Card
      shadow={withShadow ? "sm" : undefined}
      padding="lg"
      radius="md"
      withBorder={withBorder}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        transition: 'all 0.2s ease',
        transform: isHovered ? 'translateY(-2px)' : 'translateY(0)',
        height: '100%',
      }}
    >
      <Group justify="space-between" mb="md">
        <Text size="lg" fw={600}>
          {title}
        </Text>
        <Group gap="xs">
          {onRefresh && (
            <Tooltip label="Refresh chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onRefresh}>
                <IconRefresh size={16} />
              </ActionIcon>
            </Tooltip>
          )}
          {onExport && (
            <Tooltip label="Export chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onExport}>
                <IconDownload size={16} />
              </ActionIcon>
            </Tooltip>
          )}
        </Group>
      </Group>

      <div style={{ height: `${height}px`, width: '100%' }}>
        <Line ref={chartRef} data={chartData} options={options} />
      </div>
    </Card>
  );
};

export default LineChartWidget;
