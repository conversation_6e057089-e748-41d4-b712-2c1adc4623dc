import { format } from 'date-fns'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import { useState, useEffect, KeyboardEvent } from 'react'

interface CalendarHeaderProps {
  currentDate: Date
  onPrevMonth: () => void
  onNextMonth: () => void
  view?: 'day' | 'workweek' | 'week' | 'month' | 'year'
  onYearChange?: (year: number) => void
}

export function CalendarHeader({ 
  currentDate, 
  onPrevMonth, 
  onNextMonth, 
  view = 'month',
  onYearChange 
}: CalendarHeaderProps) {
  const [yearInput, setYearInput] = useState(currentDate.getFullYear().toString())
  const [isEditing, setIsEditing] = useState(false)

  useEffect(() => {
    setYearInput(currentDate.getFullYear().toString())
  }, [currentDate])

  const handleYearKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const targetYear = parseInt(yearInput)
      if (!isNaN(targetYear) && targetYear >= 1900 && targetYear <= 2100) {
        onYearChange?.(targetYear)
        setIsEditing(false)
      }
    } else if (e.key === 'Escape') {
      setYearInput(currentDate.getFullYear().toString())
      setIsEditing(false)
    }
  }

  return (
    <div className="flex items-center justify-between p-4 bg-[var(--header-bg)] dark:bg-[#1F2937] dark:text-white">
      <div className="flex items-center space-x-2">
        <button 
          onClick={onPrevMonth} 
          className="p-1 hover:bg-[var(--primary-color)]/5 dark:hover:bg-[var(--primary-color)]/20 rounded transition-colors"
        >
          <ChevronLeftIcon className="h-5 w-5" />
        </button>
        <h2 className="text-lg font-semibold flex items-center space-x-2">
          {view === 'year' ? (
            isEditing ? (
              <input
                type="text"
                value={yearInput}
                onChange={(e) => setYearInput(e.target.value)}
                onKeyDown={handleYearKeyDown}
                onBlur={() => {
                  setYearInput(currentDate.getFullYear().toString())
                  setIsEditing(false)
                }}
                className="w-20 bg-transparent focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] rounded px-2 text-center"
                autoFocus
              />
            ) : (
              <span 
                onClick={() => setIsEditing(true)}
                className="cursor-pointer hover:text-[var(--primary-color)] dark:hover:text-[var(--primary-color)] transition-colors"
              >
                {format(currentDate, 'yyyy')}
              </span>
            )
          ) : (
            format(currentDate, 'MMMM yyyy')
          )}
        </h2>
        <button 
          onClick={onNextMonth} 
          className="p-1 hover:bg-[var(--primary-color)]/5 dark:hover:bg-[var(--primary-color)]/20 rounded transition-colors"
        >
          <ChevronRightIcon className="h-5 w-5" />
        </button>
      </div>
      <div className="flex items-center space-x-2 text-sm">
        <span>Today: {format(new Date(), "MMM d, yyyy")}</span>
      </div>
    </div>
  )
}