import { useState, useRef } from 'react';
import { Card, Text, Group, ActionIcon, Tooltip, Stack } from '@mantine/core';
import { IconRefresh, IconDownload } from '@tabler/icons-react';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip as ChartTooltip,
  Legend,
} from 'chart.js';
import { Pie } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  ArcElement,
  ChartTooltip,
  Legend
);

interface PieChartWidgetProps {
  title: string;
  data: any[];
  colors: string[];
  height?: number;
  onRefresh?: () => void;
  onExport?: () => void;
  withBorder?: boolean;
  withShadow?: boolean;
  minimal?: boolean;
}

const PieChartWidget = ({
  title,
  data,
  colors,
  height = 300,
  onRefresh,
  onExport,
  withBorder = true,
  withShadow = true,
  minimal = false
}: PieChartWidgetProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const chartRef = useRef<ChartJS<'pie'>>(null);

  // Consistent tooltip styling to match header row tooltips
  const tooltipStyles = {
    tooltip: {
      fontSize: '11px',
      padding: '4px 8px',
      fontWeight: 400
    }
  };

  // Prepare data for Chart.js
  const chartData = {
    labels: data.map(d => d.name || d.label),
    datasets: [
      {
        data: data.map(d => d.value),
        backgroundColor: colors,
        borderColor: '#ffffff',
        borderWidth: 2,
        hoverBorderWidth: 3,
        hoverBackgroundColor: colors,
        hoverBorderColor: '#ffffff',
      },
    ],
  };

  // Calculate total for percentages
  const total = data.reduce((sum, item) => sum + item.value, 0);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false, // We'll use custom side legend
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed;
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      },
    },
  };

  if (minimal) {
    return (
      <div style={{ height: '100%', padding: '16px' }}>
        <Group justify="space-between" mb="md">
          <Text size="lg" fw={600}>
            {title}
          </Text>
          <Group gap="xs">
            {onRefresh && (
              <Tooltip label="Refresh chart data" styles={tooltipStyles}>
                <ActionIcon variant="light" size="sm" onClick={onRefresh}>
                  <IconRefresh size={16} />
                </ActionIcon>
              </Tooltip>
            )}
            {onExport && (
              <Tooltip label="Export chart data" styles={tooltipStyles}>
                <ActionIcon variant="light" size="sm" onClick={onExport}>
                  <IconDownload size={16} />
                </ActionIcon>
              </Tooltip>
            )}
          </Group>
        </Group>

        <Group align="flex-start" gap="md" style={{ height: `${height}px`, width: '100%' }}>
          {/* Pie Chart */}
          <div style={{ flex: 1, height: '100%', minWidth: 0 }}>
            <Pie ref={chartRef} data={chartData} options={options} />
          </div>

          {/* Side Legend */}
          <Stack gap={6} style={{ minWidth: 120, maxWidth: 120 }}>
            {data.map((item, index) => (
              <Group key={index} gap="xs" wrap="nowrap">
                <div
                  style={{
                    width: 10,
                    height: 10,
                    borderRadius: '50%',
                    backgroundColor: colors[index % colors.length],
                    flexShrink: 0
                  }}
                />
                <div style={{ minWidth: 0, flex: 1 }}>
                  <Text size="xs" fw={500} truncate>{item.name || item.label}</Text>
                  <Text size="xs" c="dimmed">
                    {item.value} ({total > 0 ? Math.round((item.value / total) * 100) : 0}%)
                  </Text>
                </div>
              </Group>
            ))}
          </Stack>
        </Group>
      </div>
    );
  }

  return (
    <Card
      shadow={withShadow ? "sm" : "none"}
      padding="lg"
      radius="md"
      withBorder={withBorder}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        transition: 'all 0.2s ease',
        transform: isHovered ? 'translateY(-2px)' : 'translateY(0)',
        height: '100%',
      }}
    >
      <Group justify="space-between" mb="md">
        <Text size="lg" fw={600}>
          {title}
        </Text>
        <Group gap="xs">
          {onRefresh && (
            <Tooltip label="Refresh chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onRefresh}>
                <IconRefresh size={16} />
              </ActionIcon>
            </Tooltip>
          )}
          {onExport && (
            <Tooltip label="Export chart data" styles={tooltipStyles}>
              <ActionIcon variant="light" size="sm" onClick={onExport}>
                <IconDownload size={16} />
              </ActionIcon>
            </Tooltip>
          )}
        </Group>
      </Group>

      <Group align="flex-start" gap="md" style={{ height: `${height}px`, width: '100%' }}>
        {/* Pie Chart */}
        <div style={{ flex: 1, height: '100%', minWidth: 0 }}>
          <Pie ref={chartRef} data={chartData} options={options} />
        </div>

        {/* Side Legend */}
        <Stack gap={6} style={{ minWidth: 120, maxWidth: 120 }}>
          {data.map((item, index) => (
            <Group key={index} gap="xs" wrap="nowrap">
              <div
                style={{
                  width: 10,
                  height: 10,
                  borderRadius: '50%',
                  backgroundColor: colors[index % colors.length],
                  flexShrink: 0
                }}
              />
              <div style={{ minWidth: 0, flex: 1 }}>
                <Text size="xs" fw={500} truncate>{item.name || item.label}</Text>
                <Text size="xs" c="dimmed">
                  {item.value} ({total > 0 ? Math.round((item.value / total) * 100) : 0}%)
                </Text>
              </div>
            </Group>
          ))}
        </Stack>
      </Group>
    </Card>
  );
};

export default PieChartWidget;
