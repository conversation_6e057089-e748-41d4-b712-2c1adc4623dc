import { useState, useEffect, useCallback } from "react";
import { Text, Card, Group, Badge, Progress } from "@mantine/core";
import { IconServer, IconCpu, IconNetwork } from "@tabler/icons-react";
import DashboardLayout from "../layouts/DashboardLayout";
import WidgetMenu from "../components/WidgetMenu";
import { useLayoutManager } from "../hooks/useLayoutManager";
import { GenericCardWidget } from '../components/widgets/GenericCardWidget';
import { GenericTableWidget } from '../components/widgets/GenericTableWidget';
import { DashboardWidget } from '../types/dashboard';
import { Responsive, WidthProvider } from 'react-grid-layout';

const ResponsiveGridLayout = WidthProvider(Responsive);

// Server widgets
const serverWidgets: DashboardWidget[] = [
  { id: 'server-overview', title: 'Server Overview', type: 'generic-card' },
  { id: 'server-performance', title: 'Performance Metrics', type: 'generic-card' },
  { id: 'server-status', title: 'Server Status', type: 'generic-card' },
  { id: 'server-list', title: 'Server List', type: 'generic-table' }
];

// Server layouts
const serverLayouts = {
  lg: [
    { i: 'server-overview', x: 0, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
    { i: 'server-performance', x: 3, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
    { i: 'server-status', x: 6, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
    { i: 'server-list', x: 0, y: 3, w: 9, h: 5, minW: 6, minH: 4 }
  ],
  md: [
    { i: 'server-overview', x: 0, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
    { i: 'server-performance', x: 3, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
    { i: 'server-status', x: 6, y: 0, w: 4, h: 3, minW: 2, minH: 2 },
    { i: 'server-list', x: 0, y: 3, w: 10, h: 5, minW: 8, minH: 4 }
  ],
  sm: [
    { i: 'server-overview', x: 0, y: 0, w: 6, h: 3, minW: 6, minH: 2 },
    { i: 'server-performance', x: 0, y: 3, w: 6, h: 3, minW: 6, minH: 2 },
    { i: 'server-status', x: 0, y: 6, w: 6, h: 3, minW: 6, minH: 2 },
    { i: 'server-list', x: 0, y: 9, w: 6, h: 5, minW: 6, minH: 4 }
  ]
};

// Sample server data
const serverData = [
  { id: 1, name: 'Web Server 01', status: 'Online', cpu: '45%', memory: '67%', uptime: '15 days' },
  { id: 2, name: 'Database Server', status: 'Online', cpu: '23%', memory: '89%', uptime: '32 days' },
  { id: 3, name: 'API Server', status: 'Warning', cpu: '78%', memory: '45%', uptime: '8 days' },
  { id: 4, name: 'File Server', status: 'Offline', cpu: '0%', memory: '0%', uptime: '0 days' }
];

const getInitialMenuPosition = () => ({
  x: Math.max(50, window.innerWidth - 370),
  y: 100
});

const Servers = () => {
  // Layout management with multiple presets
  const {
    layouts,
    layoutPresets,
    currentLayoutId,
    switchToLayout,
    saveCurrentLayout,
    deleteLayout,
    resetToDefault,
    handleLayoutChange
  } = useLayoutManager({
    pageKey: 'servers',
    defaultLayouts: serverLayouts,
    defaultLayoutName: 'Default Servers Layout'
  });

  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [widgets] = useState(serverWidgets);
  const [visibleWidgets, setVisibleWidgets] = useState(new Set(serverWidgets.map(w => w.id)));
  const [showWidgetMenu, setShowWidgetMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState(getInitialMenuPosition());
  const [isDragging, setIsDragging] = useState(false);

  // Load saved widget visibility
  useEffect(() => {
    try {
      const savedVisibility = localStorage.getItem('servers-visible-widgets');
      if (savedVisibility) {
        setVisibleWidgets(new Set(JSON.parse(savedVisibility)));
      }
    } catch (error) {
      console.error('Error loading widget visibility:', error);
    }
  }, []);

  const toggleWidgetVisibility = (widgetId: string) => {
    setVisibleWidgets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(widgetId)) {
        newSet.delete(widgetId);
      } else {
        newSet.add(widgetId);
      }
      localStorage.setItem('servers-visible-widgets', JSON.stringify([...newSet]));
      return newSet;
    });
  };

  const closeWidgetMenu = () => {
    setShowWidgetMenu(false);
  };

  const resetToDefaultAndClose = () => {
    resetToDefault();
    setShowWidgetMenu(false);
    setMenuPosition(getInitialMenuPosition());
  };

  // Optimized drag handlers for floating menu
  const handleMouseDown = (e: React.MouseEvent) => {
    // Prevent dragging when clicking on interactive elements
    const target = e.target as HTMLElement;
    if (target.tagName === 'BUTTON' || target.tagName === 'INPUT' || target.tagName === 'SELECT' ||
        target.closest('button') || target.closest('[role="button"]') || target.closest('input') || target.closest('select')) {
      return;
    }

    setIsDragging(true);
    const startX = e.clientX - menuPosition.x;
    const startY = e.clientY - menuPosition.y;

    const handleMouseMove = (e: MouseEvent) => {
      const newX = Math.max(0, Math.min(window.innerWidth - 300, e.clientX - startX));
      const newY = Math.max(0, Math.min(window.innerHeight - 200, e.clientY - startY));

      setMenuPosition({ x: newX, y: newY });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1000);
  }, []);

  // Widget render function
  const renderWidget = (widget: DashboardWidget) => {
    switch (widget.type) {
      case 'generic-card':
        if (widget.id === 'server-overview') {
          return (
            <GenericCardWidget
              widget={widget}
              title="Server Overview"
              value={serverData.length}
              icon={<IconServer size={20} />}
              color="blue"
              description="Total servers in infrastructure"
            >
              <Group justify="space-around" mt="md">
                <div style={{ textAlign: 'center' }}>
                  <Text fw={700} size="lg" c="green">{serverData.filter(s => s.status === 'Online').length}</Text>
                  <Text size="xs" c="dimmed">Online</Text>
                </div>
                <div style={{ textAlign: 'center' }}>
                  <Text fw={700} size="lg" c="orange">{serverData.filter(s => s.status === 'Warning').length}</Text>
                  <Text size="xs" c="dimmed">Warning</Text>
                </div>
                <div style={{ textAlign: 'center' }}>
                  <Text fw={700} size="lg" c="red">{serverData.filter(s => s.status === 'Offline').length}</Text>
                  <Text size="xs" c="dimmed">Offline</Text>
                </div>
              </Group>
            </GenericCardWidget>
          );
        }

        if (widget.id === 'server-performance') {
          const avgCpu = Math.round(serverData.reduce((acc, server) => acc + parseInt(server.cpu), 0) / serverData.length);
          const avgMemory = Math.round(serverData.reduce((acc, server) => acc + parseInt(server.memory), 0) / serverData.length);

          return (
            <GenericCardWidget
              widget={widget}
              title="Performance Metrics"
              icon={<IconCpu size={20} />}
              color="orange"
              description="Average resource utilization"
            >
              <div style={{ marginTop: '16px' }}>
                <Group justify="space-between" mb="xs">
                  <Text size="sm">CPU Usage</Text>
                  <Text size="sm" fw={500}>{avgCpu}%</Text>
                </Group>
                <Progress value={avgCpu} color={avgCpu > 70 ? 'red' : avgCpu > 50 ? 'orange' : 'green'} mb="md" />

                <Group justify="space-between" mb="xs">
                  <Text size="sm">Memory Usage</Text>
                  <Text size="sm" fw={500}>{avgMemory}%</Text>
                </Group>
                <Progress value={avgMemory} color={avgMemory > 80 ? 'red' : avgMemory > 60 ? 'orange' : 'green'} />
              </div>
            </GenericCardWidget>
          );
        }

        if (widget.id === 'server-status') {
          return (
            <GenericCardWidget
              widget={widget}
              title="Network Status"
              icon={<IconNetwork size={20} />}
              color="green"
              description="Network connectivity status"
            >
              <div style={{ marginTop: '16px' }}>
                <Group justify="space-between" mb="sm">
                  <Text size="sm">Network Latency</Text>
                  <Badge color="green" size="sm">12ms</Badge>
                </Group>
                <Group justify="space-between" mb="sm">
                  <Text size="sm">Bandwidth Usage</Text>
                  <Badge color="blue" size="sm">45%</Badge>
                </Group>
                <Group justify="space-between" mb="sm">
                  <Text size="sm">Active Connections</Text>
                  <Badge color="cyan" size="sm">1,247</Badge>
                </Group>
              </div>
            </GenericCardWidget>
          );
        }
        break;

      case 'generic-table':
        if (widget.id === 'server-list') {
          const columns = [
            { key: 'name', label: 'Server Name' },
            {
              key: 'status',
              label: 'Status',
              render: (value: string) => (
                <Badge
                  color={value === 'Online' ? 'green' : value === 'Warning' ? 'orange' : 'red'}
                  size="sm"
                >
                  {value}
                </Badge>
              )
            },
            { key: 'cpu', label: 'CPU' },
            { key: 'memory', label: 'Memory' },
            { key: 'uptime', label: 'Uptime' }
          ];

          return (
            <GenericTableWidget
              widget={widget}
              title="Server List"
              columns={columns}
              data={serverData}
              maxHeight={400}
            />
          );
        }
        break;

      default:
        return (
          <Card h="100%" p="md">
            <Text c="dimmed" ta="center">
              Widget: {widget.type}
            </Text>
          </Card>
        );
    }
  };
  const allWidgets = widgets.map(widget => ({
    ...widget,
    isVisible: visibleWidgets.has(widget.id)
  }));

  const dashboardControls = {
    isEditing,
    loading,
    showWidgetMenu,
    onToggleEdit: () => setIsEditing(!isEditing),
    onRefresh: handleRefresh,
    onResetLayout: resetToDefaultAndClose,
    onToggleWidgetMenu: () => {
      if (!showWidgetMenu) {
        setMenuPosition(getInitialMenuPosition());
      }
      setShowWidgetMenu(!showWidgetMenu);
    }
  };

  return (
    <DashboardLayout dashboardControls={dashboardControls}>
      <div style={{ width: '100%', position: 'relative', maxWidth: '1400px', margin: '0 auto' }}>

        {/* Dashboard Grid */}
        <div style={{ flex: 1, overflow: 'auto', paddingTop: '40px' }}>
          <ResponsiveGridLayout
            className={`layout ${isEditing ? 'editing' : ''}`}
            layouts={layouts}
            onLayoutChange={(layout, layouts) => {
              if (isEditing) {
                handleLayoutChange(layout, layouts);
              }
            }}
            breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
            cols={{ lg: 9, md: 10, sm: 6, xs: 4, xxs: 2 }}
            rowHeight={60}
            isDraggable={isEditing}
            isResizable={isEditing}
            margin={[16, 16]}
            containerPadding={[16, 16]}
            useCSSTransforms={true}
            compactType="vertical"
            preventCollision={false}
            allowOverlap={false}
          >
            {allWidgets.map((widget) => {
              const isVisible = visibleWidgets.has(widget.id);
              return (
                <div
                  key={widget.id}
                  className="widget-container"
                  style={{
                    display: isVisible ? 'block' : 'none'
                  }}
                >
                  <Card
                    h="100%"
                    p={0}
                    style={{
                      position: 'relative',
                      overflow: 'hidden',
                      border: isEditing ? '2px solid var(--mantine-color-blue-4)' : '1px solid var(--mantine-color-gray-3)',
                      borderRadius: '8px',
                      backgroundColor: 'var(--mantine-color-body)'
                    }}
                  >
                    {renderWidget(widget)}
                  </Card>
                </div>
              );
            })}
          </ResponsiveGridLayout>
        </div>

        {/* Floating Widget Selection Menu */}
        {showWidgetMenu && (
          <WidgetMenu
            widgets={widgets}
            visibleWidgets={visibleWidgets}
            onToggleWidgetVisibility={toggleWidgetVisibility}
            layoutPresets={layoutPresets}
            currentLayoutId={currentLayoutId}
            onLayoutChange={switchToLayout}
            onSaveLayout={saveCurrentLayout}
            onDeleteLayout={deleteLayout}
            onResetToDefault={resetToDefaultAndClose}
            menuPosition={menuPosition}
            isDragging={isDragging}
            onMouseDown={handleMouseDown}
            onClose={closeWidgetMenu}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default Servers;
