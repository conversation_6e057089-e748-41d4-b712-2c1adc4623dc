import React, { useEffect, useRef, useState } from 'react';
import { Card, Text, Group, Select, Button, Badge } from '@mantine/core';
import { IconRefresh } from '@tabler/icons-react';
import * as d3 from 'd3';

interface DataPoint {
  name: string;
  time: string;
  value: number;
  cpu?: number;
  memory?: number;
  disk?: number;
}

interface D3ZoomEnhancedProps {
  title: string;
  height?: number;
}

// Generate realistic network traffic data
const generateTimeSeriesData = (timePeriod: string, dataInterval: string) => {
  const data = [];
  const now = new Date();
  const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
  
  let totalPoints = 0;
  let intervalMinutes = 0;
  
  // Configure based on time period
  switch (timePeriod) {
    case '15mins':
      totalPoints = 15;
      intervalMinutes = 1;
      break;
    case '30mins':
      totalPoints = 30;
      intervalMinutes = 1;
      break;
    case '1hr':
      totalPoints = dataInterval === '15min' ? 4 : dataInterval === '5min' ? 12 : 60;
      intervalMinutes = dataInterval === '15min' ? 15 : dataInterval === '5min' ? 5 : 1;
      break;
    case '6hrs':
      totalPoints = dataInterval === '15min' ? 24 : dataInterval === '5min' ? 72 : 360;
      intervalMinutes = dataInterval === '15min' ? 15 : dataInterval === '5min' ? 5 : 1;
      break;
    case '24hrs':
      totalPoints = dataInterval === '15min' ? 96 : dataInterval === '5min' ? 288 : 1440;
      intervalMinutes = dataInterval === '15min' ? 15 : dataInterval === '5min' ? 5 : 1;
      break;
    case 'today':
      totalPoints = dataInterval === '15min' ? 96 : dataInterval === '5min' ? 288 : 1440;
      intervalMinutes = dataInterval === '15min' ? 15 : dataInterval === '5min' ? 5 : 1;
      break;
    case 'yesterday':
      totalPoints = dataInterval === '15min' ? 96 : dataInterval === '5min' ? 288 : 1440;
      intervalMinutes = dataInterval === '15min' ? 15 : dataInterval === '5min' ? 5 : 1;
      break;
    case '7days':
      totalPoints = dataInterval === '1hr' ? 168 : dataInterval === '15min' ? 672 : 2016;
      intervalMinutes = dataInterval === '1hr' ? 60 : dataInterval === '15min' ? 15 : 5;
      break;
    case '30days':
      totalPoints = dataInterval === '1hr' ? 720 : dataInterval === '4hr' ? 180 : 1440;
      intervalMinutes = dataInterval === '1hr' ? 60 : dataInterval === '4hr' ? 240 : 30;
      break;
    case '90days':
      totalPoints = dataInterval === '4hr' ? 540 : dataInterval === '1day' ? 90 : 2160;
      intervalMinutes = dataInterval === '4hr' ? 240 : dataInterval === '1day' ? 1440 : 60;
      break;
    default:
      totalPoints = 96;
      intervalMinutes = 15;
  }

  for (let i = 0; i < totalPoints; i++) {
    const time = new Date(startOfDay.getTime() + i * intervalMinutes * 60 * 1000);
    
    // Create realistic traffic patterns
    const hour = time.getHours();
    let baseTraffic = 30; // Base traffic level
    
    // Business hours pattern (8 AM - 6 PM)
    if (hour >= 8 && hour <= 18) {
      baseTraffic = 60 + Math.sin((hour - 8) * Math.PI / 10) * 20;
    }
    // Evening pattern (6 PM - 11 PM)
    else if (hour >= 19 && hour <= 23) {
      baseTraffic = 45 + Math.sin((hour - 19) * Math.PI / 4) * 15;
    }
    // Night pattern (11 PM - 8 AM)
    else {
      baseTraffic = 20 + Math.random() * 10;
    }
    
    // Add some randomness and occasional spikes
    const noise = Math.random() * 15 - 7.5;
    const spike = Math.random() < 0.05 ? Math.random() * 30 : 0; // 5% chance of spike
    
    const value = Math.max(5, Math.min(100, baseTraffic + noise + spike));
    
    data.push({
      name: time.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: intervalMinutes >= 60 ? false : true 
      }),
      time: time.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: intervalMinutes >= 60 ? false : true 
      }),
      value: value,
      cpu: Math.max(10, Math.min(90, value * 0.8 + Math.random() * 10)),
      memory: Math.max(20, Math.min(85, value * 0.6 + Math.random() * 15)),
      disk: Math.max(15, Math.min(75, 40 + Math.random() * 20)),
    });
  }
  
  return data;
};

const D3ZoomEnhanced: React.FC<D3ZoomEnhancedProps> = ({ title, height = 400 }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [timePeriod, setTimePeriod] = useState('1hr');
  const [dataInterval, setDataInterval] = useState('15min');
  const [data, setData] = useState<DataPoint[]>([]);

  // Time period options like OpManager
  const timePeriodOptions = [
    { value: '15mins', label: '15 Mins' },
    { value: '30mins', label: '30 Mins' },
    { value: '1hr', label: '1 Hr' },
    { value: '6hrs', label: '6 Hrs' },
    { value: '24hrs', label: '24 Hrs' },
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: '7days', label: '7 Days' },
    { value: '30days', label: '30 Days' },
    { value: '90days', label: '90 Days' },
  ];

  // Data interval options
  const dataIntervalOptions = [
    { value: '1min', label: '1 Minute' },
    { value: '5min', label: '5 Minutes' },
    { value: '15min', label: '15 Minutes' },
    { value: '1hr', label: '1 Hour' },
    { value: '4hr', label: '4 Hours' },
    { value: '1day', label: '1 Day' },
  ];

  // Generate data when period or interval changes
  useEffect(() => {
    const newData = generateTimeSeriesData(timePeriod, dataInterval);
    setData(newData);
  }, [timePeriod, dataInterval]);

  const refreshData = () => {
    const newData = generateTimeSeriesData(timePeriod, dataInterval);
    setData(newData);
  };

  // Filter available data interval options based on time period
  const getAvailableIntervals = (period: string) => {
    switch (period) {
      case '15mins':
      case '30mins':
        return dataIntervalOptions.filter(opt => ['1min', '5min'].includes(opt.value));
      case '1hr':
      case '6hrs':
        return dataIntervalOptions.filter(opt => ['1min', '5min', '15min'].includes(opt.value));
      case '24hrs':
      case 'today':
      case 'yesterday':
        return dataIntervalOptions.filter(opt => ['5min', '15min', '1hr'].includes(opt.value));
      case '7days':
        return dataIntervalOptions.filter(opt => ['15min', '1hr', '4hr'].includes(opt.value));
      case '30days':
      case '90days':
        return dataIntervalOptions.filter(opt => ['1hr', '4hr', '1day'].includes(opt.value));
      default:
        return dataIntervalOptions;
    }
  };

  // Handle time period change - keep current interval if valid, otherwise use default
  const handleTimePeriodChange = (newPeriod: string) => {
    const availableIntervals = getAvailableIntervals(newPeriod);
    const currentIntervalValid = availableIntervals.some(opt => opt.value === dataInterval);

    setTimePeriod(newPeriod);

    // Only change interval if current one is not valid for new period
    if (!currentIntervalValid) {
      setDataInterval(availableIntervals[0]?.value || '15min');
    }
  };

  useEffect(() => {
    if (!containerRef.current || !data.length) return;

    // Clear previous chart
    d3.select(containerRef.current).selectAll('*').remove();

    const margin = { top: 20, right: 80, bottom: 40, left: 60 };
    const width = (containerRef.current?.clientWidth || 800) - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom - 100;

    // Process data
    let currentData = data.map((d, i) => ({
      ...d,
      index: i,
      timeLabel: d.time,
    }));

    // Create SVG
    const svg = d3.select(containerRef.current)
      .append('svg')
      .attr('width', width + margin.left + margin.right)
      .attr('height', height)
      .style('background', 'var(--mantine-color-body)');

    const g = svg.append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Scales
    let currentXScale = d3.scaleLinear()
      .domain([0, currentData.length - 1])
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(currentData, d => d.value) || 100])
      .range([chartHeight, 0]);

    // Line generator
    const line = d3.line<any>()
      .x((d, i) => currentXScale(i))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Add axes
    g.append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0,${chartHeight})`)
      .call(d3.axisBottom(currentXScale)
        .tickFormat((d) => currentData[d as number]?.timeLabel || '')
        .ticks(Math.min(10, currentData.length))
      );

    g.append('g')
      .attr('class', 'y-axis')
      .call(d3.axisLeft(yScale));

    // Add main line
    g.append('path')
      .attr('class', 'main-line')
      .datum(currentData)
      .attr('fill', 'none')
      .attr('stroke', '#3b82f6')
      .attr('stroke-width', 3)
      .attr('d', line);

    // Add dots
    g.selectAll('.dot')
      .data(currentData)
      .enter()
      .append('circle')
      .attr('class', 'dot')
      .attr('r', 3)
      .attr('fill', '#3b82f6')
      .attr('stroke', '#ffffff')
      .attr('stroke-width', 1)
      .attr('cx', (d, i) => currentXScale(i))
      .attr('cy', d => yScale(d.value));

    // Add critical points (red diamonds)
    const anomalies = currentData.filter(d => d.value > 85);
    anomalies.forEach(d => {
      g.append('path')
        .attr('class', 'diamond')
        .attr('d', `M ${currentXScale(d.index)} ${yScale(d.value) - 8}
                   L ${currentXScale(d.index) + 6} ${yScale(d.value)}
                   L ${currentXScale(d.index)} ${yScale(d.value) + 8}
                   L ${currentXScale(d.index) - 6} ${yScale(d.value)} Z`)
        .attr('fill', '#ef4444')
        .attr('stroke', '#ffffff')
        .attr('stroke-width', 2);
    });

    // Add brush for zoom
    const brush = d3.brushX()
      .extent([[0, 0], [width, chartHeight]])
      .on('end', (event) => {
        if (!event.selection) return;

        const [x0, x1] = event.selection;
        const startIndex = Math.max(0, Math.round(currentXScale.invert(x0)));
        const endIndex = Math.min(currentData.length - 1, Math.round(currentXScale.invert(x1)));

        if (startIndex >= endIndex) return;

        // Create zoomed data with new indices
        const zoomedData = currentData.slice(startIndex, endIndex + 1).map((d, i) => ({
          ...d,
          index: i,
          originalIndex: d.index
        }));

        // New scale
        const newXScale = d3.scaleLinear()
          .domain([0, zoomedData.length - 1])
          .range([0, width]);

        // Update axis with explicit tick values
        const tickValues = zoomedData.length <= 10 
          ? d3.range(0, zoomedData.length)
          : d3.range(0, zoomedData.length, Math.ceil(zoomedData.length / 8));
        
        g.select('.x-axis')
          .transition()
          .duration(750)
          .call(d3.axisBottom(newXScale)
            .tickValues(tickValues)
            .tickFormat((d) => {
              const index = Math.round(d as number);
              return zoomedData[index]?.timeLabel || '';
            })
          );

        // Update line
        const newLine = d3.line<any>()
          .x((d, i) => newXScale(i))
          .y(d => yScale(d.value))
          .curve(d3.curveMonotoneX);

        g.select('.main-line')
          .datum(zoomedData)
          .transition()
          .duration(750)
          .attr('d', newLine);

        // Update dots
        const dotsSelection = g.selectAll('.dot').data(zoomedData);
        dotsSelection.exit().remove();
        dotsSelection
          .transition()
          .duration(750)
          .attr('cx', (d, i) => newXScale(i))
          .attr('cy', d => yScale(d.value));

        // Update diamonds - clear all old ones first
        g.selectAll('.diamond').remove();
        g.selectAll('path[fill="#ef4444"]').remove();
        
        const zoomedAnomalies = zoomedData.filter(d => d.value > 85);
        zoomedAnomalies.forEach((d, i) => {
          g.append('path')
            .attr('class', 'diamond')
            .attr('d', `M ${newXScale(i)} ${yScale(d.value) - 8}
                       L ${newXScale(i) + 6} ${yScale(d.value)}
                       L ${newXScale(i)} ${yScale(d.value) + 8}
                       L ${newXScale(i) - 6} ${yScale(d.value)} Z`)
            .attr('fill', '#ef4444')
            .attr('stroke', '#ffffff')
            .attr('stroke-width', 2)
            .attr('opacity', 0)
            .transition()
            .duration(500)
            .attr('opacity', 1);
        });

        // Update current data and scale for next zoom
        currentData = zoomedData;
        currentXScale = newXScale;

        // Clear brush
        g.select('.brush').call(brush.move, null);
      });

    g.append('g')
      .attr('class', 'brush')
      .call(brush);

  }, [data, height]);

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Group justify="space-between" mb="md">
        <Text size="lg" fw={700}>{title}</Text>
        <Group gap="sm">
          <Badge variant="light" color="blue">
            {data.length} points
          </Badge>
          <Badge variant="light" color="green">
            {timePeriod} / {dataInterval}
          </Badge>
        </Group>
      </Group>

      {/* OpManager-style Time Period Selector */}
      <Group mb="md" gap="sm">
        <Select
          label="Time Period"
          value={timePeriod}
          onChange={(value) => handleTimePeriodChange(value || '1hr')}
          data={timePeriodOptions}
          size="sm"
          style={{ minWidth: 120 }}
        />
        <Select
          label="Data Interval"
          value={dataInterval}
          onChange={(value) => setDataInterval(value || '15min')}
          data={getAvailableIntervals(timePeriod)}
          size="sm"
          style={{ minWidth: 120 }}
        />
        <Button
          leftSection={<IconRefresh size={16} />}
          onClick={refreshData}
          variant="light"
          size="sm"
        >
          Refresh
        </Button>
      </Group>

      <div ref={containerRef} style={{ width: '100%', height: `${height}px` }} />
      
      <Text size="sm" c="dimmed" mt="sm">
        Enhanced version with OpManager-style time controls. Drag to zoom, multiple zoom levels supported.
      </Text>
    </Card>
  );
};

export default D3ZoomEnhanced;
