import { useState } from 'react';
import { Title, Text, Card, Group, Stack, Tabs, Switch, Select, Button, NumberInput, TextInput, Badge, Modal, Alert, Tooltip, Divider, Table } from "@mantine/core";
import { IconSettings, IconShield, IconDatabase, IconTrash, IconRotate, IconClock, IconAlertTriangle, IconFileText, IconChartBar, IconServer, IconChartLine, IconPlus, IconEdit, IconSearch } from "@tabler/icons-react";
import DashboardLayout from "../layouts/DashboardLayout";

const Settings = () => {
  // Consistent tooltip styling to match header row tooltips
  const tooltipStyles = {
    tooltip: {
      fontSize: '11px',
      padding: '4px 8px',
      fontWeight: 400
    }
  };

  // State for settings
  const [activeTab, setActiveTab] = useState<string>('general');
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [showRotationModal, setShowRotationModal] = useState<boolean>(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState<string>('');

  // General settings state
  const [autoRefresh, setAutoRefresh] = useState<boolean>(true);
  const [refreshInterval, setRefreshInterval] = useState<number>(30);
  const [darkMode, setDarkMode] = useState<boolean>(true);
  const [notifications, setNotifications] = useState<boolean>(true);

  // Logs settings state (renamed from Admin)
  const [logRetentionDays, setLogRetentionDays] = useState<number>(90);
  const [maxLogSize, setMaxLogSize] = useState<number>(1000);
  const [autoRotation, setAutoRotation] = useState<boolean>(true);
  const [rotationFrequency, setRotationFrequency] = useState<string>('weekly');

  // Database settings state
  const [dbHost, setDbHost] = useState<string>('localhost');
  const [dbPort, setDbPort] = useState<number>(5432);
  const [dbName, setDbName] = useState<string>('awsdrs');
  const [dbUser, setDbUser] = useState<string>('postgres');
  const [dbPassword, setDbPassword] = useState<string>('');
  const [dbSslMode, setDbSslMode] = useState<string>('prefer');
  const [dbMaxConnections, setDbMaxConnections] = useState<number>(20);

  // DB Cache (Valkey) settings state
  const [cacheHost, setCacheHost] = useState<string>('localhost');
  const [cachePort, setCachePort] = useState<number>(6379);
  const [cachePassword, setCachePassword] = useState<string>('');
  const [cacheDatabase, setCacheDatabase] = useState<number>(0);
  const [cacheMaxConnections, setCacheMaxConnections] = useState<number>(10);
  const [cacheTtl, setCacheTtl] = useState<number>(3600);

  // Charts settings state
  const [defaultChartTheme, setDefaultChartTheme] = useState<string>('auto');
  const [chartAnimations, setChartAnimations] = useState<boolean>(true);
  const [chartRefreshInterval, setChartRefreshInterval] = useState<number>(30);
  const [maxDataPoints, setMaxDataPoints] = useState<number>(1000);

  // Database maintenance settings
  const [recentEventsDays, setRecentEventsDays] = useState<number>(7);
  const [detailedStatsDays, setDetailedStatsDays] = useState<number>(365);
  const [hourlyStatsDays, setHourlyStatsDays] = useState<number>(90);
  const [dailyStatsDays, setDailyStatsDays] = useState<number>(365);

  // Storage estimation constants (approximate sizes in KB)
  const STORAGE_ESTIMATES = {
    recentEvent: 2.5,      // KB per recent event record
    detailedStat: 1.8,     // KB per detailed statistics record
    hourlyStat: 0.8,       // KB per hourly statistics record
    dailyStat: 0.4,        // KB per daily statistics record
    eventsPerDay: 1440,    // Approximate events per day (1 per minute)
    detailedPerDay: 288,   // Detailed stats per day (every 5 minutes)
    hourlyPerDay: 24,      // Hourly stats per day
    dailyPerDay: 1         // Daily stats per day
  };

  // Calculate storage estimates
  const calculateStorageEstimate = (days: number) => {
    const recentEventsSize = (recentEventsDays <= days ? recentEventsDays : days) *
                            STORAGE_ESTIMATES.eventsPerDay * STORAGE_ESTIMATES.recentEvent;

    const detailedStatsSize = (detailedStatsDays <= days ? detailedStatsDays : days) *
                             STORAGE_ESTIMATES.detailedPerDay * STORAGE_ESTIMATES.detailedStat;

    const hourlyStatsSize = (hourlyStatsDays <= days ? hourlyStatsDays : days) *
                           STORAGE_ESTIMATES.hourlyPerDay * STORAGE_ESTIMATES.hourlyStat;

    const dailyStatsSize = (dailyStatsDays <= days ? dailyStatsDays : days) *
                          STORAGE_ESTIMATES.dailyPerDay * STORAGE_ESTIMATES.dailyStat;

    const totalKB = recentEventsSize + detailedStatsSize + hourlyStatsSize + dailyStatsSize;

    // Convert to appropriate units
    if (totalKB < 1024) {
      return `${Math.round(totalKB)} KB`;
    } else if (totalKB < 1024 * 1024) {
      return `${(totalKB / 1024).toFixed(1)} MB`;
    } else {
      return `${(totalKB / (1024 * 1024)).toFixed(2)} GB`;
    }
  };

  const storageEstimates = {
    week: calculateStorageEstimate(7),
    month: calculateStorageEstimate(30),
    year: calculateStorageEstimate(365)
  };

  // Data Collection metrics state
  const [searchMetric, setSearchMetric] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Mock metrics data - this will eventually come from database
  const [metrics] = useState([
    // AWS DRS Metrics
    { id: 1, name: 'replication_lag', displayName: 'Replication Lag', category: 'AWS DRS', unit: 'seconds', description: 'Time delay in data replication', dataType: 'number', enabled: true },
    { id: 2, name: 'recovery_point_objective', displayName: 'Recovery Point Objective', category: 'AWS DRS', unit: 'minutes', description: 'Maximum acceptable data loss time', dataType: 'number', enabled: true },
    { id: 3, name: 'recovery_time_objective', displayName: 'Recovery Time Objective', category: 'AWS DRS', unit: 'minutes', description: 'Maximum acceptable recovery time', dataType: 'number', enabled: true },
    { id: 4, name: 'source_server_status', displayName: 'Source Server Status', category: 'AWS DRS', unit: 'status', description: 'Current status of source servers', dataType: 'string', enabled: true },
    { id: 5, name: 'replication_status', displayName: 'Replication Status', category: 'AWS DRS', unit: 'status', description: 'Current replication status', dataType: 'string', enabled: true },

    // Network Metrics
    { id: 6, name: 'network_traffic', displayName: 'Network Traffic', category: 'Network', unit: 'mbps', description: 'Network bandwidth utilization', dataType: 'number', enabled: true },
    { id: 7, name: 'packet_loss', displayName: 'Packet Loss', category: 'Network', unit: 'percentage', description: 'Network packet loss rate', dataType: 'number', enabled: true },
    { id: 8, name: 'latency', displayName: 'Network Latency', category: 'Network', unit: 'ms', description: 'Network round-trip time', dataType: 'number', enabled: true },
    { id: 9, name: 'bandwidth_utilization', displayName: 'Bandwidth Utilization', category: 'Network', unit: 'percentage', description: 'Percentage of available bandwidth used', dataType: 'number', enabled: true },

    // System Performance
    { id: 10, name: 'cpu_usage', displayName: 'CPU Usage', category: 'System', unit: 'percentage', description: 'Processor utilization percentage', dataType: 'number', enabled: true },
    { id: 11, name: 'memory_usage', displayName: 'Memory Usage', category: 'System', unit: 'percentage', description: 'RAM utilization percentage', dataType: 'number', enabled: true },
    { id: 12, name: 'disk_usage', displayName: 'Disk Usage', category: 'System', unit: 'percentage', description: 'Storage utilization percentage', dataType: 'number', enabled: true },
    { id: 13, name: 'disk_io', displayName: 'Disk I/O', category: 'System', unit: 'iops', description: 'Disk input/output operations per second', dataType: 'number', enabled: true },

    // Application Metrics
    { id: 14, name: 'response_time', displayName: 'Response Time', category: 'Application', unit: 'ms', description: 'Application response time', dataType: 'number', enabled: true },
    { id: 15, name: 'error_rate', displayName: 'Error Rate', category: 'Application', unit: 'percentage', description: 'Application error percentage', dataType: 'number', enabled: true },
    { id: 16, name: 'throughput', displayName: 'Throughput', category: 'Application', unit: 'requests/sec', description: 'Requests processed per second', dataType: 'number', enabled: true },
    { id: 17, name: 'active_connections', displayName: 'Active Connections', category: 'Application', unit: 'count', description: 'Number of active connections', dataType: 'number', enabled: true },

    // Security Metrics
    { id: 18, name: 'failed_logins', displayName: 'Failed Login Attempts', category: 'Security', unit: 'count', description: 'Number of failed authentication attempts', dataType: 'number', enabled: true },
    { id: 19, name: 'security_events', displayName: 'Security Events', category: 'Security', unit: 'count', description: 'Number of security-related events', dataType: 'number', enabled: true },
    { id: 20, name: 'vulnerability_score', displayName: 'Vulnerability Score', category: 'Security', unit: 'score', description: 'Overall security vulnerability rating', dataType: 'number', enabled: false }
  ]);

  const metricCategories = ['all', 'AWS DRS', 'Network', 'System', 'Application', 'Security'];

  const filteredMetrics = metrics.filter(metric => {
    const matchesSearch = metric.name.toLowerCase().includes(searchMetric.toLowerCase()) ||
                         metric.displayName.toLowerCase().includes(searchMetric.toLowerCase()) ||
                         metric.description.toLowerCase().includes(searchMetric.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || metric.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Mock data for admin stats
  const [logStats] = useState({
    totalLogs: 15847,
    totalSize: '2.3 GB',
    oldestLog: '2024-01-15',
    hiddenLogs: 234,
    archivedLogs: 1205
  });

  // Admin functions
  const handlePermanentDelete = () => {
    if (deleteConfirmText === 'DELETE ALL LOGS') {
      console.log('Permanently deleting all logs...');
      setShowDeleteModal(false);
      setDeleteConfirmText('');
      // Here you would call the actual delete API
    }
  };

  const handleLogRotation = () => {
    console.log('Starting log rotation...');
    setShowRotationModal(false);
    // Here you would call the actual rotation API
  };

  const handleDeleteHiddenLogs = () => {
    console.log('Deleting hidden logs...');
    // Here you would call the API to permanently delete hidden logs
  };

  const handleDeleteArchivedLogs = () => {
    console.log('Deleting archived logs...');
    // Here you would call the API to permanently delete archived logs
  };

  return (
    <DashboardLayout>
      <div style={{ padding: '24px', maxWidth: '1400px', margin: '0 auto' }}>
        <Group justify="space-between" mb="xl">
          <div>
            <Title order={1} size="h2" mb="xs">
              <Group gap="sm">
                <IconSettings size={32} />
                System Settings
              </Group>
            </Title>
            <Text c="dimmed">Configure system preferences and administrative controls</Text>
          </div>
        </Group>

        {/* Settings Tabs */}
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'general')}>
          <Tabs.List>
            <Tooltip label="General system preferences and display settings" styles={tooltipStyles}>
              <Tabs.Tab value="general" leftSection={<IconSettings size={16} />}>
                General
              </Tabs.Tab>
            </Tooltip>
            <Tooltip label="Log management and system maintenance controls" styles={tooltipStyles}>
              <Tabs.Tab value="logs" leftSection={<IconFileText size={16} />}>
                Logs
              </Tabs.Tab>
            </Tooltip>
            <Tooltip label="PostgreSQL database configuration and settings" styles={tooltipStyles}>
              <Tabs.Tab value="database" leftSection={<IconDatabase size={16} />}>
                Database
              </Tabs.Tab>
            </Tooltip>
            <Tooltip label="Valkey cache instance configuration and settings" styles={tooltipStyles}>
              <Tabs.Tab value="dbcache" leftSection={<IconServer size={16} />}>
                DB Cache
              </Tabs.Tab>
            </Tooltip>
            <Tooltip label="Chart display settings and data retention policies" styles={tooltipStyles}>
              <Tabs.Tab value="charts" leftSection={<IconChartBar size={16} />}>
                Charts
              </Tabs.Tab>
            </Tooltip>
            <Tooltip label="Manage metrics collection and database schema" styles={tooltipStyles}>
              <Tabs.Tab value="datacollection" leftSection={<IconChartLine size={16} />}>
                Data Collection
              </Tabs.Tab>
            </Tooltip>
          </Tabs.List>

          {/* General Settings Tab */}
          <Tabs.Panel value="general" pt="xl">
            <Stack gap="lg">
              <Card shadow="sm" p="lg">
                <Title order={3} mb="md">
                  <Group gap="sm">
                    <IconSettings size={20} />
                    General Preferences
                  </Group>
                </Title>

                <Stack gap="md">
                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Auto Refresh</Text>
                      <Text size="sm" c="dimmed">Automatically refresh dashboard data</Text>
                    </div>
                    <Tooltip label="Enable automatic data refresh for real-time updates" styles={tooltipStyles}>
                      <Switch
                        checked={autoRefresh}
                        onChange={(event) => setAutoRefresh(event.currentTarget.checked)}
                      />
                    </Tooltip>
                  </Group>

                  {autoRefresh && (
                    <Group justify="space-between">
                      <div>
                        <Text fw={500}>Refresh Interval</Text>
                        <Text size="sm" c="dimmed">How often to refresh data (seconds)</Text>
                      </div>
                      <Tooltip label="Set the refresh interval in seconds" styles={tooltipStyles}>
                        <NumberInput
                          value={refreshInterval}
                          onChange={(value) => setRefreshInterval(Number(value))}
                          min={5}
                          max={300}
                          w={100}
                        />
                      </Tooltip>
                    </Group>
                  )}

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Dark Mode</Text>
                      <Text size="sm" c="dimmed">Use dark theme for the interface</Text>
                    </div>
                    <Tooltip label="Toggle between dark and light theme" styles={tooltipStyles}>
                      <Switch
                        checked={darkMode}
                        onChange={(event) => setDarkMode(event.currentTarget.checked)}
                      />
                    </Tooltip>
                  </Group>

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Notifications</Text>
                      <Text size="sm" c="dimmed">Enable system notifications</Text>
                    </div>
                    <Tooltip label="Enable browser notifications for alerts" styles={tooltipStyles}>
                      <Switch
                        checked={notifications}
                        onChange={(event) => setNotifications(event.currentTarget.checked)}
                      />
                    </Tooltip>
                  </Group>
                </Stack>
              </Card>
            </Stack>
          </Tabs.Panel>

          {/* Logs Settings Tab (renamed from Admin) */}
          <Tabs.Panel value="logs" pt="xl">
            <Stack gap="lg">
              {/* Log Statistics Card */}
              <Card shadow="sm" p="lg">
                <Title order={3} mb="md">
                  <Group gap="sm">
                    <IconDatabase size={20} />
                    Log Statistics
                  </Group>
                </Title>

                <Group gap="xl">
                  <div>
                    <Text size="xl" fw={700}>{logStats.totalLogs.toLocaleString()}</Text>
                    <Text size="sm" c="dimmed">Total Logs</Text>
                  </div>
                  <div>
                    <Text size="xl" fw={700}>{logStats.totalSize}</Text>
                    <Text size="sm" c="dimmed">Storage Used</Text>
                  </div>
                  <div>
                    <Text size="xl" fw={700}>{logStats.hiddenLogs}</Text>
                    <Text size="sm" c="dimmed">Hidden Logs</Text>
                  </div>
                  <div>
                    <Text size="xl" fw={700}>{logStats.archivedLogs}</Text>
                    <Text size="sm" c="dimmed">Archived Logs</Text>
                  </div>
                  <div>
                    <Text size="xl" fw={700}>{logStats.oldestLog}</Text>
                    <Text size="sm" c="dimmed">Oldest Log</Text>
                  </div>
                </Group>
              </Card>

              {/* Log Retention Settings */}
              <Card shadow="sm" p="lg">
                <Title order={3} mb="md">
                  <Group gap="sm">
                    <IconClock size={20} />
                    Log Retention Settings
                  </Group>
                </Title>

                <Stack gap="md">
                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Log Retention Period</Text>
                      <Text size="sm" c="dimmed">How long to keep logs before automatic deletion</Text>
                    </div>
                    <Tooltip label="Number of days to retain logs before automatic cleanup" styles={tooltipStyles}>
                      <NumberInput
                        value={logRetentionDays}
                        onChange={(value) => setLogRetentionDays(Number(value))}
                        min={1}
                        max={365}
                        suffix=" days"
                        w={120}
                      />
                    </Tooltip>
                  </Group>

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Maximum Log Size</Text>
                      <Text size="sm" c="dimmed">Maximum storage size for logs before rotation</Text>
                    </div>
                    <Tooltip label="Maximum storage size in MB before triggering log rotation" styles={tooltipStyles}>
                      <NumberInput
                        value={maxLogSize}
                        onChange={(value) => setMaxLogSize(Number(value))}
                        min={100}
                        max={10000}
                        suffix=" MB"
                        w={120}
                      />
                    </Tooltip>
                  </Group>

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Auto Rotation</Text>
                      <Text size="sm" c="dimmed">Automatically rotate logs based on schedule</Text>
                    </div>
                    <Tooltip label="Enable automatic log rotation to manage storage" styles={tooltipStyles}>
                      <Switch
                        checked={autoRotation}
                        onChange={(event) => setAutoRotation(event.currentTarget.checked)}
                      />
                    </Tooltip>
                  </Group>

                  {autoRotation && (
                    <Group justify="space-between">
                      <div>
                        <Text fw={500}>Rotation Frequency</Text>
                        <Text size="sm" c="dimmed">How often to perform log rotation</Text>
                      </div>
                      <Tooltip label="Select how frequently to rotate logs" styles={tooltipStyles}>
                        <Select
                          value={rotationFrequency}
                          onChange={(value) => setRotationFrequency(value || 'weekly')}
                          data={[
                            { value: 'daily', label: 'Daily' },
                            { value: 'weekly', label: 'Weekly' },
                            { value: 'monthly', label: 'Monthly' }
                          ]}
                          w={120}
                        />
                      </Tooltip>
                    </Group>
                  )}
                </Stack>
              </Card>

              {/* Dangerous Actions */}
              <Card shadow="sm" p="lg" style={{ borderColor: 'var(--mantine-color-red-6)', borderWidth: '1px' }}>
                <Title order={3} mb="md">
                  <Group gap="sm">
                    <IconAlertTriangle size={20} color="red" />
                    Dangerous Actions
                  </Group>
                </Title>

                <Alert icon={<IconAlertTriangle size={16} />} color="red" mb="md">
                  <Text fw={500}>Warning: These actions are permanent and cannot be undone!</Text>
                  <Text size="sm">Please ensure you have proper backups before proceeding.</Text>
                </Alert>

                <Stack gap="md">
                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Delete Hidden Logs</Text>
                      <Text size="sm" c="dimmed">Permanently delete all logs that have been hidden from view</Text>
                      <Badge size="sm" color="orange">{logStats.hiddenLogs} hidden logs</Badge>
                    </div>
                    <Tooltip label="Permanently delete all hidden logs from the database" styles={tooltipStyles}>
                      <Button
                        variant="light"
                        color="orange"
                        leftSection={<IconTrash size={16} />}
                        onClick={handleDeleteHiddenLogs}
                        disabled={logStats.hiddenLogs === 0}
                        size="sm"
                      >
                        Delete Hidden
                      </Button>
                    </Tooltip>
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Delete Archived Logs</Text>
                      <Text size="sm" c="dimmed">Permanently delete all archived logs to free up storage space</Text>
                      <Badge size="sm" color="blue">{logStats.archivedLogs} archived logs</Badge>
                    </div>
                    <Tooltip label="Permanently delete all archived logs from storage" styles={tooltipStyles}>
                      <Button
                        variant="light"
                        color="blue"
                        leftSection={<IconTrash size={16} />}
                        onClick={handleDeleteArchivedLogs}
                        disabled={logStats.archivedLogs === 0}
                        size="sm"
                      >
                        Delete Archived
                      </Button>
                    </Tooltip>
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Manual Log Rotation</Text>
                      <Text size="sm" c="dimmed">Force immediate log rotation and archival</Text>
                    </div>
                    <Tooltip label="Manually trigger log rotation to archive old logs" styles={tooltipStyles}>
                      <Button
                        variant="light"
                        color="cyan"
                        leftSection={<IconRotate size={16} />}
                        onClick={() => setShowRotationModal(true)}
                        size="sm"
                      >
                        Rotate Now
                      </Button>
                    </Tooltip>
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Delete ALL Logs</Text>
                      <Text size="sm" c="dimmed">Permanently delete ALL logs from the system</Text>
                      <Badge size="sm" color="red">EXTREME CAUTION</Badge>
                    </div>
                    <Tooltip label="WARNING: This will permanently delete ALL logs from the system" styles={tooltipStyles}>
                      <Button
                        variant="light"
                        color="red"
                        leftSection={<IconTrash size={16} />}
                        onClick={() => setShowDeleteModal(true)}
                        size="sm"
                      >
                        Delete ALL
                      </Button>
                    </Tooltip>
                  </Group>
                </Stack>
              </Card>
            </Stack>
          </Tabs.Panel>

          {/* Database Settings Tab */}
          <Tabs.Panel value="database" pt="xl">
            <Stack gap="lg">
              <Card shadow="sm" p="lg">
                <Title order={3} mb="md">
                  <Group gap="sm">
                    <IconDatabase size={20} />
                    PostgreSQL Configuration
                  </Group>
                </Title>

                <Stack gap="md">
                  <Group grow>
                    <div>
                      <Text fw={500} mb="xs">Database Host</Text>
                      <Tooltip label="PostgreSQL server hostname or IP address" styles={tooltipStyles}>
                        <TextInput
                          value={dbHost}
                          onChange={(event) => setDbHost(event.currentTarget.value)}
                          placeholder="localhost"
                        />
                      </Tooltip>
                    </div>
                    <div>
                      <Text fw={500} mb="xs">Port</Text>
                      <Tooltip label="PostgreSQL server port (default: 5432)" styles={tooltipStyles}>
                        <NumberInput
                          value={dbPort}
                          onChange={(value) => setDbPort(Number(value))}
                          min={1}
                          max={65535}
                        />
                      </Tooltip>
                    </div>
                  </Group>

                  <Group grow>
                    <div>
                      <Text fw={500} mb="xs">Database Name</Text>
                      <Tooltip label="Name of the PostgreSQL database" styles={tooltipStyles}>
                        <TextInput
                          value={dbName}
                          onChange={(event) => setDbName(event.currentTarget.value)}
                          placeholder="awsdrs"
                        />
                      </Tooltip>
                    </div>
                    <div>
                      <Text fw={500} mb="xs">Username</Text>
                      <Tooltip label="PostgreSQL username for authentication" styles={tooltipStyles}>
                        <TextInput
                          value={dbUser}
                          onChange={(event) => setDbUser(event.currentTarget.value)}
                          placeholder="postgres"
                        />
                      </Tooltip>
                    </div>
                  </Group>

                  <Group grow>
                    <div>
                      <Text fw={500} mb="xs">Password</Text>
                      <Tooltip label="PostgreSQL password for authentication" styles={tooltipStyles}>
                        <TextInput
                          type="password"
                          value={dbPassword}
                          onChange={(event) => setDbPassword(event.currentTarget.value)}
                          placeholder="Enter password"
                        />
                      </Tooltip>
                    </div>
                    <div>
                      <Text fw={500} mb="xs">SSL Mode</Text>
                      <Tooltip label="SSL connection mode for database security" styles={tooltipStyles}>
                        <Select
                          value={dbSslMode}
                          onChange={(value) => setDbSslMode(value || 'prefer')}
                          data={[
                            { value: 'disable', label: 'Disable' },
                            { value: 'prefer', label: 'Prefer' },
                            { value: 'require', label: 'Require' },
                            { value: 'verify-ca', label: 'Verify CA' },
                            { value: 'verify-full', label: 'Verify Full' }
                          ]}
                        />
                      </Tooltip>
                    </div>
                  </Group>

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Max Connections</Text>
                      <Text size="sm" c="dimmed">Maximum number of database connections in the pool</Text>
                    </div>
                    <Tooltip label="Maximum concurrent database connections" styles={tooltipStyles}>
                      <NumberInput
                        value={dbMaxConnections}
                        onChange={(value) => setDbMaxConnections(Number(value))}
                        min={1}
                        max={100}
                        w={100}
                      />
                    </Tooltip>
                  </Group>

                  <Group justify="flex-end" mt="md">
                    <Button variant="light" color="blue">
                      Test Connection
                    </Button>
                    <Button color="blue">
                      Save Configuration
                    </Button>
                  </Group>
                </Stack>
              </Card>
            </Stack>
          </Tabs.Panel>

          {/* DB Cache (Valkey) Settings Tab */}
          <Tabs.Panel value="dbcache" pt="xl">
            <Stack gap="lg">
              <Card shadow="sm" p="lg">
                <Title order={3} mb="md">
                  <Group gap="sm">
                    <IconServer size={20} />
                    Valkey Cache Configuration
                  </Group>
                </Title>

                <Stack gap="md">
                  <Group grow>
                    <div>
                      <Text fw={500} mb="xs">Cache Host</Text>
                      <Tooltip label="Valkey server hostname or IP address" styles={tooltipStyles}>
                        <TextInput
                          value={cacheHost}
                          onChange={(event) => setCacheHost(event.currentTarget.value)}
                          placeholder="localhost"
                        />
                      </Tooltip>
                    </div>
                    <div>
                      <Text fw={500} mb="xs">Port</Text>
                      <Tooltip label="Valkey server port (default: 6379)" styles={tooltipStyles}>
                        <NumberInput
                          value={cachePort}
                          onChange={(value) => setCachePort(Number(value))}
                          min={1}
                          max={65535}
                        />
                      </Tooltip>
                    </div>
                  </Group>

                  <Group grow>
                    <div>
                      <Text fw={500} mb="xs">Password</Text>
                      <Tooltip label="Valkey authentication password (optional)" styles={tooltipStyles}>
                        <TextInput
                          type="password"
                          value={cachePassword}
                          onChange={(event) => setCachePassword(event.currentTarget.value)}
                          placeholder="Enter password (optional)"
                        />
                      </Tooltip>
                    </div>
                    <div>
                      <Text fw={500} mb="xs">Database</Text>
                      <Tooltip label="Valkey database number (0-15)" styles={tooltipStyles}>
                        <NumberInput
                          value={cacheDatabase}
                          onChange={(value) => setCacheDatabase(Number(value))}
                          min={0}
                          max={15}
                        />
                      </Tooltip>
                    </div>
                  </Group>

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Max Connections</Text>
                      <Text size="sm" c="dimmed">Maximum number of cache connections in the pool</Text>
                    </div>
                    <Tooltip label="Maximum concurrent cache connections" styles={tooltipStyles}>
                      <NumberInput
                        value={cacheMaxConnections}
                        onChange={(value) => setCacheMaxConnections(Number(value))}
                        min={1}
                        max={50}
                        w={100}
                      />
                    </Tooltip>
                  </Group>

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Default TTL</Text>
                      <Text size="sm" c="dimmed">Default time-to-live for cached items (seconds)</Text>
                    </div>
                    <Tooltip label="Default cache expiration time in seconds" styles={tooltipStyles}>
                      <NumberInput
                        value={cacheTtl}
                        onChange={(value) => setCacheTtl(Number(value))}
                        min={60}
                        max={86400}
                        suffix=" sec"
                        w={120}
                      />
                    </Tooltip>
                  </Group>

                  <Group justify="flex-end" mt="md">
                    <Button variant="light" color="orange">
                      Test Connection
                    </Button>
                    <Button variant="light" color="red">
                      Flush Cache
                    </Button>
                    <Button color="orange">
                      Save Configuration
                    </Button>
                  </Group>
                </Stack>
              </Card>
            </Stack>
          </Tabs.Panel>

          {/* Charts Settings Tab */}
          <Tabs.Panel value="charts" pt="xl">
            <Stack gap="lg">
              {/* Global Chart Settings */}
              <Card shadow="sm" p="lg">
                <Title order={3} mb="md">
                  <Group gap="sm">
                    <IconChartBar size={20} />
                    Global Chart Settings
                  </Group>
                </Title>

                <Stack gap="md">
                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Default Chart Theme</Text>
                      <Text size="sm" c="dimmed">Default color theme for all charts</Text>
                    </div>
                    <Tooltip label="Select the default theme for chart displays" styles={tooltipStyles}>
                      <Select
                        value={defaultChartTheme}
                        onChange={(value) => setDefaultChartTheme(value || 'auto')}
                        data={[
                          { value: 'auto', label: 'Auto (Follow System)' },
                          { value: 'light', label: 'Light Theme' },
                          { value: 'dark', label: 'Dark Theme' },
                          { value: 'blue', label: 'Blue Theme' },
                          { value: 'green', label: 'Green Theme' }
                        ]}
                        w={180}
                      />
                    </Tooltip>
                  </Group>

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Chart Animations</Text>
                      <Text size="sm" c="dimmed">Enable smooth animations for chart transitions</Text>
                    </div>
                    <Tooltip label="Enable or disable chart animations for better performance" styles={tooltipStyles}>
                      <Switch
                        checked={chartAnimations}
                        onChange={(event) => setChartAnimations(event.currentTarget.checked)}
                      />
                    </Tooltip>
                  </Group>

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Chart Refresh Interval</Text>
                      <Text size="sm" c="dimmed">How often to refresh chart data (seconds)</Text>
                    </div>
                    <Tooltip label="Set the refresh interval for chart data updates" styles={tooltipStyles}>
                      <NumberInput
                        value={chartRefreshInterval}
                        onChange={(value) => setChartRefreshInterval(Number(value))}
                        min={5}
                        max={300}
                        suffix=" sec"
                        w={120}
                      />
                    </Tooltip>
                  </Group>

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Maximum Data Points</Text>
                      <Text size="sm" c="dimmed">Maximum number of data points to display per chart</Text>
                    </div>
                    <Tooltip label="Limit the number of data points to improve performance" styles={tooltipStyles}>
                      <NumberInput
                        value={maxDataPoints}
                        onChange={(value) => setMaxDataPoints(Number(value))}
                        min={100}
                        max={10000}
                        w={120}
                      />
                    </Tooltip>
                  </Group>
                </Stack>
              </Card>

              {/* Data Retention */}
              <Card shadow="sm" p="lg">
                <Title order={3} mb="md">
                  <Group gap="sm">
                    <IconClock size={20} />
                    Data Retention
                  </Group>
                </Title>

                <Text size="sm" c="dimmed" mb="lg">
                  Optimize performance by automatically cleaning up older data and retaining only the required data.
                </Text>

                <Stack gap="md">
                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Recent Events</Text>
                      <Text size="sm" c="dimmed">Recent Events will be maintained for the last</Text>
                    </div>
                    <Group gap="xs" align="center">
                      <Tooltip label="Number of days to retain recent event data" styles={tooltipStyles}>
                        <NumberInput
                          value={recentEventsDays}
                          onChange={(value) => setRecentEventsDays(Number(value))}
                          min={1}
                          max={30}
                          w={80}
                        />
                      </Tooltip>
                      <Text size="sm">day(s)</Text>
                    </Group>
                  </Group>

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Detailed Statistics</Text>
                      <Text size="sm" c="dimmed">Detailed statistics will be maintained for the last</Text>
                    </div>
                    <Group gap="xs" align="center">
                      <Tooltip label="Number of days to retain detailed statistical data" styles={tooltipStyles}>
                        <NumberInput
                          value={detailedStatsDays}
                          onChange={(value) => setDetailedStatsDays(Number(value))}
                          min={30}
                          max={1095}
                          w={80}
                        />
                      </Tooltip>
                      <Text size="sm">day(s)</Text>
                    </Group>
                  </Group>

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Hourly Statistics</Text>
                      <Text size="sm" c="dimmed">Hourly statistics will be maintained for the last</Text>
                    </div>
                    <Group gap="xs" align="center">
                      <Tooltip label="Number of days to retain hourly statistical data" styles={tooltipStyles}>
                        <NumberInput
                          value={hourlyStatsDays}
                          onChange={(value) => setHourlyStatsDays(Number(value))}
                          min={7}
                          max={365}
                          w={80}
                        />
                      </Tooltip>
                      <Text size="sm">day(s)</Text>
                    </Group>
                  </Group>

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Daily Statistics</Text>
                      <Text size="sm" c="dimmed">Daily statistics will be maintained for the last</Text>
                    </div>
                    <Group gap="xs" align="center">
                      <Tooltip label="Number of days to retain daily statistical data" styles={tooltipStyles}>
                        <NumberInput
                          value={dailyStatsDays}
                          onChange={(value) => setDailyStatsDays(Number(value))}
                          min={30}
                          max={1095}
                          w={80}
                        />
                      </Tooltip>
                      <Text size="sm">day(s)</Text>
                    </Group>
                  </Group>

                  <Divider my="md" />

                  {/* Storage Estimates */}
                  <div>
                    <Text fw={500} mb="sm">
                      <Group gap="sm">
                        <IconDatabase size={16} />
                        Estimated Database Storage
                      </Group>
                    </Text>
                    <Text size="sm" c="dimmed" mb="md">
                      Projected storage requirements based on current retention settings
                    </Text>

                    <Group gap="xl">
                      <Card shadow="xs" p="md" style={{ backgroundColor: 'var(--mantine-color-blue-0)', minWidth: 120 }}>
                        <Text size="lg" fw={700} c="blue" ta="center">{storageEstimates.week}</Text>
                        <Text size="sm" c="dimmed" ta="center">1 Week</Text>
                      </Card>

                      <Card shadow="xs" p="md" style={{ backgroundColor: 'var(--mantine-color-green-0)', minWidth: 120 }}>
                        <Text size="lg" fw={700} c="green" ta="center">{storageEstimates.month}</Text>
                        <Text size="sm" c="dimmed" ta="center">30 Days</Text>
                      </Card>

                      <Card shadow="xs" p="md" style={{ backgroundColor: 'var(--mantine-color-orange-0)', minWidth: 120 }}>
                        <Text size="lg" fw={700} c="orange" ta="center">{storageEstimates.year}</Text>
                        <Text size="sm" c="dimmed" ta="center">1 Year</Text>
                      </Card>
                    </Group>

                    <Text size="xs" c="dimmed" mt="sm">
                      * Estimates based on typical AWS DRS event volumes and data compression
                    </Text>
                  </div>

                  <Divider my="md" />

                  <Group justify="flex-end">
                    <Button variant="light" color="blue">
                      Preview Changes
                    </Button>
                    <Button color="blue">
                      Apply Maintenance Settings
                    </Button>
                  </Group>
                </Stack>
              </Card>
            </Stack>
          </Tabs.Panel>

          {/* Data Collection Tab */}
          <Tabs.Panel value="datacollection" pt="xl">
            <Stack gap="lg">
              {/* Implementation Notice */}
              <Alert icon={<IconAlertTriangle size={16} />} color="blue" mb="lg">
                <Text fw={500}>Future Implementation Required</Text>
                <Text size="sm">
                  This tab manages metric definitions that will require a new database table (metrics_definitions)
                  to store metric configurations, data types, and collection parameters for production use.
                </Text>
              </Alert>

              {/* Metrics Management */}
              <Card shadow="sm" p="lg">
                <Title order={3} mb="md">
                  <Group gap="sm">
                    <IconChartLine size={20} />
                    Metrics Collection Management
                  </Group>
                </Title>

                <Text size="sm" c="dimmed" mb="lg">
                  Configure and manage all metrics collected for charts and dashboard purposes.
                  Reference this list when adding new charts to use existing metrics or create new ones.
                </Text>

                {/* Search and Filter Controls */}
                <Group mb="lg" gap="md">
                  <TextInput
                    placeholder="Search metrics..."
                    value={searchMetric}
                    onChange={(event) => setSearchMetric(event.currentTarget.value)}
                    leftSection={<IconSearch size={16} />}
                    style={{ flex: 1 }}
                  />
                  <Select
                    placeholder="Filter by category"
                    value={selectedCategory}
                    onChange={(value) => setSelectedCategory(value || 'all')}
                    data={metricCategories.map(cat => ({
                      value: cat,
                      label: cat === 'all' ? 'All Categories' : cat
                    }))}
                    style={{ minWidth: 180 }}
                  />
                  <Button
                    leftSection={<IconPlus size={16} />}
                    variant="filled"
                    color="blue"
                  >
                    Add New Metric
                  </Button>
                </Group>

                {/* Metrics Summary */}
                <Group mb="lg" gap="xl">
                  <div>
                    <Text size="xl" fw={700}>{metrics.length}</Text>
                    <Text size="sm" c="dimmed">Total Metrics</Text>
                  </div>
                  <div>
                    <Text size="xl" fw={700} c="green">{metrics.filter(m => m.enabled).length}</Text>
                    <Text size="sm" c="dimmed">Active</Text>
                  </div>
                  <div>
                    <Text size="xl" fw={700} c="orange">{metrics.filter(m => !m.enabled).length}</Text>
                    <Text size="sm" c="dimmed">Disabled</Text>
                  </div>
                  <div>
                    <Text size="xl" fw={700} c="blue">{metricCategories.length - 1}</Text>
                    <Text size="sm" c="dimmed">Categories</Text>
                  </div>
                </Group>

                {/* Metrics Table */}
                <Table
                  striped
                  highlightOnHover
                  withTableBorder
                  withColumnBorders
                  style={{
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    overflow: 'hidden'
                  }}
                >
                  <Table.Thead style={{ backgroundColor: 'rgb(2, 6, 23)' }}>
                    <Table.Tr>
                      <Table.Th style={{ color: 'white', padding: '8px 12px' }}>
                        <Text c="white" size="sm" fw={600}>Metric Name</Text>
                      </Table.Th>
                      <Table.Th style={{ color: 'white', padding: '8px 12px' }}>
                        <Text c="white" size="sm" fw={600}>Display Name</Text>
                      </Table.Th>
                      <Table.Th style={{ color: 'white', padding: '8px 12px' }}>
                        <Text c="white" size="sm" fw={600}>Category</Text>
                      </Table.Th>
                      <Table.Th style={{ color: 'white', padding: '8px 12px' }}>
                        <Text c="white" size="sm" fw={600}>Unit</Text>
                      </Table.Th>
                      <Table.Th style={{ color: 'white', padding: '8px 12px' }}>
                        <Text c="white" size="sm" fw={600}>Description</Text>
                      </Table.Th>
                      <Table.Th style={{ color: 'white', padding: '8px 12px', textAlign: 'center' }}>
                        <Text c="white" size="sm" fw={600}>Status</Text>
                      </Table.Th>
                      <Table.Th style={{ color: 'white', padding: '8px 12px', textAlign: 'center' }}>
                        <Text c="white" size="sm" fw={600}>Actions</Text>
                      </Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {filteredMetrics.map((metric, index) => (
                      <Table.Tr
                        key={metric.id}
                        style={{
                          backgroundColor: index % 2 === 0 ? 'white' : 'rgba(2, 6, 23, 0.02)'
                        }}
                      >
                        <Table.Td style={{ padding: '6px 12px', verticalAlign: 'middle' }}>
                          <Text size="xs" ff="monospace" c="blue" fw={500}>{metric.name}</Text>
                        </Table.Td>
                        <Table.Td style={{ padding: '6px 12px', verticalAlign: 'middle' }}>
                          <Text size="xs" fw={600} c="dark">{metric.displayName}</Text>
                        </Table.Td>
                        <Table.Td style={{ padding: '6px 12px', verticalAlign: 'middle' }}>
                          <Badge size="xs" variant="filled" color={
                            metric.category === 'AWS DRS' ? 'orange' :
                            metric.category === 'Network' ? 'blue' :
                            metric.category === 'System' ? 'green' :
                            metric.category === 'Application' ? 'purple' :
                            metric.category === 'Security' ? 'red' : 'gray'
                          }>
                            {metric.category}
                          </Badge>
                        </Table.Td>
                        <Table.Td style={{ padding: '6px 12px', verticalAlign: 'middle' }}>
                          <Text size="xs" c="dark" fw={500}>{metric.unit}</Text>
                        </Table.Td>
                        <Table.Td style={{ padding: '6px 12px', verticalAlign: 'middle', maxWidth: '200px' }}>
                          <Text size="xs" c="dark" fw={400} style={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}>
                            {metric.description}
                          </Text>
                        </Table.Td>
                        <Table.Td style={{ padding: '6px 12px', verticalAlign: 'middle', textAlign: 'center' }}>
                          <Badge
                            size="xs"
                            color={metric.enabled ? 'green' : 'orange'}
                            variant="filled"
                          >
                            {metric.enabled ? 'Active' : 'Disabled'}
                          </Badge>
                        </Table.Td>
                        <Table.Td style={{ padding: '6px 12px', verticalAlign: 'middle', textAlign: 'center' }}>
                          <Tooltip label="Edit metric configuration">
                            <Button
                              size="xs"
                              variant="outline"
                              color="blue"
                              style={{
                                height: '20px',
                                minHeight: '20px',
                                borderColor: 'var(--mantine-color-blue-7)',
                                color: 'var(--mantine-color-blue-8)',
                                borderWidth: '1.5px'
                              }}
                            >
                              <IconEdit size={12} stroke={2} />
                            </Button>
                          </Tooltip>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>

                {filteredMetrics.length === 0 && (
                  <Text ta="center" c="dimmed" py="xl">
                    No metrics found matching your search criteria.
                  </Text>
                )}

                <Group justify="flex-end" mt="lg">
                  <Button variant="outline" color="blue">
                    Export Metrics Schema
                  </Button>
                  <Button variant="outline" color="green">
                    Import Metrics
                  </Button>
                  <Button variant="filled" color="blue">
                    Save Changes
                  </Button>
                </Group>
              </Card>
            </Stack>
          </Tabs.Panel>
        </Tabs>

        {/* Delete All Logs Modal */}
        <Modal
          opened={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setDeleteConfirmText('');
          }}
          title="Delete ALL Logs - EXTREME CAUTION"
          size="md"
        >
          <Stack gap="md">
            <Alert icon={<IconAlertTriangle size={16} />} color="red">
              <Text fw={500}>This action will permanently delete ALL logs from the system!</Text>
              <Text size="sm">This includes active, hidden, and archived logs. This action cannot be undone.</Text>
            </Alert>

            <Text>
              To confirm this dangerous action, please type <Text span fw={700} c="red">DELETE ALL LOGS</Text> in the field below:
            </Text>

            <TextInput
              placeholder="Type: DELETE ALL LOGS"
              value={deleteConfirmText}
              onChange={(event) => setDeleteConfirmText(event.currentTarget.value)}
              error={deleteConfirmText !== '' && deleteConfirmText !== 'DELETE ALL LOGS' ? 'Text must match exactly' : null}
            />

            <Group justify="flex-end" gap="sm">
              <Button
                variant="light"
                onClick={() => {
                  setShowDeleteModal(false);
                  setDeleteConfirmText('');
                }}
              >
                Cancel
              </Button>
              <Button
                color="red"
                leftSection={<IconTrash size={16} />}
                onClick={handlePermanentDelete}
                disabled={deleteConfirmText !== 'DELETE ALL LOGS'}
              >
                Delete ALL Logs
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* Log Rotation Modal */}
        <Modal
          opened={showRotationModal}
          onClose={() => setShowRotationModal(false)}
          title="Manual Log Rotation"
          size="md"
        >
          <Stack gap="md">
            <Alert icon={<IconRotate size={16} />} color="blue">
              <Text fw={500}>Log rotation will archive old logs and free up active storage.</Text>
              <Text size="sm">This process may take a few minutes to complete.</Text>
            </Alert>

            <Text>
              This will:
            </Text>
            <ul style={{ marginLeft: '20px' }}>
              <li>Archive logs older than {logRetentionDays} days</li>
              <li>Compress archived logs to save space</li>
              <li>Update log statistics</li>
              <li>Free up active storage space</li>
            </ul>

            <Group justify="flex-end" gap="sm">
              <Button
                variant="light"
                onClick={() => setShowRotationModal(false)}
              >
                Cancel
              </Button>
              <Button
                color="blue"
                leftSection={<IconRotate size={16} />}
                onClick={handleLogRotation}
              >
                Start Rotation
              </Button>
            </Group>
          </Stack>
        </Modal>
      </div>
    </DashboardLayout>
  );
};

export default Settings;
