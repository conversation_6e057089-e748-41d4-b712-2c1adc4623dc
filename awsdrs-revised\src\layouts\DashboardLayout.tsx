import { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  AppShell,
  Burger,
  Group,
  Text,
  NavLink,
  ScrollArea,
  useMantineColorScheme,
  TextInput,
  Avatar,
  Menu
} from '@mantine/core';
import {
  IconGauge,
  IconServer,
  IconChartBar,
  IconAlertCircle,
  IconDeviceDesktopAnalytics,
  IconCloudComputing,
  IconChartLine,
  IconCalendar,
  IconSettings,
  IconLogout,
  IconMenu2,
  IconSearch,
  IconSun,
  IconMoon,
  IconUser,
  IconRefresh,
  IconLock,
  IconLockOpen,
  IconRestore,
  IconStar,
  IconUsers,
  IconEye,
  IconBrandBehance,
  IconBuilding,
  IconShieldCheck,
  IconPuzzle,
  IconCreditCard,
  IconTool,
  IconHelp,
  IconBook,
  IconClock,
  IconReload
} from '@tabler/icons-react';
import { useAppDispatch } from '../store';
import { logout } from '../store/slices/authSlice';

interface DashboardControls {
  isEditing: boolean;
  loading: boolean;
  showWidgetMenu: boolean;
  onToggleEdit: () => void;
  onRefresh: () => void;
  onResetLayout: () => void;
  onToggleWidgetMenu: () => void;
}

interface DashboardLayoutProps {
  children: React.ReactNode;
  dashboardControls?: DashboardControls;
  navbarOpened?: boolean;
}

interface NavbarLinkProps {
  icon: React.ReactNode;
  label: string;
  active?: boolean;
  onClick?: () => void;
}

function NavbarLink({ icon, label, active, onClick }: NavbarLinkProps) {
  return (
    <NavLink
      href="#"
      label={label}
      leftSection={icon}
      active={active}
      onClick={(event) => {
        event.preventDefault();
        onClick?.();
      }}
      style={{ borderRadius: '8px', marginBottom: '4px' }}
    />
  );
}

// Header button component that matches navbar styling with cyan glow effects
interface HeaderButtonProps {
  icon?: React.ReactNode;
  children?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  title?: string;
  active?: boolean;
}

function HeaderButton({ icon, children, onClick, disabled, loading, title, active }: HeaderButtonProps) {
  const { colorScheme } = useMantineColorScheme();

  // Colors matching the theme
  const cyanPrimary = '#06b6d4'; // cyan-500 for dark mode
  const cyanLight = 'rgba(6, 182, 212, 0.1)'; // cyan with low opacity for dark mode
  const cyanGlow = 'rgba(6, 182, 212, 0.2)'; // cyan glow effect for dark mode

  // Light mode colors matching left menu
  const lightModeHover = '#1e40af'; // blue-800 - matches left menu hover
  const lightModeHoverBg = 'rgba(59, 130, 246, 0.08)'; // blue with low opacity

  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      title={title}
      style={{
        backgroundColor: active
          ? (colorScheme === 'dark' ? cyanLight : lightModeHoverBg)
          : 'transparent',
        border: active
          ? `1px solid ${colorScheme === 'dark' ? cyanPrimary : lightModeHover}`
          : '1px solid transparent',
        color: active
          ? (colorScheme === 'dark' ? cyanPrimary : lightModeHover)
          : (colorScheme === 'dark' ? 'white' : '#374151'),
        padding: '8px 12px',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: active ? 600 : 500,
        cursor: (disabled || loading) ? 'not-allowed' : 'pointer',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        opacity: (disabled || loading) ? 0.6 : 1,
        minHeight: '36px',
        boxShadow: active
          ? (colorScheme === 'dark' ? `0 0 10px ${cyanGlow}` : 'none')
          : 'none'
      }}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          if (active) {
            if (colorScheme === 'dark') {
              e.currentTarget.style.backgroundColor = cyanGlow;
              e.currentTarget.style.boxShadow = `0 0 15px ${cyanGlow}`;
            } else {
              e.currentTarget.style.backgroundColor = 'rgba(59, 130, 246, 0.12)';
            }
          } else {
            e.currentTarget.style.backgroundColor = colorScheme === 'dark'
              ? 'rgba(6, 182, 212, 0.1)'
              : lightModeHoverBg;
            e.currentTarget.style.color = colorScheme === 'dark' ? cyanPrimary : lightModeHover;
            if (colorScheme === 'dark') {
              e.currentTarget.style.boxShadow = `0 0 8px rgba(6, 182, 212, 0.15)`;
            }
          }
        }
      }}
      onMouseLeave={(e) => {
        if (!active) {
          e.currentTarget.style.backgroundColor = 'transparent';
          e.currentTarget.style.color = colorScheme === 'dark' ? 'white' : '#374151';
          e.currentTarget.style.boxShadow = 'none';
        } else {
          e.currentTarget.style.backgroundColor = colorScheme === 'dark' ? cyanLight : lightModeHoverBg;
          if (colorScheme === 'dark') {
            e.currentTarget.style.boxShadow = `0 0 10px ${cyanGlow}`;
          } else {
            e.currentTarget.style.boxShadow = 'none';
          }
        }
      }}
    >
      {icon}
      {children}
    </button>
  );
}

export default function DashboardLayout({ children, dashboardControls }: DashboardLayoutProps) {
  const { colorScheme, toggleColorScheme } = useMantineColorScheme();
  const [mobileOpened, setMobileOpened] = useState(false);
  const [desktopOpened, setDesktopOpened] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  // Toggle functions
  const toggleMobileSidebar = () => setMobileOpened((prev) => !prev);
  const toggleDesktopSidebar = () => setDesktopOpened((prev) => !prev);

  const navItems = [
    {
      icon: <IconGauge size={18} />,
      label: 'Dashboard',
      path: '/dashboard',
      active: location.pathname === '/' || location.pathname === '/dashboard'
    },
    {
      icon: <IconServer size={18} />,
      label: 'Servers',
      path: '/servers',
      active: location.pathname === '/servers'
    },
    {
      icon: <IconAlertCircle size={18} />,
      label: 'Alerts',
      path: '/alerts',
      active: location.pathname === '/alerts'
    },
    {
      icon: <IconChartBar size={18} />,
      label: 'Metrics',
      path: '/metrics',
      active: location.pathname === '/metrics'
    },
    {
      icon: <IconDeviceDesktopAnalytics size={18} />,
      label: 'Analytics',
      path: '/analytics',
      active: location.pathname === '/analytics'
    },
    {
      icon: <IconReload size={18} />,
      label: 'Recovery',
      path: '/recovery',
      active: location.pathname === '/recovery'
    },
    {
      icon: <IconCloudComputing size={18} />,
      label: 'Cloud Resources',
      path: '/cloud-resources',
      active: location.pathname === '/cloud-resources'
    },
    {
      icon: <IconCalendar size={18} />,
      label: 'Calendar',
      path: '/calendar',
      active: location.pathname === '/calendar'
    },
  ];

  return (
    <AppShell
      header={{ height: 60 }}
      navbar={{
        width: 220,
        breakpoint: 'sm',
        collapsed: { desktop: !desktopOpened, mobile: !mobileOpened }
      }}
      padding="md"
      styles={(theme) => ({
        main: {
          backgroundColor: colorScheme === 'dark' ? theme.colors.dark[8] : theme.colors.gray[0],
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          padding: theme.spacing.md,
          transition: 'margin-left 150ms cubic-bezier(0.4, 0, 0.2, 1), max-width 150ms cubic-bezier(0.4, 0, 0.2, 1)'
        },
        header: {
          borderBottom: 'none'
        },
        navbar: {
          backgroundColor: colorScheme === 'dark' ? theme.colors.dark[8] : theme.colors.gray[0],
          backdropFilter: 'blur(12px)',
          borderRight: 'none',
          transition: 'transform 150ms cubic-bezier(0.4, 0, 0.2, 1)',
          position: 'fixed',
          zIndex: 200
        },
        root: {
          width: '100vw',
          height: '100vh'
        }
      })}
    >
      <AppShell.Header p="xs" style={{
        backgroundColor: colorScheme === 'dark' ? '#020617' : '#ffffff',
        borderBottom: 'none'
      }}>
        <Group justify="space-between" style={{ height: '100%' }}>
          <Group>
            {/* Mobile burger menu */}
            <Burger
              opened={mobileOpened}
              onClick={toggleMobileSidebar}
              size="sm"
              hiddenFrom="sm"
            />

            {/* Desktop sidebar toggle */}
            <HeaderButton
              icon={<IconMenu2 size={18} />}
              onClick={toggleDesktopSidebar}
              title={desktopOpened ? "Collapse Sidebar" : "Expand Sidebar"}
            />

            <div>
              <Text fw={700} size="lg" style={{ color: 'var(--mantine-color-blue-6)' }}>
                CHCIT
              </Text>
              <Text size="xs" c="dimmed" style={{ lineHeight: 1 }}>
                Disaster Recovery Dashboard
              </Text>
            </div>

            {/* Search Field - Left aligned with dashboard content */}
            {dashboardControls && (
              <TextInput
                placeholder="Search..."
                leftSection={<IconSearch size={16} />}
                value={searchValue}
                onChange={(event) => setSearchValue(event.currentTarget.value)}
                style={{
                  width: 200,
                  marginLeft: '16px'
                }}
                styles={{
                  input: {
                    background: colorScheme === 'dark' ? 'var(--vision-glass)' : 'var(--mantine-color-body)',
                    border: colorScheme === 'dark' ? '1.5px solid var(--vision-border)' : '1px solid var(--mantine-color-gray-3)',
                    borderRadius: '8px',
                    backdropFilter: 'blur(12px)',
                    boxShadow: colorScheme === 'dark'
                      ? 'var(--vision-glow), inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 0 0 0.5px rgba(100, 116, 139, 0.2)'
                      : '0 2px 4px rgba(0, 0, 0, 0.1)',
                    transition: 'all var(--transition-duration) var(--transition-timing)',
                    '&:focus': {
                      borderColor: colorScheme === 'dark' ? 'var(--vision-cyan)' : 'var(--mantine-color-blue-6)',
                      boxShadow: colorScheme === 'dark'
                        ? '0 0 30px rgba(6, 182, 212, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.15)'
                        : '0 0 0 2px rgba(59, 130, 246, 0.2), 0 2px 8px rgba(0, 0, 0, 0.15)',
                      transform: 'translateY(-1px)'
                    },
                    '&:hover': {
                      boxShadow: colorScheme === 'dark'
                        ? '0 0 30px rgba(6, 182, 212, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.15)'
                        : '0 4px 8px rgba(0, 0, 0, 0.12)',
                      transform: 'translateY(-1px)'
                    }
                  }
                }}
              />
            )}
          </Group>

          <Group gap="sm">
            {/* Dashboard Controls */}
            {dashboardControls ? (
              <>
                <HeaderButton
                  icon={<IconMenu2 size={18} />}
                  onClick={dashboardControls.onToggleWidgetMenu}
                  title="Widget Menu"
                  active={dashboardControls.showWidgetMenu}
                />

                <HeaderButton
                  icon={dashboardControls.isEditing ? <IconLockOpen size={18} /> : <IconLock size={18} />}
                  onClick={dashboardControls.onToggleEdit}
                  title={dashboardControls.isEditing ? "Lock Layout" : "Edit Layout"}
                  active={dashboardControls.isEditing}
                />

                <HeaderButton
                  icon={colorScheme === 'dark' ? <IconSun size={18} /> : <IconMoon size={18} />}
                  onClick={() => toggleColorScheme()}
                  title={colorScheme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
                />

                <HeaderButton
                  icon={<IconRefresh size={18} />}
                  onClick={dashboardControls.onRefresh}
                  loading={dashboardControls.loading}
                  title="Refresh Dashboard"
                  active={dashboardControls.loading}
                />
              </>
            ) : (
              <>
                {/* Default controls for non-dashboard pages */}
                <HeaderButton
                  icon={colorScheme === 'dark' ? <IconSun size={18} /> : <IconMoon size={18} />}
                  onClick={() => toggleColorScheme()}
                  title={colorScheme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
                />

                <HeaderButton
                  icon={<IconRefresh size={18} />}
                  onClick={() => window.location.reload()}
                  title="Refresh Page"
                />
              </>
            )}

            {/* User Avatar Menu */}
            <Menu shadow="lg" width={280} position="bottom-end" offset={8}>
              <Menu.Target>
                <div style={{ position: 'relative', display: 'inline-block' }}>
                  <HeaderButton title="User Menu">
                    <IconUser size={20} stroke={2.5} />
                  </HeaderButton>
                </div>
              </Menu.Target>
              <Menu.Dropdown
                style={{
                  backgroundColor: colorScheme === 'dark' ? '#1a1b1e' : '#ffffff',
                  border: colorScheme === 'dark' ? '1px solid #373A40' : '1px solid #e9ecef',
                  borderRadius: '8px',
                  padding: '8px 0'
                }}
              >
                {/* User Info Header */}
                <div style={{
                  padding: '12px 16px 8px 16px',
                  borderBottom: colorScheme === 'dark' ? '1px solid #373A40' : '1px solid #e9ecef',
                  marginBottom: '8px'
                }}>
                  <Group gap="sm">
                    <div>
                      <Text size="sm" fw={600} style={{ color: colorScheme === 'dark' ? '#ffffff' : '#000000', marginBottom: '4px' }}>
                        Brent Taylor
                      </Text>
                      <Text size="xs" style={{ color: colorScheme === 'dark' ? '#909296' : '#868e96', marginBottom: '6px' }}>
                        CHCIT • <EMAIL>
                      </Text>
                      <Group gap={4} mt={4}>
                        <div style={{
                          backgroundColor: '#06b6d4',
                          color: 'white',
                          fontSize: '10px',
                          padding: '2px 6px',
                          borderRadius: '4px',
                          fontWeight: 600
                        }}>
                          administrator
                        </div>
                        <Group gap={4} align="center">
                          <div style={{
                            width: '6px',
                            height: '6px',
                            borderRadius: '50%',
                            backgroundColor: '#10b981'
                          }} />
                          <Text size="xs" style={{ color: colorScheme === 'dark' ? '#10b981' : '#059669' }}>
                            Timeout: 2 hrs
                          </Text>
                        </Group>
                      </Group>
                    </div>
                  </Group>
                </div>

                {/* Menu Items */}
                <Menu.Item
                  leftSection={<IconBuilding size={16} />}
                  style={{
                    color: colorScheme === 'dark' ? '#ffffff' : '#000000',
                    fontSize: '14px'
                  }}
                >
                  Organisations
                </Menu.Item>

                <Menu.Item
                  leftSection={<IconPuzzle size={16} />}
                  style={{
                    color: colorScheme === 'dark' ? '#ffffff' : '#000000',
                    fontSize: '14px'
                  }}
                >
                  PSA Integration
                </Menu.Item>

                <Menu.Item
                  leftSection={<IconServer size={16} />}
                  style={{
                    color: colorScheme === 'dark' ? '#ffffff' : '#000000',
                    fontSize: '14px'
                  }}
                >
                  Tenancy
                </Menu.Item>

                <Menu.Item
                  leftSection={<IconCreditCard size={16} />}
                  style={{
                    color: colorScheme === 'dark' ? '#ffffff' : '#000000',
                    fontSize: '14px'
                  }}
                >
                  Billing
                </Menu.Item>

                <Menu.Item
                  leftSection={<IconTool size={16} />}
                  rightSection={
                    <div style={{
                      backgroundColor: '#6b7280',
                      color: 'white',
                      fontSize: '10px',
                      padding: '2px 6px',
                      borderRadius: '4px',
                      fontWeight: 600
                    }}>
                      Beta
                    </div>
                  }
                  style={{
                    color: colorScheme === 'dark' ? '#ffffff' : '#000000',
                    fontSize: '14px'
                  }}
                >
                  AutoFix
                </Menu.Item>

                <Menu.Divider style={{
                  borderColor: colorScheme === 'dark' ? '#373A40' : '#e9ecef',
                  margin: '8px 0'
                }} />

                <Menu.Item
                  leftSection={<IconSettings size={16} />}
                  onClick={() => navigate('/settings')}
                  style={{
                    color: colorScheme === 'dark' ? '#ffffff' : '#000000',
                    fontSize: '14px'
                  }}
                >
                  Settings
                </Menu.Item>

                <Menu.Item
                  leftSection={<IconHelp size={16} />}
                  style={{
                    color: colorScheme === 'dark' ? '#ffffff' : '#000000',
                    fontSize: '14px'
                  }}
                >
                  Support
                </Menu.Item>

                <Menu.Item
                  leftSection={<IconBook size={16} />}
                  rightSection={<Text size="xs" style={{ color: '#6b7280' }}>↗</Text>}
                  style={{
                    color: colorScheme === 'dark' ? '#ffffff' : '#000000',
                    fontSize: '14px'
                  }}
                >
                  Documentation
                </Menu.Item>

                <Menu.Divider style={{
                  borderColor: colorScheme === 'dark' ? '#373A40' : '#e9ecef',
                  margin: '8px 0'
                }} />

                <Menu.Item
                  leftSection={<IconLogout size={16} />}
                  onClick={() => dispatch(logout())}
                  style={{
                    color: colorScheme === 'dark' ? '#ffffff' : '#000000',
                    fontSize: '14px'
                  }}
                >
                  Logout
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </Group>
        </Group>
      </AppShell.Header>

      <AppShell.Navbar p="xs">
        <AppShell.Section>
          <div style={{ marginBottom: '40px' }}>
            {/* Empty space to maintain menu button positioning */}
          </div>
        </AppShell.Section>

        <AppShell.Section grow component={ScrollArea}>
          <div style={{ paddingTop: '20px' }}>
            {navItems.map((item, index) => (
              <div key={index} style={{ marginBottom: '12px' }}>
                <NavbarLink
                  icon={item.icon}
                  label={item.label}
                  active={item.active}
                  onClick={() => navigate(item.path)}
                />
              </div>
            ))}
          </div>
        </AppShell.Section>

        <AppShell.Section>
          <div style={{ paddingTop: '20px' }}>
            <div style={{ marginBottom: '12px' }}>
              <NavbarLink
                icon={<IconSettings size={18} />}
                label="Settings"
                active={location.pathname === '/settings'}
                onClick={() => navigate('/settings')}
              />
            </div>
            <div style={{ marginBottom: '12px' }}>
              <NavbarLink
                icon={<IconLogout size={18} />}
                label="Logout"
                onClick={() => dispatch(logout())}
              />
            </div>
          </div>
        </AppShell.Section>
      </AppShell.Navbar>

      <AppShell.Main>
        <div style={{
          width: '100%',
          marginLeft: desktopOpened ? '220px' : '0px',
          maxWidth: desktopOpened ? 'calc(100vw - 220px - 32px)' : '100%',
          transition: 'margin-left 150ms cubic-bezier(0.4, 0, 0.2, 1), max-width 150ms cubic-bezier(0.4, 0, 0.2, 1)',
          position: 'relative',
          paddingTop: '20px' // Add padding to prevent content from being hidden behind header
        }}>
          {children}
        </div>
      </AppShell.Main>
    </AppShell>
  );
}
