import {
  createReactComponent
} from "./chunk-5HMDTYKJ.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconMapMinus.mjs
var IconMapMinus = createReactComponent("outline", "map-minus", "IconMapMinus", [["path", { "d": "M12 18.5l-3 -1.5l-6 3v-13l6 -3l6 3l6 -3v11", "key": "svg-0" }], ["path", { "d": "M9 4v13", "key": "svg-1" }], ["path", { "d": "M15 7v8", "key": "svg-2" }], ["path", { "d": "M16 19h6", "key": "svg-3" }]]);

export {
  IconMapMinus
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconMapMinus.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-OWH3UFTV.js.map
