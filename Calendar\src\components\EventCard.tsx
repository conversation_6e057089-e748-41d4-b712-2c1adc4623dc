import { format } from 'date-fns'
import { Paper, Text, Group, Badge, Stack } from '@mantine/core'
import { CalendarEvent } from '../types/calendar'

interface EventCardProps {
  event: CalendarEvent
  onClick: (event: CalendarEvent) => void
  isCompact?: boolean
  isFirstDay?: boolean
  isLastDay?: boolean
  isMiddleDay?: boolean
}

export function EventCard({ 
  event, 
  onClick, 
  isCompact = false,
  isFirstDay = false,
  isLastDay = false,
  isMiddleDay = false
}: EventCardProps) {
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onClick(event)
  }

  const formatTime = (date: Date) => {
    const hour = date.getHours()
    const minute = date.getMinutes()
    const ampm = hour >= 12 ? 'p' : 'a'
    const displayHour = hour % 12 || 12
    
    if (minute === 0) {
      return `${displayHour}${ampm}`
    }
    return `${displayHour}:${minute.toString().padStart(2, '0')}${ampm}`
  }

  if (isCompact) {
    if (event.allDay) {
      // All-day events as thin colored bars that can span multiple days
      let borderRadius = ''
      if (isFirstDay && isLastDay) {
        borderRadius = 'rounded-sm'
      } else if (isFirstDay) {
        borderRadius = 'rounded-l-sm'
      } else if (isLastDay) {
        borderRadius = 'rounded-r-sm'
      }
      
      return (
        <Paper
          onClick={handleClick}
          className={`cursor-pointer ${borderRadius}`}
          style={{ 
            backgroundColor: event.color || '#3B82F6',
            height: '18px',
            lineHeight: '16px',
            cursor: 'pointer'
          }}
          p="xs"
          withBorder={false}
          title={event.title}
        >
          <Text size="xs" c="white" fw={500} truncate>
            {(isFirstDay || (!isFirstDay && !isLastDay && !isMiddleDay)) && event.title}
          </Text>
        </Paper>
      )
    } else {
      // Timed events as dots with time and title
      return (
        <Group
          onClick={handleClick}
          gap="xs"
          style={{ cursor: 'pointer' }}
          title={`${formatTime(event.start)} - ${formatTime(event.end)}: ${event.title}`}
        >
          <Badge
            size="xs"
            variant="filled"
            style={{ backgroundColor: event.color || '#3B82F6' }}
          >
            {formatTime(event.start)}
          </Badge>
          <Text size="xs" c="dimmed" truncate>
            {event.title}
          </Text>
        </Group>
      )
    }
  }

  // Non-compact view (for detailed views)
  return (
    <Paper
      onClick={handleClick}
      p="md"
      withBorder
      shadow="xs"
      style={{ borderLeftColor: event.color || '#3B82F6', borderLeftWidth: '4px' }}
      className="cursor-pointer hover:shadow-md transition-shadow"
    >
      <Group justify="space-between" align="flex-start">
        <Stack gap="xs" style={{ flex: 1 }}>
          <Text size="sm" fw={500}>{event.title}</Text>
          
          {!event.allDay && (
            <Text size="xs" c="dimmed">
              {formatTime(event.start)} - {formatTime(event.end)}
            </Text>
          )}
          
          {event.location && (
            <Text size="xs" c="dimmed">
              📍 {event.location}
            </Text>
          )}
          
          {event.attendees && event.attendees.length > 0 && (
            <Text size="xs" c="dimmed">
              👥 {event.attendees.length} attendee{event.attendees.length > 1 ? 's' : ''}
            </Text>
          )}
          
          {event.description && (
            <Text size="xs" c="dimmed" lineClamp={2}>
              {event.description}
            </Text>
          )}
        </Stack>
        
        <Badge
          size="xs"
          variant="filled"
          style={{ backgroundColor: event.color || '#3B82F6' }}
        >
          •
        </Badge>
      </Group>
    </Paper>
  )
}