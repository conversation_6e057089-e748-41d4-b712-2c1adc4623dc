import { useState, useEffect, useCallback } from "react";
import { Text, Card } from "@mantine/core";
import { IconCpu, IconServer, IconDatabase, IconActivity } from "@tabler/icons-react";
import DashboardLayout from "../layouts/DashboardLayout";
import { MetricCard, LineChartWidget, BarChartWidget, PieChartWidget } from "../components/widgets";
import WidgetMenu from "../components/WidgetMenu";
import { useLayoutManager } from "../hooks/useLayoutManager";
import dataService from "../services/dataService";
import { Responsive, WidthProvider } from 'react-grid-layout';
import type { Layout } from 'react-grid-layout';

const ResponsiveGridLayout = WidthProvider(Responsive);

// Define Metrics widgets (like Dashboard pattern)
interface MetricsWidget {
  id: string;
  title: string;
  type: string;
}

const defaultMetricsWidgets: MetricsWidget[] = [
  { id: 'cpu-metric', title: 'CPU Usage', type: 'cpu-metric' },
  { id: 'memory-metric', title: 'Memory Usage', type: 'memory-metric' },
  { id: 'disk-metric', title: 'Disk I/O', type: 'disk-metric' },
  { id: 'network-metric', title: 'Network Latency', type: 'network-metric' },
  { id: 'system-usage-chart', title: 'System Resource Usage', type: 'system-usage-chart' },
  { id: 'health-status-chart', title: 'System Health Status', type: 'health-status-chart' },
  { id: 'network-traffic-chart', title: 'Network Traffic Trends', type: 'network-traffic-chart' }
];

// Default layout configuration (like Dashboard pattern)
const defaultMetricsLayouts = {
  lg: [
    { i: 'cpu-metric', x: 0, y: 0, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'memory-metric', x: 3, y: 0, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'disk-metric', x: 6, y: 0, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'network-metric', x: 9, y: 0, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'system-usage-chart', x: 0, y: 3, w: 6, h: 5, minW: 4, minH: 4 },
    { i: 'health-status-chart', x: 6, y: 3, w: 6, h: 5, minW: 3, minH: 4 },
    { i: 'network-traffic-chart', x: 0, y: 8, w: 12, h: 4, minW: 6, minH: 3 }
  ],
  md: [
    { i: 'cpu-metric', x: 0, y: 0, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'memory-metric', x: 3, y: 0, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'disk-metric', x: 0, y: 3, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'network-metric', x: 3, y: 3, w: 3, h: 3, minW: 2, minH: 3 },
    { i: 'system-usage-chart', x: 0, y: 6, w: 6, h: 5, minW: 4, minH: 4 },
    { i: 'health-status-chart', x: 0, y: 11, w: 6, h: 5, minW: 3, minH: 4 },
    { i: 'network-traffic-chart', x: 0, y: 16, w: 6, h: 4, minW: 6, minH: 3 }
  ],
  sm: [
    { i: 'cpu-metric', x: 0, y: 0, w: 4, h: 3, minW: 4, minH: 3 },
    { i: 'memory-metric', x: 0, y: 3, w: 4, h: 3, minW: 4, minH: 3 },
    { i: 'disk-metric', x: 0, y: 6, w: 4, h: 3, minW: 4, minH: 3 },
    { i: 'network-metric', x: 0, y: 9, w: 4, h: 3, minW: 4, minH: 3 },
    { i: 'system-usage-chart', x: 0, y: 12, w: 4, h: 5, minW: 4, minH: 4 },
    { i: 'health-status-chart', x: 0, y: 17, w: 4, h: 5, minW: 4, minH: 4 },
    { i: 'network-traffic-chart', x: 0, y: 22, w: 4, h: 4, minW: 4, minH: 3 }
  ]
};

// Widget factory function (like Dashboard pattern)
const renderMetricsWidget = (widget: MetricsWidget) => {
  const metricsData = dataService.getMetricsData();
  const systemResourceData = dataService.getSystemResourceData();
  const systemHealthData = dataService.getSystemHealthData();
  const networkTrafficData = dataService.getNetworkTrafficData();

  switch (widget.type) {
    case 'cpu-metric':
      return (
        <div style={{ height: '100%', padding: '16px' }}>
          <MetricCard
            title="CPU Usage"
            value={metricsData.cpu.value}
            change={metricsData.cpu.change}
            changeType={metricsData.cpu.changeType}
            icon={<IconCpu size={20} />}
            color="blue"
            subtitle="Average across all servers"
          />
        </div>
      );
    case 'memory-metric':
      return (
        <div style={{ height: '100%', padding: '16px' }}>
          <MetricCard
            title="Memory Usage"
            value={metricsData.memory.value}
            change={metricsData.memory.change}
            changeType={metricsData.memory.changeType}
            icon={<IconServer size={20} />}
            color="orange"
            subtitle="System memory utilization"
          />
        </div>
      );
    case 'disk-metric':
      return (
        <div style={{ height: '100%', padding: '16px' }}>
          <MetricCard
            title="Disk I/O"
            value={metricsData.diskIO.value}
            change={metricsData.diskIO.change}
            changeType={metricsData.diskIO.changeType}
            icon={<IconDatabase size={20} />}
            color="cyan"
            subtitle="Read/write throughput"
          />
        </div>
      );
    case 'network-metric':
      return (
        <div style={{ height: '100%', padding: '16px' }}>
          <MetricCard
            title="Network Latency"
            value={metricsData.networkLatency.value}
            change={metricsData.networkLatency.change}
            changeType={metricsData.networkLatency.changeType}
            icon={<IconActivity size={20} />}
            color="green"
            subtitle="Average response time"
          />
        </div>
      );
    case 'system-usage-chart':
      return (
        <LineChartWidget
          title="System Resource Usage (24h)"
          data={systemResourceData}
          lines={[
            { dataKey: 'cpu', stroke: '#3b82f6', name: 'CPU Usage (%)' },
            { dataKey: 'memory', stroke: '#f59e0b', name: 'Memory Usage (%)' },
            { dataKey: 'disk', stroke: '#10b981', name: 'Disk Usage (%)' }
          ]}
          height={280}
          onRefresh={() => console.log('Refreshing system metrics...')}
          onExport={() => console.log('Exporting system metrics...')}
          withBorder={false}
        />
      );
    case 'health-status-chart':
      return (
        <PieChartWidget
          title="System Health Status"
          data={systemHealthData}
          colors={['#10b981', '#f59e0b', '#ef4444']}
          height={280}
          onRefresh={() => console.log('Refreshing health data...')}
          onExport={() => console.log('Exporting health data...')}
          withBorder={false}
        />
      );
    case 'network-traffic-chart':
      return (
        <BarChartWidget
          title="Network Traffic Trends (6 Months)"
          data={networkTrafficData}
          bars={[
            { dataKey: 'inbound', fill: '#06b6d4', name: 'Inbound Traffic (MB)' },
            { dataKey: 'outbound', fill: '#8b5cf6', name: 'Outbound Traffic (MB)' }
          ]}
          height={200}
          onRefresh={() => console.log('Refreshing network data...')}
          onExport={() => console.log('Exporting network data...')}
          withBorder={false}
        />
      );
    default:
      return (
        <Card h="100%" p="md">
          <Text c="dimmed" ta="center">
            Widget: {widget.type}
          </Text>
        </Card>
      );
  }
};

const getInitialMenuPosition = () => ({
  x: Math.max(50, window.innerWidth - 370),
  y: 100
});

const Metrics = () => {
  // Layout management with multiple presets
  const {
    layouts,
    layoutPresets,
    currentLayoutId,
    switchToLayout,
    saveCurrentLayout,
    deleteLayout,
    resetToDefault,
    handleLayoutChange
  } = useLayoutManager({
    pageKey: 'metrics',
    defaultLayouts: defaultMetricsLayouts,
    defaultLayoutName: 'Default Metrics Layout'
  });

  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [widgets, setWidgets] = useState(defaultMetricsWidgets);
  const [visibleWidgets, setVisibleWidgets] = useState(new Set(defaultMetricsWidgets.map(w => w.id)));
  const [showWidgetMenu, setShowWidgetMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState(getInitialMenuPosition());
  const [isDragging, setIsDragging] = useState(false);

  // Auto-refresh data
  useEffect(() => {
    const interval = setInterval(() => {
      // Trigger re-render for fresh data
      setLoading(false);
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Load saved widget visibility
  useEffect(() => {
    try {
      const savedVisibility = localStorage.getItem('metrics-visible-widgets');
      if (savedVisibility) {
        setVisibleWidgets(new Set(JSON.parse(savedVisibility)));
      }
    } catch (error) {
      console.error('Error loading widget visibility:', error);
    }
  }, []);

  const toggleWidgetVisibility = (widgetId: string) => {
    setVisibleWidgets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(widgetId)) {
        newSet.delete(widgetId);
      } else {
        newSet.add(widgetId);
      }
      localStorage.setItem('metrics-visible-widgets', JSON.stringify([...newSet]));
      return newSet;
    });
  };

  const closeWidgetMenu = () => {
    setShowWidgetMenu(false);
  };

  const resetToDefaultAndClose = () => {
    resetToDefault();
    setShowWidgetMenu(false);
    setMenuPosition(getInitialMenuPosition());
  };

  // Optimized drag handlers for floating menu
  const handleMouseDown = (e: React.MouseEvent) => {
    // Prevent dragging when clicking on interactive elements
    const target = e.target as HTMLElement;
    if (target.tagName === 'BUTTON' || target.tagName === 'INPUT' || target.tagName === 'SELECT' ||
        target.closest('button') || target.closest('[role="button"]') || target.closest('input') || target.closest('select')) {
      return;
    }

    setIsDragging(true);
    const startX = e.clientX - menuPosition.x;
    const startY = e.clientY - menuPosition.y;

    const handleMouseMove = (e: MouseEvent) => {
      const newX = Math.max(0, Math.min(window.innerWidth - 300, e.clientX - startX));
      const newY = Math.max(0, Math.min(window.innerHeight - 200, e.clientY - startY));

      setMenuPosition({ x: newX, y: newY });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };



  // Handle refresh
  const handleRefresh = useCallback(() => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1000);
  }, []);

  // Create allWidgets array (like Dashboard)
  const allWidgets = widgets.filter(widget => visibleWidgets.has(widget.id));

  const dashboardControls = {
    isEditing,
    loading,
    showWidgetMenu,
    onToggleEdit: () => setIsEditing(!isEditing),
    onRefresh: handleRefresh,
    onResetLayout: resetToDefaultAndClose,
    onToggleWidgetMenu: () => {
      if (!showWidgetMenu) {
        setMenuPosition(getInitialMenuPosition());
      }
      setShowWidgetMenu(!showWidgetMenu);
    }
  };

  return (
    <DashboardLayout dashboardControls={dashboardControls}>
      <div style={{ width: '100%', position: 'relative', maxWidth: '1400px', margin: '0 auto' }}>

        {/* Dashboard Grid */}
        <div style={{ flex: 1, overflow: 'auto', paddingTop: '40px' }}>
          <ResponsiveGridLayout
            className={`layout ${isEditing ? 'editing' : ''}`}
            layouts={layouts}
            onLayoutChange={(layout, layouts) => {
              if (isEditing) {
                handleLayoutChange(layout, layouts);
              }
            }}
            breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
            cols={{ lg: 9, md: 10, sm: 6, xs: 4, xxs: 2 }}
            rowHeight={60}
            isDraggable={isEditing}
            isResizable={isEditing}
            margin={[16, 16]}
            containerPadding={[16, 16]}
            useCSSTransforms={true}
            compactType="vertical"
            preventCollision={false}
            allowOverlap={false}
          >
            {allWidgets.map((widget) => {
              const isVisible = visibleWidgets.has(widget.id);
              return (
                <div
                  key={widget.id}
                  className="widget-container"
                  style={{
                    display: isVisible ? 'block' : 'none'
                  }}
                >
                  <Card
                    h="100%"
                    p={0}
                    withBorder
                    shadow="sm"
                    radius="md"
                    style={{
                      position: 'relative',
                      overflow: 'visible',
                      border: isEditing ? '2px solid var(--mantine-color-blue-4)' : undefined
                    }}
                  >
                    {renderMetricsWidget(widget)}
                  </Card>
                </div>
              );
            })}
          </ResponsiveGridLayout>
        </div>

        {/* Floating Widget Selection Menu */}
        {showWidgetMenu && (
          <WidgetMenu
            widgets={widgets}
            visibleWidgets={visibleWidgets}
            onToggleWidgetVisibility={toggleWidgetVisibility}
            layoutPresets={layoutPresets}
            currentLayoutId={currentLayoutId}
            onLayoutChange={switchToLayout}
            onSaveLayout={saveCurrentLayout}
            onDeleteLayout={deleteLayout}
            onResetToDefault={resetToDefaultAndClose}
            menuPosition={menuPosition}
            isDragging={isDragging}
            onMouseDown={handleMouseDown}
            onClose={closeWidgetMenu}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default Metrics;
