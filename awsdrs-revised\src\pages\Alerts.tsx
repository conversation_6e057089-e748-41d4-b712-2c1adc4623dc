import { useState } from 'react';
import { Text, Group, Badge, Stack, TextInput, Select, Button, ActionIcon, Card, Collapse, Divider, Tabs, Table, Checkbox, Tooltip } from '@mantine/core';
import { IconAlertCircle, IconAlertTriangle, IconInfoCircle, IconSearch, IconFilter, IconRefresh, IconCheck, IconX, IconChevronDown, IconChevronUp, IconClock, IconArrowUp, IconList, IconLayoutGrid, IconEye, IconEyeOff } from '@tabler/icons-react';
import DashboardLayout from "../layouts/DashboardLayout";

const Alerts = () => {
  // Consistent tooltip styling to match header row tooltips
  const tooltipStyles = {
    tooltip: {
      fontSize: '11px',
      padding: '4px 8px',
      fontWeight: 400
    }
  };

  // State for filtering and search
  const [searchQuery, setSearchQuery] = useState('');
  const [severityFilter, setSeverityFilter] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<string>('timestamp');
  const [expandedAlerts, setExpandedAlerts] = useState<Set<number>>(new Set());
  const [viewMode, setViewMode] = useState<string>('condensed');

  // State for log management
  const [hiddenLogs, setHiddenLogs] = useState<Set<number>>(new Set());
  const [selectedLogs, setSelectedLogs] = useState<Set<number>>(new Set());

  // Mock data for alerts - now stateful so actions can update them
  const [alerts, setAlerts] = useState([
    {
      id: 1,
      title: 'High Replication Lag Detected',
      severity: 'critical',
      priority: 'high',
      status: 'Active',
      source: 'DB-PROD-01',
      timestamp: '2024-01-15 14:23:45',
      description: 'Replication lag exceeded 30 seconds threshold',
      details: 'Current lag: 45 seconds. Threshold: 30 seconds. This may impact data consistency.',
      assignee: 'DBA Team',
      category: 'Database',
      affectedSystems: ['Primary DB', 'Replica DB-02', 'Reporting System']
    },
    {
      id: 2,
      title: 'Disk Space Warning',
      severity: 'warning',
      priority: 'medium',
      status: 'Active',
      source: 'WEB-SERVER-03',
      timestamp: '2024-01-15 13:45:12',
      description: 'Disk usage above 85% threshold',
      details: 'Current usage: 87% (435GB of 500GB). Recommend cleanup or expansion.',
      assignee: 'Infrastructure Team',
      category: 'Storage',
      affectedSystems: ['Web Server 03', 'Application Logs']
    },
    {
      id: 3,
      title: 'Network Latency Alert',
      severity: 'info',
      priority: 'low',
      status: 'Resolved',
      source: 'LOAD-BALANCER',
      timestamp: '2024-01-15 12:30:00',
      description: 'Network response time increased',
      details: 'Response time increased to 250ms. Normal range: 50-150ms. Issue resolved automatically.',
      assignee: 'Network Team',
      category: 'Network',
      affectedSystems: ['Load Balancer', 'Web Servers']
    }
  ]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'red';
      case 'warning': return 'orange';
      case 'info': return 'blue';
      default: return 'gray';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'red';
      case 'warning': return 'orange';
      case 'info': return 'blue';
      default: return 'gray';
    }
  };

  const getSeverityIcon = (severity: string) => {
    const getTooltipText = (sev: string) => {
      switch (sev) {
        case 'critical': return 'Critical: System failure or major issue requiring immediate action';
        case 'warning': return 'Warning: Potential issue that should be monitored and addressed';
        case 'info': return 'Info: Informational alert for awareness and tracking';
        default: return 'Unknown severity level';
      }
    };

    const icon = (() => {
      switch (severity) {
        case 'critical': return <IconAlertCircle size={16} />;
        case 'warning': return <IconAlertTriangle size={16} />;
        case 'info': return <IconInfoCircle size={16} />;
        default: return <IconInfoCircle size={16} />;
      }
    })();

    return (
      <Tooltip label={getTooltipText(severity)} styles={tooltipStyles}>
        {icon}
      </Tooltip>
    );
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'blue';
      default: return 'gray';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return <IconArrowUp size={14} />;
      case 'medium': return <IconClock size={14} />;
      case 'low': return <IconChevronDown size={14} />;
      default: return <IconClock size={14} />;
    }
  };

  // Filter and sort alerts
  const filteredAndSortedAlerts = alerts
    .filter(alert => {
      const matchesSearch = searchQuery === '' ||
        alert.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        alert.source.toLowerCase().includes(searchQuery.toLowerCase()) ||
        alert.description.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesSeverity = severityFilter === null || alert.severity === severityFilter;
      const matchesStatus = statusFilter === null || alert.status === statusFilter;

      // Completely hide hidden logs
      const isNotHidden = !hiddenLogs.has(alert.id);

      return matchesSearch && matchesSeverity && matchesStatus && isNotHidden;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'timestamp':
          return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
        case 'severity':
          const severityOrder = { critical: 3, warning: 2, info: 1 };
          return (severityOrder[b.severity as keyof typeof severityOrder] || 0) -
                 (severityOrder[a.severity as keyof typeof severityOrder] || 0);
        case 'priority':
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return (priorityOrder[b.priority as keyof typeof priorityOrder] || 0) -
                 (priorityOrder[a.priority as keyof typeof priorityOrder] || 0);
        default:
          return 0;
      }
    });

  const handleRefresh = () => {
    // Simulate refresh - in real app this would fetch new data
    console.log('Refreshing alerts...');
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSeverityFilter(null);
    setStatusFilter(null);
  };

  // Alert actions
  const toggleAlertExpansion = (alertId: number) => {
    const newExpanded = new Set(expandedAlerts);
    if (newExpanded.has(alertId)) {
      newExpanded.delete(alertId);
    } else {
      newExpanded.add(alertId);
    }
    setExpandedAlerts(newExpanded);
  };

  const acknowledgeAlert = (alertId: number) => {
    setAlerts(prevAlerts =>
      prevAlerts.map(alert =>
        alert.id === alertId
          ? { ...alert, status: 'Acknowledged', assignee: 'You' }
          : alert
      )
    );
    console.log(`Alert ${alertId} acknowledged`);
  };

  const resolveAlert = (alertId: number) => {
    setAlerts(prevAlerts =>
      prevAlerts.map(alert =>
        alert.id === alertId
          ? { ...alert, status: 'Resolved' }
          : alert
      )
    );
    console.log(`Alert ${alertId} resolved`);
  };

  const escalateAlert = (alertId: number) => {
    setAlerts(prevAlerts =>
      prevAlerts.map(alert =>
        alert.id === alertId
          ? { ...alert, priority: 'high', assignee: 'Senior Team' }
          : alert
      )
    );
    console.log(`Alert ${alertId} escalated`);
  };

  // Log management functions
  const hideSelectedLogs = () => {
    setHiddenLogs(prev => new Set([...prev, ...selectedLogs]));
    setSelectedLogs(new Set());
    console.log(`Hidden ${selectedLogs.size} logs`);
  };

  const restoreSelectedLogs = () => {
    const newHidden = new Set(hiddenLogs);
    selectedLogs.forEach(id => newHidden.delete(id));
    setHiddenLogs(newHidden);
    setSelectedLogs(new Set());
    console.log(`Restored ${selectedLogs.size} logs`);
  };

  const toggleLogSelection = (alertId: number) => {
    const newSelected = new Set(selectedLogs);
    if (newSelected.has(alertId)) {
      newSelected.delete(alertId);
    } else {
      newSelected.add(alertId);
    }
    setSelectedLogs(newSelected);
  };

  const selectAllVisibleLogs = () => {
    const visibleIds = filteredAndSortedAlerts.map(alert => alert.id);
    setSelectedLogs(new Set(visibleIds));
  };

  const clearSelection = () => {
    setSelectedLogs(new Set());
  };

  const restoreAllHidden = () => {
    setHiddenLogs(new Set());
    setSelectedLogs(new Set());
    console.log(`Restored all ${hiddenLogs.size} hidden logs`);
  };

  // Create dashboard controls similar to main Dashboard
  const dashboardControls = {
    isEditing: false,
    loading: false,
    showWidgetMenu: false,
    onToggleEdit: () => {},
    onRefresh: () => {},
    onResetLayout: () => {},
    onToggleWidgetMenu: () => {}
  };

  return (
    <DashboardLayout dashboardControls={dashboardControls}>
      <div style={{ width: '100%', position: 'relative', maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ flex: 1, overflow: 'auto', paddingTop: '40px', padding: '40px 24px 24px 24px' }}>
          <Group justify="space-between" mb="xl">
            <Text size="xl" fw={700}>Logging and Alert Management</Text>
            <Badge color="red" size="lg">{alerts.filter(a => a.status === 'Active').length} Active</Badge>
          </Group>

          {/* Search and Filter Controls */}
          <Group mb="lg" gap="md">
            <Tooltip
              label="Search alerts by title, source, or description"
              styles={tooltipStyles}
            >
              <TextInput
                placeholder="Search alerts..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(event) => setSearchQuery(event.currentTarget.value)}
                style={{ flex: 1 }}
              />
            </Tooltip>
            <Tooltip label="Filter alerts by severity level" styles={tooltipStyles}>
              <Select
                placeholder="Severity"
                data={[
                  { value: 'critical', label: 'Critical' },
                  { value: 'warning', label: 'Warning' },
                  { value: 'info', label: 'Info' }
                ]}
                value={severityFilter}
                onChange={setSeverityFilter}
                clearable
                leftSection={<IconFilter size={16} />}
                w={120}
              />
            </Tooltip>
            <Tooltip label="Filter alerts by current status" styles={tooltipStyles}>
              <Select
                placeholder="Status"
                data={[
                  { value: 'Active', label: 'Active' },
                  { value: 'Resolved', label: 'Resolved' }
                ]}
                value={statusFilter}
                onChange={setStatusFilter}
                clearable
                w={120}
              />
            </Tooltip>
            <Tooltip label="Refresh alerts data" styles={tooltipStyles}>
              <ActionIcon variant="light" onClick={handleRefresh} size="lg">
                <IconRefresh size={16} />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Sort alerts by different criteria" styles={tooltipStyles}>
              <Select
                placeholder="Sort by"
                data={[
                  { value: 'timestamp', label: 'Time' },
                  { value: 'severity', label: 'Severity' },
                  { value: 'priority', label: 'Priority' }
                ]}
                value={sortBy}
                onChange={(value) => setSortBy(value || 'timestamp')}
                w={100}
              />
            </Tooltip>
            <Tooltip label="Clear all search filters and sorting" styles={tooltipStyles}>
              <Button variant="light" onClick={clearFilters} size="sm">
                Clear Filters
              </Button>
            </Tooltip>
          </Group>



          {/* Results count */}
          <Text size="sm" c="dimmed" mb="md">
            Showing {filteredAndSortedAlerts.length} of {alerts.length} alerts
          </Text>

          {/* Tabbed Views with Log Management Controls */}
          <Tabs value={viewMode} onChange={(value) => setViewMode(value || 'expanded')}>
            <Group justify="space-between" align="center" mb="md">
              <Tabs.List>
                <Tooltip label="Show compact table view with more alerts per page" styles={tooltipStyles}>
                  <Tabs.Tab value="condensed" leftSection={<IconList size={16} />}>
                    Condensed
                  </Tabs.Tab>
                </Tooltip>
                <Tooltip label="Show detailed card view with expandable information" styles={tooltipStyles}>
                  <Tabs.Tab value="expanded" leftSection={<IconLayoutGrid size={16} />}>
                    Expanded
                  </Tabs.Tab>
                </Tooltip>
              </Tabs.List>

              <Group gap="xs">
                {selectedLogs.size > 0 && (
                  <>
                    <Text size="xs" c="dimmed">{selectedLogs.size} selected</Text>
                    <Tooltip label="Hide selected alerts from view (can be restored later)" styles={tooltipStyles}>
                      <Button
                        variant="light"
                        color="orange"
                        leftSection={<IconEyeOff size={14} />}
                        onClick={hideSelectedLogs}
                        size="xs"
                      >
                        Hide Selected
                      </Button>
                    </Tooltip>
                    <Tooltip label="Clear current selection" styles={tooltipStyles}>
                      <Button variant="subtle" onClick={clearSelection} size="xs">
                        Clear
                      </Button>
                    </Tooltip>
                  </>
                )}

                <Tooltip label="Select all currently visible alerts" styles={tooltipStyles}>
                  <Button variant="subtle" onClick={selectAllVisibleLogs} size="xs">
                    Select All
                  </Button>
                </Tooltip>

                {hiddenLogs.size > 0 && (
                  <Tooltip label="Restore all previously hidden alerts back to view" styles={tooltipStyles}>
                    <Button
                      variant="light"
                      color="green"
                      leftSection={<IconEye size={14} />}
                      onClick={restoreAllHidden}
                      size="xs"
                    >
                      Restore All Hidden ({hiddenLogs.size})
                    </Button>
                  </Tooltip>
                )}
              </Group>
            </Group>

            <Tabs.Panel value="expanded" pt="md">
              <Stack gap="md">
                {filteredAndSortedAlerts.map((alert: any) => (
                  <Card
                    key={alert.id}
                    shadow="sm"
                    padding="md"
                    withBorder
                    style={{
                      backgroundColor: selectedLogs.has(alert.id) ? 'var(--mantine-color-blue-light)' : undefined
                    }}
                  >
                    <Group justify="space-between" mb="sm">
                      <Group gap="sm">
                        <Tooltip label="Select this alert for bulk operations" styles={tooltipStyles}>
                          <Checkbox
                            checked={selectedLogs.has(alert.id)}
                            onChange={() => toggleLogSelection(alert.id)}
                          />
                        </Tooltip>
                        {getSeverityIcon(alert.severity)}
                        <Text fw={600} size="md">{alert.title}</Text>
                        <Tooltip label={`Priority: ${alert.priority} - ${alert.priority === 'high' ? 'Requires immediate attention' : alert.priority === 'medium' ? 'Should be addressed soon' : 'Can be handled when convenient'}`} styles={tooltipStyles}>
                          <Badge color={getPriorityColor(alert.priority)} size="sm" leftSection={getPriorityIcon(alert.priority)}>
                            {alert.priority.toUpperCase()}
                          </Badge>
                        </Tooltip>
                        <Tooltip label={`Status: ${alert.status} - ${alert.status === 'Active' ? 'Needs attention' : alert.status === 'Acknowledged' ? 'Being worked on' : 'Issue resolved'}`} styles={tooltipStyles}>
                          <Badge color={alert.status === 'Active' ? 'red' : alert.status === 'Acknowledged' ? 'blue' : 'green'} size="sm">
                            {alert.status}
                          </Badge>
                        </Tooltip>
                      </Group>
                      <Group gap="xs">
                        {(alert.status === 'Active' || alert.status === 'Acknowledged') && (
                          <>
                            <Tooltip label="Acknowledge this alert and assign to yourself" styles={tooltipStyles}>
                              <ActionIcon variant="light" color="blue" size="sm" onClick={() => acknowledgeAlert(alert.id)}>
                                <IconCheck size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Mark this alert as resolved" styles={tooltipStyles}>
                              <ActionIcon variant="light" color="green" size="sm" onClick={() => resolveAlert(alert.id)}>
                                <IconX size={14} />
                              </ActionIcon>
                            </Tooltip>
                            <Tooltip label="Escalate to senior team with high priority" styles={tooltipStyles}>
                              <ActionIcon variant="light" color="orange" size="sm" onClick={() => escalateAlert(alert.id)}>
                                <IconArrowUp size={14} />
                              </ActionIcon>
                            </Tooltip>
                          </>
                        )}
                        <Tooltip label={expandedAlerts.has(alert.id) ? "Collapse alert details" : "Expand alert details"} styles={tooltipStyles}>
                          <ActionIcon variant="light" size="sm" onClick={() => toggleAlertExpansion(alert.id)}>
                            {expandedAlerts.has(alert.id) ? <IconChevronUp size={14} /> : <IconChevronDown size={14} />}
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Group>

                    <Group gap="md" mb="sm">
                      <Text size="sm" c="dimmed">
                        <strong>Source:</strong> {alert.source}
                      </Text>
                      <Text size="sm" c="dimmed">
                        <strong>Category:</strong> {alert.category}
                      </Text>
                      <Text size="sm" c="dimmed">
                        <strong>Assignee:</strong> {alert.assignee}
                      </Text>
                      <Text size="sm" c="dimmed">
                        <strong>Time:</strong> {alert.timestamp}
                      </Text>
                    </Group>

                    <Text size="sm" mb="sm">{alert.description}</Text>

                    <Collapse in={expandedAlerts.has(alert.id)}>
                      <Divider mb="sm" />
                      <Text size="sm" mb="sm">
                        <strong>Details:</strong> {alert.details}
                      </Text>
                      <Text size="sm" mb="sm">
                        <strong>Affected Systems:</strong>
                      </Text>
                      <Group gap="xs" mb="sm">
                        {alert.affectedSystems.map((system: string, index: number) => (
                          <Badge key={index} variant="light" size="sm">
                            {system}
                          </Badge>
                        ))}
                      </Group>
                    </Collapse>
                  </Card>
          ))}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="condensed" pt="md">
              <Table striped highlightOnHover fontSize="xs">
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th style={{ width: '40px', textAlign: 'center' }}>
                      <Tooltip label="Select/deselect all visible alerts" styles={tooltipStyles}>
                        <Checkbox
                          size="xs"
                          checked={selectedLogs.size === filteredAndSortedAlerts.length && filteredAndSortedAlerts.length > 0}
                          indeterminate={selectedLogs.size > 0 && selectedLogs.size < filteredAndSortedAlerts.length}
                          onChange={selectedLogs.size === filteredAndSortedAlerts.length ? clearSelection : selectAllVisibleLogs}
                        />
                      </Tooltip>
                    </Table.Th>
                    <Table.Th>Alert</Table.Th>
                    <Table.Th>Severity</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Source</Table.Th>
                    <Table.Th>Time</Table.Th>
                    <Table.Th style={{ width: '120px', textAlign: 'center' }}>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {filteredAndSortedAlerts.map((alert: any) => (
                    <Table.Tr
                      key={alert.id}
                      style={{
                        backgroundColor: selectedLogs.has(alert.id) ? 'var(--mantine-color-blue-light)' : undefined
                      }}
                    >
                      <Table.Td style={{ textAlign: 'center' }}>
                        <Tooltip label="Select this alert for bulk operations" styles={tooltipStyles}>
                          <Checkbox
                            size="xs"
                            checked={selectedLogs.has(alert.id)}
                            onChange={() => toggleLogSelection(alert.id)}
                          />
                        </Tooltip>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Group gap="xs" align="center">
                            {getSeverityIcon(alert.severity)}
                            <Text size="xs" fw={500}>{alert.title}</Text>
                          </Group>
                          <Text size="xs" c="dimmed">{alert.description}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Badge
                          color={getSeverityColor(alert.severity)}
                          variant="light"
                          size="xs"
                        >
                          {alert.severity}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Badge
                          color={alert.status === 'Active' ? 'red' : alert.status === 'Acknowledged' ? 'blue' : 'green'}
                          variant="light"
                          size="xs"
                        >
                          {alert.status}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="xs">{alert.source}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="xs" c="dimmed">{alert.timestamp}</Text>
                      </Table.Td>
                      <Table.Td style={{ textAlign: 'center' }}>
                        <Group gap="xs" justify="center">
                          {(alert.status === 'Active' || alert.status === 'Acknowledged') && (
                            <>
                              <Tooltip label="Acknowledge this alert and assign to yourself" styles={tooltipStyles}>
                                <ActionIcon variant="light" color="blue" size="sm" onClick={() => acknowledgeAlert(alert.id)}>
                                  <IconCheck size={12} />
                                </ActionIcon>
                              </Tooltip>
                              <Tooltip label="Mark this alert as resolved" styles={tooltipStyles}>
                                <ActionIcon variant="light" color="green" size="sm" onClick={() => resolveAlert(alert.id)}>
                                  <IconX size={12} />
                                </ActionIcon>
                              </Tooltip>
                              <Tooltip label="Escalate to senior team with high priority" styles={tooltipStyles}>
                                <ActionIcon variant="light" color="orange" size="sm" onClick={() => escalateAlert(alert.id)}>
                                  <IconArrowUp size={12} />
                                </ActionIcon>
                              </Tooltip>
                            </>
                          )}
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </Tabs.Panel>
          </Tabs>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Alerts;
