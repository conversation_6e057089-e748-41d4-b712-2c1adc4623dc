import { useRef } from 'react';
import { Text, Card, Group, ActionIcon, Stack, Button, Select } from "@mantine/core";
import { IconEye, IconEyeOff, IconRestore, IconDeviceFloppy, IconTrash, IconPlus } from "@tabler/icons-react";

interface Widget {
  id: string;
  title: string;
}

interface LayoutPreset {
  id: string;
  name: string;
  layouts: { [key: string]: any[] };
}

interface WidgetMenuProps {
  // Widget management
  widgets: Widget[];
  visibleWidgets: Set<string>;
  onToggleWidgetVisibility: (widgetId: string) => void;
  
  // Layout management
  layoutPresets: LayoutPreset[];
  currentLayoutId: string;
  onLayoutChange: (layoutId: string) => void;
  onSaveLayout: (name: string) => void;
  onDeleteLayout: (layoutId: string) => void;
  onResetToDefault: () => void;
  
  // Menu positioning and control
  menuPosition: { x: number; y: number };
  isDragging: boolean;
  onMouseDown: (e: React.MouseEvent) => void;
  onClose: () => void;
}

const WidgetMenu: React.FC<WidgetMenuProps> = ({
  widgets,
  visibleWidgets,
  onToggleWidgetVisibility,
  layoutPresets,
  currentLayoutId,
  onLayoutChange,
  onSaveLayout,
  onDeleteLayout,
  onResetToDefault,
  menuPosition,
  isDragging,
  onMouseDown,
  onClose
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  const handleSaveLayout = () => {
    const name = prompt('Enter layout name:');
    if (name && name.trim()) {
      onSaveLayout(name.trim());
    }
  };

  const handleDeleteLayout = () => {
    if (currentLayoutId !== 'default' && confirm('Delete this layout?')) {
      onDeleteLayout(currentLayoutId);
    }
  };

  const currentLayout = layoutPresets.find(preset => preset.id === currentLayoutId);

  return (
    <>
      {/* Backdrop */}
      <div
        style={{
          position: 'fixed',
          inset: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.2)',
          backdropFilter: 'blur(1px)',
          zIndex: 40
        }}
        onClick={onClose}
      />

      {/* Draggable Floating Menu */}
      <div
        ref={menuRef}
        style={{
          position: 'fixed',
          left: `${menuPosition.x}px`,
          top: `${menuPosition.y}px`,
          zIndex: 50,
          cursor: isDragging ? 'grabbing' : 'grab',
          userSelect: 'none',
          pointerEvents: 'auto'
        }}
        onMouseDown={onMouseDown}
      >
        <Card
          shadow="lg"
          p="md"
          className="widget-menu-card"
          style={{
            backgroundColor: 'var(--mantine-color-body)',
            border: '1px solid var(--mantine-color-gray-3)',
            minWidth: '280px'
          }}
        >
          {/* Menu Header */}
          <div
            className="widget-menu-header"
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '16px',
              padding: '8px',
              borderRadius: '8px',
              backgroundColor: 'var(--mantine-color-gray-1)',
              pointerEvents: 'none'
            }}
          >
            <Text fw={600} size="sm">Widget Manager</Text>
            <Group gap="xs">
              <ActionIcon
                variant="subtle"
                color="blue"
                onClick={onResetToDefault}
                size="sm"
                title="Reset to Default Layout"
                style={{ pointerEvents: 'auto' }}
              >
                <IconRestore size={16} />
              </ActionIcon>
              <ActionIcon
                variant="subtle"
                color="gray"
                onClick={onClose}
                size="sm"
                style={{ pointerEvents: 'auto' }}
              >
                <IconEyeOff size={16} />
              </ActionIcon>
            </Group>
          </div>

          {/* Layout Management Section */}
          <div style={{ marginBottom: '16px', pointerEvents: 'auto' }}>
            <Text size="sm" fw={500} mb="xs">Layout Presets</Text>
            <Group gap="xs" mb="xs">
              <Select
                size="xs"
                value={currentLayoutId}
                onChange={(value) => value && onLayoutChange(value)}
                data={layoutPresets.map(preset => ({
                  value: preset.id,
                  label: preset.name
                }))}
                style={{ flex: 1 }}
              />
              <ActionIcon
                variant="subtle"
                color="green"
                onClick={handleSaveLayout}
                size="sm"
                title="Save Current Layout"
              >
                <IconDeviceFloppy size={14} />
              </ActionIcon>
              {currentLayoutId !== 'default' && (
                <ActionIcon
                  variant="subtle"
                  color="red"
                  onClick={handleDeleteLayout}
                  size="sm"
                  title="Delete Current Layout"
                >
                  <IconTrash size={14} />
                </ActionIcon>
              )}
            </Group>
            <Text size="xs" c="dimmed">
              Current: {currentLayout?.name || 'Unknown'}
            </Text>
          </div>

          {/* Widget List */}
          <Stack gap="xs" style={{ pointerEvents: 'auto' }}>
            {widgets.map((widget) => {
              const isVisible = visibleWidgets.has(widget.id);
              return (
                <Group key={widget.id} justify="space-between" align="center">
                  <Text size="sm" fw={500} className="widget-menu-text">{widget.title}</Text>
                  <Button
                    variant={isVisible ? "filled" : "outline"}
                    color={isVisible ? "blue" : "red"}
                    size="xs"
                    leftSection={isVisible ? <IconEye size={12} /> : <IconEyeOff size={12} />}
                    onClick={() => onToggleWidgetVisibility(widget.id)}
                    onMouseDown={(e) => e.stopPropagation()}
                    className={isVisible ? 'widget-menu-button-visible' : 'widget-menu-button-hidden'}
                  >
                    {isVisible ? 'Hide' : 'Show'}
                  </Button>
                </Group>
              );
            })}
          </Stack>
        </Card>
      </div>
    </>
  );
};

export default WidgetMenu;
