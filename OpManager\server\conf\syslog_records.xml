<?xml version="1.0" encoding="ISO-8859-1"?>
<syslog>
	<simulator_config iterations="-1"/>
	<!-- DB columns...  HOST_ID TIMESTAMP SEVERITY FACILITY SOURCE MESSAGE -->

<!-- parameters to replace dynamic value... ${count} and ${count} are predefined variables... -->

<!--param name="facility">0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23</param>
<param name="severity">0,1,2,3,4,5,6,7</param-->

<param name="process">syslogd,syslog,CROND,xinetd,ftpd,telnetd,lpd,sendmail,ntpd,gdm,atd,Font,xfs,gpm,httpd,mysql,apmd,dd,random,rpc,anacron,keytable,kernel,shutdown,init,rc,portmap,exiting,cpboot,gconfd,su,login,sshd,nfslock</param>
<!-- for severity value of 0 -->
<param name="facility">0,8,16,24,32,40,48,56,64,72,80,88,96,104,112,120,128,136,144,152,160,168,176,184</param>
<!-- for severity value of 1 -->
<param name="facility">1,9,17,25,33,41,49,57,65,73,81,89,97,105,113,121,129,137,145,153,161,169,177,185</param>
<!-- for severity value of 2 -->
<param name="facility">2,10,18,26,34,42,50,58,66,74,82,90,98,106,114,122,130,138,146,154,162,170,178,186</param>
<!-- for severity value of 3 -->
<param name="facility">3,11,19,27,35,43,51,59,67,75,83,91,99,107,115,123,131,139,147,155,163,171,179,187</param>
<!-- for severity value of 4 -->
<param name="facility">4,12,20,28,36,44,52,60,68,76,84,92,100,108,116,124,132,140,148,156,164,172,180,188</param>
<!-- for severity value of 5 -->
<param name="facility">5,13,21,29,37,45,53,61,69,77,85,93,101,109,117,125,133,141,149,157,165,173,181,189</param>
<!-- for severity value of 6 -->
<param name="facility">6,14,22,30,38,46,54,62,70,78,86,94,102,110,118,126,134,142,150,158,166,174,182,190</param>
<!-- for severity value of 7 -->
<param name="facility">7,15,23,31,39,47,55,63,71,79,87,95,103,111,119,127,135,143,151,159,167,175,183,191</param> 
<!-- total number of records is [(number of params)*(number of packets)*(number of iterations)] -->

<!-- linux event log format...  <78>CROND[28460]:(mailman)CMD(/usr/bin/python-S/var/mailman/cron/qrunner) -->
<packet id = "traffic" recordspersec = "10" key_val_separator="none" field_separator="none" display_key="none">	
		<tag>&lt;</tag>
		<facility>${facility}</facility>
		<!--facility>((${facility}*8)+${severity})</facility-->
		<ctag>&gt;</ctag>
		<process>${process}[${count}]:</process>
		<msg>my message...${count}</msg>
	</packet>
</syslog>
