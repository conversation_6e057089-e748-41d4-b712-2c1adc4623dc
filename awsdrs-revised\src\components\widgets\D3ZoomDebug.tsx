import React, { useEffect, useRef } from 'react';
import { Card, Text } from '@mantine/core';
import * as d3 from 'd3';

interface DataPoint {
  name: string;
  time: string;
  value: number;
  cpu?: number;
  memory?: number;
  disk?: number;
}

interface D3ZoomDebugProps {
  title: string;
  data: DataPoint[];
  height?: number;
}

const D3ZoomDebug: React.FC<D3ZoomDebugProps> = ({ title, data, height = 400 }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current || !data.length) return;

    // Clear previous chart
    d3.select(containerRef.current).selectAll('*').remove();

    const margin = { top: 20, right: 80, bottom: 40, left: 60 };
    const width = (containerRef.current?.clientWidth || 800) - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom - 50;

    // Process data
    let currentData = data.map((d, i) => ({
      ...d,
      index: i,
      timeLabel: d.time,
    }));

    console.log('Initial processedData:', currentData.slice(0, 5));

    // Create SVG
    const svg = d3.select(containerRef.current)
      .append('svg')
      .attr('width', width + margin.left + margin.right)
      .attr('height', height)
      .style('background', 'var(--mantine-color-body)');

    const g = svg.append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Scales
    let currentXScale = d3.scaleLinear()
      .domain([0, currentData.length - 1])
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(currentData, d => d.value) || 100])
      .range([chartHeight, 0]);

    // Line generator
    const line = d3.line<any>()
      .x((d, i) => currentXScale(i))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Add axes
    g.append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0,${chartHeight})`)
      .call(d3.axisBottom(currentXScale)
        .tickFormat((d) => currentData[d as number]?.timeLabel || '')
        .ticks(Math.min(10, currentData.length))
      );

    g.append('g')
      .attr('class', 'y-axis')
      .call(d3.axisLeft(yScale));

    // Add main line
    g.append('path')
      .attr('class', 'main-line')
      .datum(currentData)
      .attr('fill', 'none')
      .attr('stroke', '#3b82f6')
      .attr('stroke-width', 3)
      .attr('d', line);

    // Add dots
    g.selectAll('.dot')
      .data(currentData)
      .enter()
      .append('circle')
      .attr('class', 'dot')
      .attr('r', 4)
      .attr('fill', '#3b82f6')
      .attr('stroke', '#ffffff')
      .attr('stroke-width', 2)
      .attr('cx', (d, i) => currentXScale(i))
      .attr('cy', d => yScale(d.value));

    // Add critical points (red diamonds)
    const anomalies = currentData.filter(d => d.value > 85);
    anomalies.forEach(d => {
      g.append('path')
        .attr('class', 'diamond')
        .attr('d', `M ${currentXScale(d.index)} ${yScale(d.value) - 8}
                   L ${currentXScale(d.index) + 6} ${yScale(d.value)}
                   L ${currentXScale(d.index)} ${yScale(d.value) + 8}
                   L ${currentXScale(d.index) - 6} ${yScale(d.value)} Z`)
        .attr('fill', '#ef4444')
        .attr('stroke', '#ffffff')
        .attr('stroke-width', 2);
    });

    // Add brush for zoom
    const brush = d3.brushX()
      .extent([[0, 0], [width, chartHeight]])
      .on('end', (event) => {
        if (!event.selection) return;

        const [x0, x1] = event.selection;
        const startIndex = Math.max(0, Math.round(currentXScale.invert(x0)));
        const endIndex = Math.min(currentData.length - 1, Math.round(currentXScale.invert(x1)));

        console.log('=== ZOOM DEBUG ===');
        console.log('Selection pixels:', { x0, x1 });
        console.log('Converted indices:', { startIndex, endIndex });
        console.log('Current data length:', currentData.length);

        if (startIndex >= endIndex) return;

        // Create zoomed data with new indices
        const zoomedData = currentData.slice(startIndex, endIndex + 1).map((d, i) => ({
          ...d,
          index: i,
          originalIndex: d.index
        }));

        console.log('Zoomed data length:', zoomedData.length);
        console.log('First 3 zoomed items:', zoomedData.slice(0, 3));
        console.log('Time labels:', zoomedData.map(d => d.timeLabel));

        // New scale
        const newXScale = d3.scaleLinear()
          .domain([0, zoomedData.length - 1])
          .range([0, width]);

        // Update axis with explicit tick values
        const tickValues = zoomedData.length <= 10
          ? d3.range(0, zoomedData.length) // Show all ticks if 10 or fewer
          : d3.range(0, zoomedData.length, Math.ceil(zoomedData.length / 8)); // Show ~8 evenly spaced ticks

        console.log('Tick values to show:', tickValues);

        g.select('.x-axis')
          .transition()
          .duration(750)
          .call(d3.axisBottom(newXScale)
            .tickValues(tickValues)
            .tickFormat((d) => {
              const index = Math.round(d as number);
              const label = zoomedData[index]?.timeLabel || '';
              console.log(`Tick ${d} -> index ${index} -> label "${label}"`);
              return label;
            })
          );

        // Update line
        const newLine = d3.line<any>()
          .x((d, i) => newXScale(i))
          .y(d => yScale(d.value))
          .curve(d3.curveMonotoneX);

        g.select('.main-line')
          .datum(zoomedData)
          .transition()
          .duration(750)
          .attr('d', newLine);

        // Update dots
        const dotsSelection = g.selectAll('.dot').data(zoomedData);
        dotsSelection.exit().remove();
        dotsSelection
          .transition()
          .duration(750)
          .attr('cx', (d, i) => newXScale(i))
          .attr('cy', d => yScale(d.value));

        // Update diamonds - ensure all old ones are removed
        g.selectAll('.diamond').remove();
        g.selectAll('path[fill="#ef4444"]').remove(); // Extra cleanup

        const zoomedAnomalies = zoomedData.filter(d => d.value > 85);
        console.log('Zoomed anomalies found:', zoomedAnomalies.length);

        zoomedAnomalies.forEach((d, i) => {
          g.append('path')
            .attr('class', 'diamond')
            .attr('d', `M ${newXScale(i)} ${yScale(d.value) - 8}
                       L ${newXScale(i) + 6} ${yScale(d.value)}
                       L ${newXScale(i)} ${yScale(d.value) + 8}
                       L ${newXScale(i) - 6} ${yScale(d.value)} Z`)
            .attr('fill', '#ef4444')
            .attr('stroke', '#ffffff')
            .attr('stroke-width', 2)
            .attr('opacity', 0)
            .transition()
            .duration(500)
            .attr('opacity', 1);
        });

        // Update current data and scale for next zoom
        currentData = zoomedData;
        currentXScale = newXScale;

        // Clear brush
        g.select('.brush').call(brush.move, null);
      });

    g.append('g')
      .attr('class', 'brush')
      .call(brush);

  }, [data, height]);

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Text size="lg" fw={700} mb="md">{title}</Text>
      <div ref={containerRef} style={{ width: '100%', height: `${height}px` }} />
      <Text size="sm" c="dimmed" mt="sm">
        Debug version with console logging. Check browser console for zoom details.
      </Text>
    </Card>
  );
};

export default D3ZoomDebug;
