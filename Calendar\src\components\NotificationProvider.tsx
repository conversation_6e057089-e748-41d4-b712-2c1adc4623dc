import { ReactNode, useCallback } from 'react';
import { Notifications as MantineNotifications, NotificationsProvider as MantineNotificationsProvider, showNotification } from '@mantine/notifications';
import { IconCheck, IconX, IconInfoCircle, IconAlertTriangle as Al<PERSON><PERSON>riangle } from '@tabler/icons-react';

type NotificationType = 'success' | 'error' | 'info' | 'warning';

interface ShowNotificationProps {
  title: string;
  message: string;
  type?: NotificationType;
  autoClose?: number | false;
}

// Export the notification function for use throughout the app
export const showAppNotification = ({
  title,
  message,
  type = 'info',
  autoClose = 5000,
}: ShowNotificationProps) => {
  const icons = {
    success: <IconCheck size={18} />,
    error: <IconX size={18} />,
    info: <IconInfoCircle size={18} />,
    warning: <AlertTriangle size={18} />,
  };

  const colors = {
    success: 'teal',
    error: 'red',
    info: 'blue',
    warning: 'yellow',
  } as const;

  showNotification({
    title,
    message,
    icon: icons[type],
    color: colors[type],
    autoClose: autoClose === false ? false : autoClose,
  });
};

// Hook to use the notification system
export const useNotifications = () => {
  const showSuccess = useCallback((message: string, title = 'Success') => {
    showAppNotification({ title, message, type: 'success' });
  }, []);

  const showError = useCallback((message: string, title = 'Error') => {
    showAppNotification({ title, message, type: 'error' });
  }, []);

  const showInfo = useCallback((message: string, title = 'Info') => {
    showAppNotification({ title, message, type: 'info' });
  }, []);

  const showWarning = useCallback((message: string, title = 'Warning') => {
    showAppNotification({ title, message, type: 'warning' });
  }, []);

  return {
    showSuccess,
    showError,
    showInfo,
    showWarning,
  };
};

// Provider component to wrap your app with
export const NotificationProvider = ({ children }: { children: ReactNode }) => {
  return (
    <MantineNotificationsProvider position="top-right" zIndex={1000}>
      <MantineNotifications />
      {children}
    </MantineNotificationsProvider>
  );
};

export default NotificationProvider;
