import { useState, useEffect, useCallback } from "react";
import { Text, Card, Group, Grid, Badge, Button, Progress, Stack } from "@mantine/core";
import { IconServer, IconClock, IconCheck, IconAlertTriangle } from "@tabler/icons-react";
import DashboardLayout from "../layouts/DashboardLayout";
import WidgetMenu from "../components/WidgetMenu";
import { useLayoutManager } from "../hooks/useLayoutManager";
import { Responsive, WidthProvider } from 'react-grid-layout';

const ResponsiveGridLayout = WidthProvider(Responsive);

// Define Recovery widgets
interface RecoveryWidget {
  id: string;
  title: string;
  type: 'status' | 'operations' | 'failover' | 'plans';
}

// Default widgets configuration
const defaultRecoveryWidgets: RecoveryWidget[] = [
  { id: 'recovery-status', title: 'Recovery Status', type: 'status' },
  { id: 'recovery-operations', title: 'Recovery Operations', type: 'operations' },
  { id: 'failover-management', title: 'Failover Management', type: 'failover' },
  { id: 'recovery-plans', title: 'Recovery Plans', type: 'plans' },
];

// Default layouts configuration
const defaultRecoveryLayouts = {
  lg: [
    { i: 'recovery-status', x: 0, y: 0, w: 6, h: 4 },
    { i: 'recovery-operations', x: 6, y: 0, w: 6, h: 4 },
    { i: 'failover-management', x: 0, y: 4, w: 12, h: 5 },
    { i: 'recovery-plans', x: 0, y: 9, w: 12, h: 4 },
  ],
  md: [
    { i: 'recovery-status', x: 0, y: 0, w: 6, h: 4 },
    { i: 'recovery-operations', x: 0, y: 4, w: 6, h: 4 },
    { i: 'failover-management', x: 0, y: 8, w: 6, h: 5 },
    { i: 'recovery-plans', x: 0, y: 13, w: 6, h: 4 },
  ],
  sm: [
    { i: 'recovery-status', x: 0, y: 0, w: 4, h: 4 },
    { i: 'recovery-operations', x: 0, y: 4, w: 4, h: 4 },
    { i: 'failover-management', x: 0, y: 8, w: 4, h: 5 },
    { i: 'recovery-plans', x: 0, y: 13, w: 4, h: 4 },
  ],
  xs: [
    { i: 'recovery-status', x: 0, y: 0, w: 2, h: 4 },
    { i: 'recovery-operations', x: 0, y: 4, w: 2, h: 4 },
    { i: 'failover-management', x: 0, y: 8, w: 2, h: 5 },
    { i: 'recovery-plans', x: 0, y: 13, w: 2, h: 4 },
  ]
};

// Helper function for initial menu position
const getInitialMenuPosition = () => ({
  x: window.innerWidth - 320,
  y: 100
});

// Widget factory function
const renderRecoveryWidget = (widget: RecoveryWidget) => {
  switch (widget.id) {
    case 'recovery-status':
      return (
        <Card shadow="sm" p="lg" h="100%">
          <Group justify="space-between" mb="md">
            <Text fw={600} size="lg">Recovery Status</Text>
            <Badge color="green" variant="light">Active</Badge>
          </Group>
          <Stack gap="md">
            <Group justify="space-between">
              <Text size="sm">RTO Target</Text>
              <Text size="sm" fw={600}>4 hours</Text>
            </Group>
            <Group justify="space-between">
              <Text size="sm">RPO Target</Text>
              <Text size="sm" fw={600}>15 minutes</Text>
            </Group>
            <Group justify="space-between">
              <Text size="sm">Last Test</Text>
              <Text size="sm" fw={600}>2 days ago</Text>
            </Group>
            <Progress value={95} color="green" size="lg" />
            <Text size="xs" c="dimmed">95% Recovery Readiness</Text>
          </Stack>
        </Card>
      );
    case 'recovery-operations':
      return (
        <Card shadow="sm" p="lg" h="100%">
          <Group justify="space-between" mb="md">
            <Text fw={600} size="lg">Recovery Operations</Text>
            <IconServer size={20} color="blue" />
          </Group>
          <Stack gap="sm">
            <Group justify="space-between">
              <Group gap="xs">
                <IconCheck size={16} color="green" />
                <Text size="sm">Production DB Backup</Text>
              </Group>
              <Badge color="green" size="sm">Complete</Badge>
            </Group>
            <Group justify="space-between">
              <Group gap="xs">
                <IconClock size={16} color="orange" />
                <Text size="sm">Web Server Sync</Text>
              </Group>
              <Badge color="orange" size="sm">In Progress</Badge>
            </Group>
            <Group justify="space-between">
              <Group gap="xs">
                <IconAlertTriangle size={16} color="red" />
                <Text size="sm">File Server Replication</Text>
              </Group>
              <Badge color="red" size="sm">Failed</Badge>
            </Group>
          </Stack>
        </Card>
      );
    case 'failover-management':
      return (
        <Card shadow="sm" p="lg" h="100%">
          <Group justify="space-between" mb="md">
            <Text fw={600} size="lg">Failover Management</Text>
            <Group>
              <Button variant="light" color="blue" size="sm">
                Test Failover
              </Button>
              <Button variant="filled" color="red" size="sm">
                Emergency Failover
              </Button>
            </Group>
          </Group>
          <Grid>
            <Grid.Col span={{ base: 12, md: 4 }}>
              <Card bg="blue.0" p="md" radius="sm">
                <Group justify="space-between" mb="xs">
                  <Text size="sm" fw={600}>Primary Site</Text>
                  <Badge color="green" size="sm">Online</Badge>
                </Group>
                <Text size="xs" c="dimmed">us-east-1 • 45 servers</Text>
                <Progress value={100} color="green" size="sm" mt="xs" />
              </Card>
            </Grid.Col>
            <Grid.Col span={{ base: 12, md: 4 }}>
              <Card bg="orange.0" p="md" radius="sm">
                <Group justify="space-between" mb="xs">
                  <Text size="sm" fw={600}>DR Site</Text>
                  <Badge color="orange" size="sm">Standby</Badge>
                </Group>
                <Text size="xs" c="dimmed">us-west-2 • 45 servers</Text>
                <Progress value={85} color="orange" size="sm" mt="xs" />
              </Card>
            </Grid.Col>
            <Grid.Col span={{ base: 12, md: 4 }}>
              <Card bg="gray.0" p="md" radius="sm">
                <Group justify="space-between" mb="xs">
                  <Text size="sm" fw={600}>Backup Site</Text>
                  <Badge color="gray" size="sm">Offline</Badge>
                </Group>
                <Text size="xs" c="dimmed">eu-west-1 • 30 servers</Text>
                <Progress value={0} color="gray" size="sm" mt="xs" />
              </Card>
            </Grid.Col>
          </Grid>
        </Card>
      );
    case 'recovery-plans':
      return (
        <Card shadow="sm" p="lg" h="100%">
          <Group justify="space-between" mb="md">
            <Text fw={600} size="lg">Recovery Plans</Text>
            <Button variant="light" size="sm">
              Create New Plan
            </Button>
          </Group>
          <Stack gap="sm">
            <Group justify="space-between" p="sm" style={{ border: '1px solid var(--mantine-color-gray-3)', borderRadius: '8px' }}>
              <div>
                <Text size="sm" fw={500}>Database Recovery Plan</Text>
                <Text size="xs" c="dimmed">Last updated: 3 days ago</Text>
              </div>
              <Badge color="green" size="sm">Active</Badge>
            </Group>
            <Group justify="space-between" p="sm" style={{ border: '1px solid var(--mantine-color-gray-3)', borderRadius: '8px' }}>
              <div>
                <Text size="sm" fw={500}>Application Recovery Plan</Text>
                <Text size="xs" c="dimmed">Last updated: 1 week ago</Text>
              </div>
              <Badge color="orange" size="sm">Needs Review</Badge>
            </Group>
            <Group justify="space-between" p="sm" style={{ border: '1px solid var(--mantine-color-gray-3)', borderRadius: '8px' }}>
              <div>
                <Text size="sm" fw={500}>Network Recovery Plan</Text>
                <Text size="xs" c="dimmed">Last updated: 2 weeks ago</Text>
              </div>
              <Badge color="red" size="sm">Outdated</Badge>
            </Group>
          </Stack>
        </Card>
      );
    default:
      return (
        <Card h="100%" p="md">
          <Text>Unknown widget: {widget.id}</Text>
        </Card>
      );
  }
};

// Main Recovery Component
const Recovery = () => {
  // Layout management with multiple presets
  const {
    layouts,
    layoutPresets,
    currentLayoutId,
    switchToLayout,
    saveCurrentLayout,
    deleteLayout,
    resetToDefault,
    handleLayoutChange
  } = useLayoutManager({
    pageKey: 'recovery',
    defaultLayouts: defaultRecoveryLayouts,
    defaultLayoutName: 'Default Recovery Layout'
  });

  // State management (standard for all pages)
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [widgets] = useState(defaultRecoveryWidgets);
  const [visibleWidgets, setVisibleWidgets] = useState(new Set(defaultRecoveryWidgets.map(w => w.id)));
  const [showWidgetMenu, setShowWidgetMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState(getInitialMenuPosition());
  const [isDragging, setIsDragging] = useState(false);

  // Auto-refresh data
  useEffect(() => {
    const interval = setInterval(() => {
      console.log('Auto-refreshing recovery data...');
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Load saved widget visibility
  useEffect(() => {
    try {
      const savedVisibility = localStorage.getItem('recovery-visible-widgets');
      if (savedVisibility) {
        setVisibleWidgets(new Set(JSON.parse(savedVisibility)));
      }
    } catch (error) {
      console.error('Error loading widget visibility:', error);
    }
  }, []);

  // Widget visibility toggle
  const toggleWidgetVisibility = (widgetId: string) => {
    setVisibleWidgets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(widgetId)) {
        newSet.delete(widgetId);
      } else {
        newSet.add(widgetId);
      }
      localStorage.setItem('recovery-visible-widgets', JSON.stringify([...newSet]));
      return newSet;
    });
  };

  const closeWidgetMenu = () => {
    setShowWidgetMenu(false);
  };

  const resetToDefaultAndClose = () => {
    resetToDefault();
    setShowWidgetMenu(false);
    setMenuPosition(getInitialMenuPosition());
  };

  // Optimized drag handlers for floating menu
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target !== e.currentTarget && !(e.target as Element).closest('.widget-menu-header')) return;

    setIsDragging(true);
    const startX = e.clientX - menuPosition.x;
    const startY = e.clientY - menuPosition.y;

    const handleMouseMove = (e: MouseEvent) => {
      const newX = Math.max(0, Math.min(window.innerWidth - 300, e.clientX - startX));
      const newY = Math.max(0, Math.min(window.innerHeight - 200, e.clientY - startY));

      setMenuPosition({ x: newX, y: newY });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };



  // Handle refresh
  const handleRefresh = useCallback(() => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1000);
  }, []);

  // Filter visible widgets
  const allWidgets = widgets.filter(widget => visibleWidgets.has(widget.id));

  // Dashboard controls
  const dashboardControls = {
    isEditing,
    loading,
    showWidgetMenu,
    onToggleEdit: () => setIsEditing(!isEditing),
    onRefresh: handleRefresh,
    onResetLayout: resetToDefaultAndClose,
    onToggleWidgetMenu: () => {
      if (!showWidgetMenu) {
        setMenuPosition(getInitialMenuPosition());
      }
      setShowWidgetMenu(!showWidgetMenu);
    }
  };

  return (
    <DashboardLayout dashboardControls={dashboardControls}>
      <div style={{ width: '100%', position: 'relative', maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ flex: 1, overflow: 'auto', paddingTop: '40px', padding: '40px 24px 24px 24px' }}>

          {/* Draggable Grid Layout */}
          <ResponsiveGridLayout
            className="layout"
            layouts={layouts}
            onLayoutChange={(layout, layouts) => {
              if (isEditing) {
                handleLayoutChange(layout, layouts);
              }
            }}
            isDraggable={isEditing}
            isResizable={isEditing}
            breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480 }}
            cols={{ lg: 12, md: 6, sm: 4, xs: 2 }}
            rowHeight={60}
            margin={[16, 16]}
            containerPadding={[0, 0]}
            useCSSTransforms={true}
            preventCollision={false}
            compactType="vertical"
          >
            {allWidgets.map((widget) => {
              return (
                <div key={widget.id} style={{ position: 'relative' }}>
                  {renderRecoveryWidget(widget)}
                </div>
              );
            })}
          </ResponsiveGridLayout>
        </div>

        {/* Floating Widget Selection Menu */}
        {showWidgetMenu && (
          <WidgetMenu
            widgets={widgets}
            visibleWidgets={visibleWidgets}
            onToggleWidgetVisibility={toggleWidgetVisibility}
            layoutPresets={layoutPresets}
            currentLayoutId={currentLayoutId}
            onLayoutChange={switchToLayout}
            onSaveLayout={saveCurrentLayout}
            onDeleteLayout={deleteLayout}
            onResetToDefault={resetToDefaultAndClose}
            menuPosition={menuPosition}
            isDragging={isDragging}
            onMouseDown={handleMouseDown}
            onClose={closeWidgetMenu}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default Recovery;
