{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconSquareRoundedLetterWFilled.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('filled', 'square-rounded-letter-w-filled', 'IconSquareRoundedLetterWFilled', [[\"path\",{\"d\":\"M11.676 2.001l.324 -.001c7.752 0 10 2.248 10 10l-.005 .642c-.126 7.235 -2.461 9.358 -9.995 9.358l-.642 -.005c-7.13 -.125 -9.295 -2.395 -9.358 -9.67v-.325c0 -7.643 2.185 -9.936 9.676 -9.999m2.332 5.875l-.52 4.153l-.56 -1.4c-.319 -.799 -1.41 -.837 -1.803 -.114l-.053 .114l-.561 1.4l-.519 -4.153a1 1 0 0 0 -1 -.876l-.116 .008a1 1 0 0 0 -.868 1.116l1 8c.128 1.025 1.537 1.207 1.92 .247l1.072 -2.678l1.072 2.678c.383 .96 1.792 .778 1.92 -.247l1 -8a1 1 0 0 0 -1.984 -.248\",\"key\":\"svg-0\"}]]);"], "mappings": ";;;;;AACA,IAAe,iCAAA,qBAAqB,UAAU,kCAAkC,kCAAkC,CAAC,CAAC,QAAO,EAAC,KAAI,qdAAod,OAAM,QAAO,CAAC,CAAC,CAAC;", "names": []}