@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #005499;
  --calendar-bg: #FFFFFF;
  --calendar-dark-bg: #1F2937;
  --header-bg: #FFFFFF;
  --text-color: #111827;
  --border-color: #E5E7EB;
}

@keyframes slide-in-from-top-2 {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-in {
  animation-duration: 200ms;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: forwards;
}

.fade-in {
  animation-name: fade-in;
}

.slide-in-from-top-2 {
  animation-name: slide-in-from-top-2;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f8f9fa;
  color: #111827;
}

/* Apply theme colors to all views */
.calendar-grid,
.day-view,
.week-view,
.year-view,
.calendar-container {
  background-color: var(--calendar-bg);
  color: var(--text-color);
}

.dark .calendar-grid,
.dark .day-view,
.dark .week-view,
.dark .year-view,
.dark .calendar-container {
  background-color: var(--calendar-dark-bg);
  color: var(--text-color);
}

/* Calendar Grid Specific Styles */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0px;
  background-color: color-mix(in srgb, var(--primary-color) 10%, transparent);
  overflow-y: auto;
  height: calc(100vh - 120px);
}

.calendar-day {
  background-color: var(--calendar-bg);
  min-height: 120px;
  padding: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  border: 1px solid color-mix(in srgb, var(--primary-color) 10%, transparent);
  border-right: none;
  border-bottom: none;
  outline: none;
  position: relative;
}

.calendar-day:nth-child(7n) {
  border-right: 1px solid color-mix(in srgb, var(--primary-color) 10%, transparent);
}

.calendar-day:nth-last-child(-n+7) {
  border-bottom: 1px solid color-mix(in srgb, var(--primary-color) 10%, transparent);
}
.calendar-day:hover {
  background-color: color-mix(in srgb, var(--primary-color) 5%, transparent);
  transform: scale(1.02);
  box-shadow: 0 4px 6px -1px color-mix(in srgb, var(--primary-color) 10%, transparent),
              0 2px 4px -1px color-mix(in srgb, var(--primary-color) 6%, transparent);
  z-index: 10;
  position: relative;
}

.dark .calendar-day {
  background-color: var(--calendar-dark-bg);
}

.calendar-day.today {
  background-color: color-mix(in srgb, var(--primary-color) 8%, transparent);
  border-top: 2px solid var(--primary-color);
}

.dark .calendar-day.today {
  background-color: color-mix(in srgb, var(--primary-color) 15%, transparent);
  border-top: 2px solid var(--primary-color);
}

.dark .calendar-day.today:hover {
  background-color: color-mix(in srgb, var(--primary-color) 20%, transparent);
}

.calendar-day.selected {
  background-color: color-mix(in srgb, var(--primary-color) 5%, transparent);
}

.dark .calendar-day.selected {
  background-color: color-mix(in srgb, var(--primary-color) 5%, transparent);
}

.calendar-day.out-of-month {
  background-color: var(--calendar-bg);
  opacity: 0.5;
}

.dark .calendar-day.out-of-month {
  background-color: var(--calendar-dark-bg);
  opacity: 0.5;
}

.calendar-header {
  background-color: var(--header-bg);
  padding: 8px;
  text-align: left;
  font-weight: 600;
  color: color-mix(in srgb, var(--primary-color) 40%, transparent);
}

.dark .calendar-header {
  background-color: var(--calendar-dark-bg);
  color: color-mix(in srgb, var(--primary-color) 60%, transparent);
}

/* Global text hover effects */
h1, h2, h3, h4, h5, h6, p, span, button, a, label {
  transition: filter 0.2s ease-in-out;
}

h1:hover, h2:hover, h3:hover, h4:hover, h5:hover, h6:hover, 
p:hover, span:hover, button:hover, a:hover, label:hover {
  filter: brightness(1.2);
}

/* Vertical brightness slider styles */
.brightness-slider {
  writing-mode: bt-lr;
  -webkit-appearance: slider-vertical;
  width: 8px;
  padding: 0 5px;
}

.brightness-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 8px;
  background: var(--primary-color);
  cursor: pointer;
  border-radius: 3px;
}

.brightness-slider::-moz-range-thumb {
  width: 16px;
  height: 8px;
  background: var(--primary-color);
  cursor: pointer;
  border-radius: 3px;
}

/* Force ViewSelector button to use header background */
/* Force ViewSelector styling */
.view-selector-button {
  background-color: transparent !important;
  border: none !important;
  color: white !important;
}

.view-selector-button:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

/* Force dropdown positioning */
[role="listbox"] {
  position: absolute !important;
  z-index: 60 !important;
  top: 100% !important;
  left: 0 !important;
  margin-top: 0.5rem !important;
}