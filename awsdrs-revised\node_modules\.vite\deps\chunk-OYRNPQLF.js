import {
  createReactComponent
} from "./chunk-5HMDTYKJ.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBinoculars.mjs
var IconBinoculars = createReactComponent("outline", "binoculars", "IconBinoculars", [["path", { "d": "M7 16m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-0" }], ["path", { "d": "M17 16m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-1" }], ["path", { "d": "M16.346 9.17l-.729 -1.261c-.16 -.248 -1.056 -.203 -1.117 .091l-.177 1.38", "key": "svg-2" }], ["path", { "d": "M19.761 14.813l-2.84 -5.133c-.189 -.31 -.592 -.68 -1.421 -.68c-.828 0 -1.5 .448 -1.5 1v6", "key": "svg-3" }], ["path", { "d": "M7.654 9.17l.729 -1.261c.16 -.249 1.056 -.203 1.117 .091l.177 1.38", "key": "svg-4" }], ["path", { "d": "M4.239 14.813l2.84 -5.133c.189 -.31 .592 -.68 1.421 -.68c.828 0 1.5 .448 1.5 1v6", "key": "svg-5" }], ["rect", { "width": "4", "height": "2", "x": "10", "y": "12", "key": "svg-6" }]]);

export {
  IconBinoculars
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBinoculars.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-OYRNPQLF.js.map
