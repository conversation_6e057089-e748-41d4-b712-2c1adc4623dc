<?xml version="1.0" encoding="UTF-8"?>
<!--$Id:$ -->
<DBINFO>

	<COUNTALONE  HEADING="count(*)" TABLENAMES="NetFlow_Interface,NetFlow_Router,IPGroups,AlertProfile,ScheduleDetails,BusinessView,NBAR_MetaTable,AppGroupInfo,Gmap_Locations,AAALogin" EXACTQRY="select count(*) from @{TABLE_NAME}"/>
	
	<METAQUERY HEADING="NFA_MetaTable"  EXACTQRY="select LOOKUP_TABLENAME from NFA_MetaTable where END_INDEX=-1  and TABLE_NAME NOT IN ('DstIf1Min','DstIfPkt1Min','SrcIf1Min','SrcIfPkt1Min') "/>
	<METAQUERY HEADING="RawMetaTable"  EXACTQRY="select LOOKUP_TABLENAME ,max(END_INDEX) from RawMetaTable group by DEVICE_ID,LOOKUP_TABLENAME"/>
	
	
	<QUERY HEADING="Managed interfaces" SUBHEADING="NetFlow InterFace Count" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from NetFlow_Interface where STATE = 0"/>
	<QUERY HEADING="Info obtained for sFlow Managed interfaces" SUBHEADING="sFlow Managed interfaces" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from NetFlow_Interface, NetFlow_Router where Flow_type = 1 and STATE = 0 and NetFlow_Router.Router_ID=NetFlow_Interface.Router_ID"/>
	<QUERY HEADING="Info obtained for Link Down alert configured interfaces" SUBHEADING="Link Down Alert Configured interfaces Count" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from NetFlow_Interface, NetFlow_Router where Flow_type = 1 and STATE = 0 and NetFlow_Router.Router_ID=NetFlow_Interface.Router_ID"/>
	<QUERY HEADING="Info obtained for Applications Associated with Ip" SUBHEADING="Applications Associated with Ip" COLUMNS="1" COLUMNNAMES="count(*)"  EXACTQRY="select count(*) from ApplicationNames where IP_ADDRESS NOT IN ('-1') OR IP_NETMASK NOT IN ('-1')"/>
	<QUERY HEADING="Info obtained for Alert for IPgroups" SUBHEADING="Alert for IPgroups" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from AlertProfile where DEVICE_TYPE_ID=11"/>
	<QUERY HEADING="Info obtained for DSCPGroupCount" SUBHEADING="Dscp Grouping Count" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from DscpGroupInfo"/>
	<QUERY HEADING="Info obtained for Application Group Count" SUBHEADING="Application Group Count" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from AppGroupInfo"/>
	<QUERY HEADING="Info obtained for Policy Applied Router Count" SUBHEADING="Policy Applied Router Count" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from NetFlow_Router where QOS_STATUS = 0"/>
	<QUERY HEADING="Info obtained for QoS Polling Interfaces Count" SUBHEADING="QoS Polling Interfaces Count" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from NetFlow_Interface where QOS_POLL_STATE = 0"/>
	<QUERY HEADING="Info obtained for Out Speed Applied Interfaces Count" SUBHEADING="Out Speed Applied Interfaces Count" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from NetFlow_Interface where SPEED != OUT_SPEED"/>
	<QUERY HEADING="Info obtained for ESP Excluded Interfaces Count" SUBHEADING="ESP Excluded Interfaces Count" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from FlowFilter where FILTER_ID = 1"/>
	<QUERY HEADING="Polling enabled interfaces" SUBHEADING="Polling enabled interfaces" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from NetFlow_Interface where POLL_STATE = 0"/>
	<QUERY HEADING="System Properties" SUBHEADING="System Properties" COLUMNS="2" COLUMNNAMES="PARAMETER,PARAMVALUE" EXACTQRY="select PARAMETER,PARAMVALUE from System_Properties"/>
	<QUERY HEADING="NFA_MetaTables" SUBHEADING="NFA_MetaTables" COLUMNS="1" COLUMNNAMES="LOOKUP_TABLENAME" EXACTQRY="select LOOKUP_TABLENAME from NFA_MetaTable"/>
	<QUERY HEADING="RawTables" SUBHEADING="RawTables" COLUMNS="1" COLUMNNAMES="LOOKUP_TABLENAME" EXACTQRY="select LOOKUP_TABLENAME from RawMetaTable"/>
	<QUERY HEADING="NBAR RawTables" SUBHEADING="NBAR RawTables" COLUMNS="1" COLUMNNAMES="LOOKUP_TABLENAME" EXACTQRY="select LOOKUP_TABLENAME from NBAR_MetaTable"/>
	<QUERY HEADING="Info obtained for Dscp count" SUBHEADING="Dscp  Count" COLUMNS="3" COLUMNNAMES="CODE_POINT,DSCP_NAME,STATE" EXACTQRY="select * from DscpMap where STATE = 0"/>
	<QUERY HEADING="Info obtained for One Minute files" SUBHEADING="NO. OF FILES" COLUMNS="2" COLUMNNAMES="interface_id,count(*)" EXACTQRY="select interface_id,count(*) as NoOfFiles from FileMetaTable group by interface_id"/>
	<QUERY HEADING="Info obtained for Output Suppression Applied Interfaces Count" SUBHEADING="Output Suppression Applied Interfaces Count" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from FlowFilter where FILTER_ID = 2"/>
	<QUERY HEADING="Info obtained for ACL Applied Interfaces Count" SUBHEADING="ACL Applied Interfaces Count" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from FlowFilter where FILTER_ID = 4"/>
	<QUERY HEADING="Info obtained for UserDefined DNS Count" SUBHEADING="UserDefined DNS Count" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from User_Defined_Names"/>
	<QUERY HEADING="Info obtained for Radius Authenticated User Count" SUBHEADING="Radius Authenticated User Count" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from AaaUserProperty where PROP_VALUE='radius'"/>
	<QUERY HEADING="Info obtained for BillPlan" SUBHEADING="Available Bill Plans Count" COLUMNS="1" COLUMNNAMES="count(*)" EXACTQRY="select count(*) from BillPlan"/>
	<QUERY HEADING="Info obtained for Probe Group" SUBHEADING="Probe Group" COLUMNS="4" COLUMNNAMES="PROBEGROUP_ID,PROBE_ID,TARGET_ID,OPERATION_ID" EXACTQRY="select * from ProbeGroup"/>
	<!-- EE Tables INFO -->
	<QUERY HEADING="Info obtained for Reporting_Server_Info" SUBHEADING="Reporting Server Info" COLUMNS="4" COLUMNNAMES="ID,SERVER_IP,HTTPS_PORT,FAIL_OVER_INFO" EXACTQRY="select * from Reporting_Server_Info"/>
	<QUERY HEADING="Info obtained for Collector_Properties" SUBHEADING="Collector Server Properties" COLUMNS="9" COLUMNNAMES="COLLECTOR_ID,UNIQUE_ID,COLLECTOR_NAME,STATE,ROUTER_START_ID,ROUTER_END_ID,IDGEN_START_ID,IDGEN_END_ID,USER_NAME" EXACTQRY="select COLLECTOR_ID,UNIQUE_ID,COLLECTOR_NAME,STATE,ROUTER_START_ID,ROUTER_END_ID,IDGEN_START_ID,IDGEN_END_ID,USER_NAME from Collector_Properties"/>
	<QUERY HEADING="Info obtained for Collector_Server_Info" SUBHEADING="Collector Server Info" COLUMNS="6" COLUMNNAMES="ID,COLLECTOR_ID,COLLECTOR_IP,IS_HTTP,WEB_PORT,FAIL_OVER_INFO," EXACTQRY="select * from Collector_Server_Info"/>
	<QUERY HEADING="Info obtained for NFAEE_Properties" SUBHEADING="NFAEE_Properties" COLUMNS="7" COLUMNNAMES="ID,LAST_CONF_ID,LAST_BUILD_NUMBER,FAILOVER_TIME,CS_TZONE,INTERFACE_COUNT,IPSLA_COUNT" EXACTQRY="select * from NFAEE_Properties"/>
	<QUERY HEADING="Info obtained for FailOver_Info" SUBHEADING="FailOver_Info" COLUMNS="7" COLUMNNAMES="ID,SERVER_IP,HTTP_PORT,HTTPS_PORT,MYSQL_PORT,FAIL_OVER_INFO,HEARTBIT_COUNT" EXACTQRY="select * from FailOver_Info"/>
	<QUERY HEADING="Info obtained for CollectorBackUpConfig" SUBHEADING="Collector Tables Max ID Details" COLUMNS="3" COLUMNNAMES="ID,TABLE_NAME,SENT_ID" EXACTQRY="select * from CollectorBackUpConfig"/>

</DBINFO>
