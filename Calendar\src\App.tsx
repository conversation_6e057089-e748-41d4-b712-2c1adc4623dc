import { useState } from 'react'
import { format } from 'date-fns'
import { Group, Button, ActionIcon, TextInput, Spotlight, Switch, Tooltip } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import { ChevronLeftIcon, ChevronRightIcon, SunIcon, MoonIcon } from '@heroicons/react/24/outline'
import { useCalendar } from './hooks/useCalendar'
import { useEvents } from './hooks/useEvents'
import { useTheme } from './hooks/useTheme'
import { CalendarHeader } from './components/CalendarHeader'
import { CalendarGrid } from './components/CalendarGrid'
import { DayView } from './components/DayView'
import { WeekView } from './components/WeekView'
import { YearView } from './components/YearView'
import { NewItemMenu } from './components/NewItemMenu'
import { ViewSelector } from './components/ViewSelector'
import { EventModal } from './components/EventModal'
import { CalendarEvent } from './types/calendar'

function App() {
  const [view, setView] = useState<'day' | 'workweek' | 'week' | 'month' | 'year'>('workweek')
  const [yearInput, setYearInput] = useState('')
  const [isEditingYear, setIsEditingYear] = useState(false)
  const calendar = useCalendar()
  const events = useEvents()
  const theme = useTheme()
  const [searchOpened, { open: openSearch, close: closeSearch }] = useDisclosure(false)
  const [showEventModal, setShowEventModal] = useState(false)
  const [selectedDateForEvent, setSelectedDateForEvent] = useState<Date | null>(null)
  const [editingEvent, setEditingEvent] = useState<CalendarEvent | null>(null)

  const handleYearKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const targetYear = parseInt(yearInput)
      if (!isNaN(targetYear) && targetYear >= 1900 && targetYear <= 2100) {
        calendar.setYear(targetYear)
        setIsEditingYear(false)
      }
    } else if (e.key === 'Escape') {
      setYearInput(calendar.currentDate.getFullYear().toString())
      setIsEditingYear(false)
    }
  }

  const handleYearClick = () => {
    setYearInput(calendar.currentDate.getFullYear().toString())
    setIsEditingYear(true)
  }

  const handleMenuItemClick = (type: string) => {
    setSelectedDateForEvent(new Date())
    setEditingEvent(null)
    setShowEventModal(true)
  }

  const handleEventClick = (event: CalendarEvent) => {
    setEditingEvent(event)
    setSelectedDateForEvent(null)
    setShowEventModal(true)
  }

  const handleDateDoubleClick = (date: Date) => {
    setSelectedDateForEvent(date)
    setEditingEvent(null)
    setShowEventModal(true)
  }

  const handleTimeSlotClick = (date: Date, hour: number) => {
    setSelectedDateForEvent(date)
    setEditingEvent(null)
    setShowEventModal(true)
  }

  const handleEventSave = (eventData: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingEvent) {
      events.updateEvent(editingEvent.id, eventData)
    } else {
      events.createEvent(eventData)
    }
    setShowEventModal(false)
    setEditingEvent(null)
    setSelectedDateForEvent(null)
  }

  const handleEventModalClose = () => {
    setShowEventModal(false)
    setEditingEvent(null)
    setSelectedDateForEvent(null)
  }

  const handleViewChange = (newView: 'day' | 'workweek' | 'week' | 'month' | 'year') => {
    setView(newView)
    calendar.setView(newView)
  }

  const renderCalendarView = () => {
    switch (view) {
      case 'day':
        return (
          <DayView
            currentDate={calendar.currentDate}
            selectedDate={calendar.selectedDate}
            onDayClick={calendar.handleDayClick}
            events={events.events}
            onEventClick={handleEventClick}
            onTimeSlotClick={handleTimeSlotClick}
          />
        )
      case 'workweek':
        return (
          <WeekView
            currentDate={calendar.currentDate}
            selectedDate={calendar.selectedDate}
            onDayClick={calendar.handleDayClick}
            events={events.events}
            onEventClick={handleEventClick}
            onTimeSlotClick={handleTimeSlotClick}
            isWorkWeek={true}
          />
        )
      case 'week':
        return (
          <WeekView
            currentDate={calendar.currentDate}
            selectedDate={calendar.selectedDate}
            onDayClick={calendar.handleDayClick}
            events={events.events}
            onEventClick={handleEventClick}
            onTimeSlotClick={handleTimeSlotClick}
            isWorkWeek={false}
          />
        )
      case 'year':
        return (
          <YearView
            currentDate={calendar.currentDate}
            selectedDate={calendar.selectedDate}
            onDayClick={calendar.handleDayClick}
            onViewChange={handleViewChange}
          />
        )
      default:
        return (
          <CalendarGrid
            days={calendar.days}
            currentDate={calendar.currentDate}
            selectedDate={calendar.selectedDate}
            onDayClick={calendar.handleDayClick}
            events={events.events}
            onEventClick={handleEventClick}
            onDateDoubleClick={handleDateDoubleClick}
          />
        )
    }
  }

  return (
    <div className={`h-screen flex flex-col calendar-container ${theme.isDarkMode ? 'dark' : ''}`}>
      {/* Top Navigation */}
      <div className="bg-[var(--primary-color)] text-white p-2 dark:bg-[#005499]">
        <Group justify="space-between" wrap="nowrap">
          <Group gap="sm">
            <ViewSelector view={view} onChange={handleViewChange} />
            <NewItemMenu
              onItemClick={handleMenuItemClick}
              onClose={() => {}}
            />
          </Group>
          
          {/* Calendar Navigation */}
          <Group gap="md">
            <ActionIcon
              onClick={calendar.prevMonth} 
              variant="subtle"
              color="white"
              size="sm"
            >
              <ChevronLeftIcon className="h-4 w-4 text-white" />
            </ActionIcon>
            {isEditingYear ? (
              <TextInput
                value={yearInput}
                onChange={(e) => setYearInput(e.target.value)}
                onKeyDown={handleYearKeyDown}
                onBlur={() => {
                  setYearInput(calendar.currentDate.getFullYear().toString())
                  setIsEditingYear(false)
                }}
                size="sm"
                w={120}
                styles={{
                  input: {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    textAlign: 'center',
                    fontWeight: 600,
                  }
                }}
              />
            ) : (
              <Button
                variant="subtle"
                color="white"
                size="sm"
                w={120}
                onClick={handleYearClick}
                styles={{
                  root: {
                    backgroundColor: 'transparent',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    }
                  }
                }}
              >
                {view === 'year' ? (
                  format(calendar.currentDate, 'yyyy')
                ) : (
                  format(calendar.currentDate, 'MMMM yyyy')
                )}
              </Button>
            )}
            <ActionIcon
              onClick={calendar.nextMonth} 
              variant="subtle"
              color="white"
              size="sm"
            >
              <ChevronRightIcon className="h-4 w-4 text-white" />
            </ActionIcon>
          </Group>
          
          <Group gap="sm">
            <TextInput
              placeholder="Search"
              size="sm"
              w={200}
              styles={{
                input: {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  '&::placeholder': {
                    color: 'rgba(255, 255, 255, 0.7)',
                  },
                  '&:focus': {
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  }
                }
              }}
            />
            <Tooltip label={theme.isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>
              <Switch
                checked={theme.isDarkMode}
                onChange={theme.toggleTheme}
                onLabel={<SunIcon className="h-4 w-4" />}
                offLabel={<MoonIcon className="h-4 w-4" />}
                size="md"
                color="blue"
                styles={{
                  track: {
                    backgroundColor: theme.isDarkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(255, 255, 255, 0.3)',
                    borderColor: 'rgba(255, 255, 255, 0.4)',
                  },
                  thumb: {
                    backgroundColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.4)',
                  }
                }}
              />
            </Tooltip>
          </Group>
        </Group>
      </div>

      {renderCalendarView()}

      {/* Event Modal */}
      <EventModal
        isOpen={showEventModal}
        onClose={handleEventModalClose}
        onSave={handleEventSave}
        event={editingEvent}
        selectedDate={selectedDateForEvent}
        categories={events.categories}
      />

      {/* Status Bar */}
      <div className="border-t px-4 py-1 text-sm text-[#4B5563] dark:text-[#D1D5DB] flex justify-between items-center bg-[var(--header-bg)] dark:bg-[#1F2937] dark:border-[#374151]">
        <div className="flex items-center space-x-4">
          <span>CHCIT</span>
        </div>
        <div className="flex items-center space-x-2">
          <button className="p-1 hover:bg-[#F3F4F6] dark:hover:bg-[#374151]/50 rounded transition-colors">-</button>
          <span>100%</span>
          <button className="p-1 hover:bg-[#F3F4F6] dark:hover:bg-[#374151]/50 rounded transition-colors">+</button>
        </div>
      </div>
    </div>
  )
}

export default App